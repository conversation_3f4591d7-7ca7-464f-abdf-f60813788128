import time
import subprocess
import pytest
import os
from fastapi.testclient import TestClient
from sqlalchemy import text, inspect as sa_inspect, create_engine
from db.db_connector import engine
from main import app

# 1. Raw SQL performance

def test_simple_query_performance(db_session):
    start = time.time()
    result = db_session.execute(text('SELECT 1'))
    result.fetchone()
    elapsed = time.time() - start
    assert elapsed < 0.5, f'Query took {elapsed:.2f}s'

# 2. Endpoint performance

@pytest.fixture(scope="session")
def auth_client():
    client = TestClient(app)
    payload = {
        "username": "perfuser",
        "password": "PerfPass123!",
        "confirm_password": "PerfPass123!",
        "role": "annotator",
        "full_name": "Performance User",
        "email": "<EMAIL>"
    }
    # Ensure user exists and is logged in
    client.post('/api/auth/register', json=payload)
    login_resp = client.post('/api/auth/login', data={"username": payload["username"], "password": payload["password"]})
    assert login_resp.status_code == 200, "<PERSON><PERSON> failed for performance tests"
    return client

@pytest.mark.parametrize("method,path,threshold", [
    ("GET", "/openapi.json", 0.5),
    ("GET", "/api/auth/me", 0.5),
])
def test_endpoint_response_time(auth_client, method, path, threshold):
    start = time.time()
    resp = getattr(auth_client, method.lower())(path)
    elapsed = time.time() - start
    assert elapsed < threshold, f"{method} {path} took {elapsed:.2f}s, exceeding {threshold}s"

# 3. Explain plan checks

def test_explain_plan_knowledge_entry_index(db_session):
    conn = engine.connect()
    plan = conn.execute(text("EXPLAIN QUERY PLAN SELECT * FROM knowledge_base WHERE id=1")).fetchall()
    conn.close()
    details = " ".join(str(row[3]) for row in plan)
    assert "USING INTEGER PRIMARY KEY" in details or "USING INDEX" in details


def test_explain_plan_user_email_index(db_session):
    conn = engine.connect()
    plan = conn.execute(text("EXPLAIN QUERY PLAN SELECT * FROM users WHERE email='<EMAIL>' ")).fetchall()
    conn.close()
    details = " ".join(str(row[3]) for row in plan)
    assert "USING INDEX" in details

# 4. Index performance benchmarking

def test_index_performance(db_session):
    # Prepare data: insert a safe row including all NOT NULL columns
    db_session.execute(
        text(
            "INSERT INTO users (username, full_name, email, password_hash, role, created_at, is_active, annotator_mode) "
            "VALUES (:u, :f, :e, :p, :r, CURRENT_TIMESTAMP, 1, :mode)"
        ), {"u": "perf", "f": "Perf User", "e": "<EMAIL>", "p": "h", "r": "annotator", "mode": "annotation"}
    )
    db_session.commit()
    conn = engine.connect()
    # Plan for indexed column
    plan_idx = conn.execute(text("EXPLAIN QUERY PLAN SELECT * FROM users WHERE username=:u"), {'u': 'perf'}).fetchall()
    # Plan for non-indexed column
    plan_scan = conn.execute(text("EXPLAIN QUERY PLAN SELECT * FROM users WHERE full_name=:f"), {'f': 'Perf User'}).fetchall()
    conn.close()
    plan_idx_str = ' '.join(str(r) for r in plan_idx).upper()
    plan_scan_str = ' '.join(str(r) for r in plan_scan).upper()
    assert 'USING INDEX' in plan_idx_str, f"Expected index usage, got {plan_idx}"
    assert 'SCAN' in plan_scan_str, f"Expected table scan, got {plan_scan}" 