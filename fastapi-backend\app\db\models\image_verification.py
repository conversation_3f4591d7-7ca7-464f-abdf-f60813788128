from sqlalchemy import Column, Integer, String, Text, TIMESTAMP, Boolean
from db.models import Base

class ImageVerification(Base):
    """Model for tracking image verification assignments and status."""
    __tablename__ = "image_verification"

    id = Column("Id", Integer, primary_key=True)
    annotator_username = Column("Annotator_Username", String)
    auditor_username = Column("Auditor_Username", String)
    dataset_name = Column("Dataset_Name", String, nullable=False)
    dataset_batch_name = Column("Dataset_Batch_Name", String, nullable=False)
    images = Column("Images", Text, nullable=False)
    label_file_path = Column("Label_File_Path", String, nullable=False)
    image_count = Column("Image_Count", Integer, nullable=False)
    assigned_at = Column("Assigned_At", TIMESTAMP)
    processed_at = Column("Processed_At", TIMESTAMP)
    verification_status = Column("Verification_Status", String, default='available')
    audit_status = Column("Audit_Status", Boolean, default=False)
    audited_at = Column("Audited_At", TIMESTAMP)
    audit_comments = Column("Audit_Comments", Text)
