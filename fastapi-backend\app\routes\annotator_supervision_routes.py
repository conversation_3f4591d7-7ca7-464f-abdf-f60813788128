from fastapi import APIRouter, Depends, Request, HTTPException, Form, File, UploadFile, Body
from fastapi.responses import JSONResponse, StreamingResponse, Response
from typing import List, Dict, Any, Optional
import os
from io import BytesIO
from datetime import datetime
import csv
import io
import logging
from werkzeug.utils import secure_filename

from cache.redis_connector import cache_set, cache_get
from utils.modules.document_processor import  process_queue, results_dict
from utils.modules.file_handler import delete_file
from utils.google_drive.google_sheets import save_document_data
from utils.modules.export import download_csv as export_download_csv
from core.config import settings
from db.models.user import UserRole
from dependencies.auth import get_current_active_user, require_annotator
from utils.google_drive.google_drive import GoogleDriveClient

logger = logging.getLogger('annotator_supervision_routes')

router = APIRouter(
    prefix="/supervision", 
    tags=["Supervision"],
    dependencies=[Depends(get_current_active_user), Depends(require_annotator(modes=["supervision"]))]
)

@router.post("/upload")
async def upload_documents(
    request: Request,
    document_type: str = Form(...),
    model_type: str = Form("standard"),
    files: List[UploadFile] = File(default=[]),
    drive_file_ids: Optional[str] = Form(None),
    drive_file_info: Optional[str] = Form(None),
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    if document_type not in ["passport", "check", "invoice"]:
        raise HTTPException(status_code=400, detail="Invalid document type")
    if model_type not in ["standard", "enhanced", "premium"]:
        model_type = "standard"

    # Clear any previous session data
    if "processing_files" in request.session:
        old_files = request.session["processing_files"]
        for old_file in old_files:
            old_id = old_file.get("id")
            if old_id and old_id in results_dict:
                del results_dict[old_id]
    
    request.session.pop("processing_files", None)
    request.session.pop("document_type", None)
    request.session.pop("model_type", None)
    request.session.pop("source", None)

    processed_files: List[Dict[str, Any]] = []

    # Handle Google Drive files
    if drive_file_ids and drive_file_info:
        import json
        file_ids = json.loads(drive_file_ids)
        file_info_dict = json.loads(drive_file_info)
        client = GoogleDriveClient(request)
        service = client.service()
        
        for file_id in file_ids:
            file_content = GoogleDriveClient.download_file(service, file_id)
            
            file_info = file_info_dict.get(file_id, {})
            file_name = file_info.get("name", f"drive_file_{file_id}")
            drive_link = file_info.get("webViewLink", "")
            
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            unique_filename = f"{timestamp}_{file_name}"
            image_id = unique_filename
            file_path = os.path.join(settings.upload_folder, unique_filename)
            
            file_data = file_content.getvalue()
            
            if len(file_data) < 1024 * 1024 and settings.cleanup_after_processing:
                results_dict[image_id] = {
                    "status": "processing",
                    "path": file_path,
                    "image_data": file_data,
                    "model_type": model_type,
                    "drive_link": drive_link,
                    "source": "drive"
                }
                file_buffer = BytesIO(file_data)
                process_queue.put((file_buffer, document_type, image_id, True, model_type))
            else:
                with open(file_path, "wb") as f:
                    f.write(file_data)
                results_dict[image_id] = {
                    "status": "processing",
                    "path": file_path,
                    "image_data": file_data,
                    "model_type": model_type,
                    "drive_link": drive_link,
                    "source": "drive"
                }
                process_queue.put((file_path, document_type, image_id, False, model_type))

            await cache_set(f"supervision:{image_id}:meta", {
                "status": "processing",
                "path": file_path,
                "model_type": model_type,
                "drive_link": drive_link,
                "source": "drive"
            })
            await cache_set(f"supervision:{image_id}:image_data", file_data)

            processed_files.append({
                "id": image_id,
                "name": file_name,
                "path": file_path,
                "model_type": model_type,
                "drive_link": drive_link,
                "source": "drive"
            })
                    
        source = "drive"
    
    # Handle local files
    elif files and files[0].filename != "":
        if len(files) > 10:
            raise HTTPException(status_code=400, detail="You can only upload up to 10 files at a time")

        for file in files:
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            unique_filename = f"{timestamp}_{filename}"
            image_id = unique_filename

            file_data = await file.read()
            file_size = len(file_data)
            file_path = os.path.join(settings.upload_folder, unique_filename)

            if file_size < 1024 * 1024 and settings.cleanup_after_processing:
                results_dict[image_id] = {
                    "status": "processing",
                    "path": file_path,
                    "image_data": file_data,
                    "model_type": model_type,
                    "source": "local"
                }
                file_buffer = BytesIO(file_data)
                process_queue.put((file_buffer, document_type, image_id, True, model_type))
            else:
                with open(file_path, "wb") as f:
                    f.write(file_data)
                results_dict[image_id] = {
                    "status": "processing",
                    "path": file_path,
                    "image_data": file_data,
                    "model_type": model_type,
                    "source": "local"
                }
                process_queue.put((file_path, document_type, image_id, False, model_type))

            await cache_set(f"supervision:{image_id}:meta", {
                "status": "processing",
                "path": file_path,
                "model_type": model_type,
                "source": "local"
            })
            await cache_set(f"supervision:{image_id}:image_data", file_data)

            processed_files.append({
                "id": image_id,
                "name": filename,
                "path": file_path,
                "model_type": model_type,
                "source": "local"
            })
        
        source = "local"
    else:
        raise HTTPException(status_code=400, detail="No files selected")

    if not processed_files:
        raise HTTPException(status_code=400, detail="No files could be processed")

    request.session["document_type"] = document_type
    request.session["model_type"] = model_type
    request.session["processing_files"] = processed_files
    request.session["source"] = source
    request.session["username"] = current_user.get("sub", "unknown_user")

    return JSONResponse(content={
        "success": True,
        "processed_files": processed_files,
        "source": source,
        "message": f"{len(processed_files)} files uploaded and being processed with {model_type} processing power"
    })

@router.get("/review")
async def review_documents(request: Request):
    """Review documents - unified endpoint for both local and Google Drive files"""
    session = request.session
    if "document_type" not in session or "processing_files" not in session:
        raise HTTPException(status_code=400, detail="No documents selected for processing")
    
    processing_files = session["processing_files"]
    document_type = session["document_type"]
    model_type = session.get("model_type", "standard")
    source = session.get("source", "local")
    
    files = []
    for file_info in processing_files:
        image_id = file_info["id"]
        
        status = "processing"
        fields = {}
        
        if image_id in results_dict:
            result = results_dict[image_id]
            status = result.get("status", "processing")
            
            if status == "completed":
                if "parsed_data" in result:
                    fields = result["parsed_data"]
                elif "data" in result:
                    raw_data = result["data"]
                    if isinstance(raw_data, str):
                        for line in raw_data.split('\n'):
                            if ':' in line:
                                key, value = line.split(':', 1)
                                fields[key.strip()] = value.strip()
                    elif isinstance(raw_data, dict):
                        fields = raw_data
        
        files.append({
            "id": image_id,
            "name": file_info["name"],
            "model_type": file_info.get("model_type", model_type),
            "status": status,
            "fields": fields,
            "imageUrl": f"/supervision/document-image/{image_id}",
            "source": source,
            "drive_link": file_info.get("drive_link", "") if source == "drive" else ""
        })
    
    return JSONResponse(content={
        "document_type": document_type,
        "model_type": model_type,
        "source": source,
        "files": files
    })

@router.get("/check-status/{image_id}")
async def check_status(image_id: str):
    if image_id in results_dict:
        result = results_dict[image_id]
        status = result.get("status")
        if status == "completed":
            extracted_data = {}
            
            if "parsed_data" in result:
                extracted_data = result["parsed_data"]
            elif "data" in result:
                raw_data = result.get("data", "")

                if isinstance(raw_data, str):
                    lines = raw_data.strip().split('\n')
                    for line in lines:
                        if ':' in line:
                            key, value = line.split(':', 1)
                            extracted_data[key.strip()] = value.strip()
                elif isinstance(raw_data, dict):
                    extracted_data = raw_data
                else:
                    str_data = str(raw_data)
                    lines = str_data.strip().split('\n')
                    for line in lines:
                        if ':' in line:
                            key, value = line.split(':', 1)
                            extracted_data[key.strip()] = value.strip()
            
            if not extracted_data:
                extracted_data = {"error": "No data extracted"}
            
            return {"status": "completed", "data": extracted_data}
            
        elif status == "error":
            error_message = result.get("error", "An unknown error occurred")
            return {"status": "error", "error": error_message}
        else:
            return {"status": status or "processing"}

@router.get("/document-image/{image_id}")
async def serve_document_image(image_id: str):
    """Serve document image for review interface"""
    image_data = None
    
    image_data = await cache_get(f"supervision:{image_id}:image_data")
    
    if not image_data and image_id in results_dict:
        result = results_dict[image_id]
        if "image_data" in result:
            image_data = result["image_data"]
        elif "path" in result and result["path"] and os.path.exists(result["path"]):
            with open(result["path"], "rb") as f:
                image_data = f.read()
    
    if not image_data:
        raise HTTPException(status_code=404, detail="Image not found")
    
    if not isinstance(image_data, bytes):
        if isinstance(image_data, str):
            image_data = image_data.encode('utf-8')
        else:
            raise HTTPException(status_code=500, detail="Invalid image data format")
    
    content_type = "image/jpeg"
    if len(image_data) >= 4:
        if image_data.startswith(b'\xff\xd8\xff'):
            content_type = "image/jpeg"
        elif image_data.startswith(b'\x89PNG'):
            content_type = "image/png"
        elif image_data.startswith(b'%PDF'):
            content_type = "application/pdf"
        elif image_data.startswith(b'GIF'):
            content_type = "image/gif"
        elif image_data.startswith(b'BM'):
            content_type = "image/bmp"
    
    return Response(
        content=image_data, 
        media_type=content_type,
        headers={
            "Cache-Control": "public, max-age=3600",
            "Content-Length": str(len(image_data))
        }
    )

@router.post("/save-document")
async def save_document(
    request: Request, 
    data: Dict[str, Any] = Body(...),
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    image_id = data.get("image_id")
    if not image_id or not data.get("data"):
        raise HTTPException(status_code=400, detail="Missing required data")

    if image_id not in results_dict:
        raise HTTPException(status_code=404, detail=f"Document {image_id} not found")

    document_type = request.session.get("document_type")
    if not document_type:
        raise HTTPException(status_code=400, detail="Document type not found in session")

    include_all_text = data.get("include_all_extracted_text", False)

    results_dict[image_id]["is_saved"] = True
    results_dict[image_id]["user_data"] = data.get("data", {})
    
    if "parsed_data" in results_dict[image_id]:
        results_dict[image_id]["parsed_data"].update(data.get("data", {}))

    if include_all_text and "txt_content" not in results_dict[image_id]:
        original_data = results_dict[image_id].get("data", {})
        if isinstance(original_data, dict):
            merged_data = {**original_data, **data.get("data", {})}
            txt_lines = [f"{key}: {value}" for key, value in merged_data.items() if value]
            results_dict[image_id]["txt_content"] = "\n".join(txt_lines)
            results_dict[image_id]["data"] = merged_data

    if settings.cleanup_after_processing and "path" in results_dict[image_id]:
        file_path = results_dict[image_id]["path"]
        if "image_data" not in results_dict[image_id] and os.path.exists(file_path):
            with open(file_path, "rb") as f:
                results_dict[image_id]["image_data"] = f.read()
            delete_file(file_path)
        elif os.path.exists(file_path):
            delete_file(file_path)

    sheets_success = True
    if data.get("verified", True):
        processing_files = request.session.get("processing_files", [])
        
        if processing_files and len(processing_files) > 0:
            current_file_info = None
            for file_info in processing_files:
                if file_info.get("id") == image_id:
                    current_file_info = file_info
                    break
            
            if current_file_info:
                filename = current_file_info.get("name", "unknown_file")
                folder_id = current_file_info.get("folder_id", None)
            else:
                filename = processing_files[0].get("name", "unknown_file")
                folder_id = processing_files[0].get("folder_id", None)
        
        model_type = request.session.get("model_type", "standard")
        username = current_user.get("sub") or request.session.get("username", "unknown_user")

        file_path_or_drive_link = ""
        if image_id in results_dict:
            result = results_dict[image_id]
            source = result.get("source", "local")
            if source == "drive":
                file_path_or_drive_link = result.get("drive_link", "") or result.get("path", "")
            else:
                file_path_or_drive_link = result.get("path", "")
        
        save_success = save_document_data(
            username,
            filename,
            file_path_or_drive_link,
            model_type,
            folder_id,
            document_type,
            data.get("data"),
            data.get("corrections")
        )
        
        if not save_success:
            sheets_success = False

    response_data = {
        "success": True,
        "sheets_saved": sheets_success
    }
    
    if not sheets_success:
        response_data["warning"] = "Document saved locally but Google Sheets sync failed"
    
    return response_data

@router.get("/download_csv/{image_id}")
async def download_csv_file(image_id: str, request: Request):
    if image_id not in results_dict or results_dict[image_id].get("status") != "completed":
        raise HTTPException(status_code=404, detail="No processed results found for this file")
    result = results_dict[image_id]
    document_type = request.session.get("document_type", "document")
    filename = os.path.basename(result.get("path", ""))

    extraction_data: Dict[str, Any] = {}
    raw_data = result.get("data", {})
    if isinstance(raw_data, str):
        for line in raw_data.split("\n"):
            if ":" in line:
                key, value = line.split(":", 1)
                extraction_data[key.strip()] = value.strip()
    elif isinstance(raw_data, dict):
        extraction_data = {k: v for k, v in raw_data.items()}
    else:
        extraction_data = {"Raw Data": str(raw_data)}

    drive_link = result.get("drive_link", "")
    if drive_link:
        extraction_data["Link to File"] = drive_link

    all_results = [{
        "filename": filename,
        "extraction_data": extraction_data,
        "drive_link": drive_link
    }]

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"{document_type}_{filename}_{timestamp}.csv"

    return export_download_csv(all_results, document_type, output_filename)

@router.get("/download_txt/{image_id}")
async def download_txt_file(image_id: str, request: Request):
    if image_id not in results_dict or results_dict[image_id].get("status") != "completed":
        raise HTTPException(status_code=404, detail="No processed results found for this file")
    result = results_dict[image_id]
    document_type = request.session.get("document_type", "document")
    filename = os.path.basename(result.get("path", ""))
    txt_content = result.get("txt_content", result.get("data", ""))

    mem_file = BytesIO(txt_content.encode("utf-8"))
    mem_file.seek(0)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    headers = {
        "Content-Disposition": f'attachment; filename="{document_type}_{filename}_{timestamp}.txt"'
    }
    return StreamingResponse(mem_file, media_type="text/plain", headers=headers)

@router.post("/list-drive-folders")
async def list_folders(request: Request, body: Dict[str, Any] = Body(...)):
    parent_folder_id = body.get("folder_id")
    client = GoogleDriveClient(request)
    svc = client.service()
    breadcrumb = client.get_folder_hierarchy(svc, parent_folder_id) if parent_folder_id else [{"id": "", "name": "Root"}]
    contents = client.list_folder_contents(svc, parent_folder_id)
    
    folders = [item for item in contents if item.get("type") == "folder"]
    files = [item for item in contents if item.get("type") != "folder"]
    folders.sort(key=lambda x: x.get("name", "").lower())
    files.sort(key=lambda x: x.get("name", "").lower())

    return {"success": True, "folders": folders, "files": files, "breadcrumb": breadcrumb, "current_folder_id": parent_folder_id}

@router.post("/download_csv")
async def download_csv_route(body: Dict[str, Any] = Body(...)):
    edited_data = body.get("edited_data")
    filename = body.get("filename", "data.csv")

    if not edited_data:
        raise HTTPException(status_code=400, detail="No edited data provided")

    field_names = list(edited_data.keys())
    output = io.StringIO()
    writer = csv.writer(output, quoting=csv.QUOTE_MINIMAL)
    writer.writerow(field_names)
    writer.writerow([edited_data.get(field, "") for field in field_names])

    mem = BytesIO(output.getvalue().encode("utf-8"))
    mem.seek(0)
    headers = {
        "Content-Disposition": f'attachment; filename="{filename}"',
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0"
    }
    return StreamingResponse(mem, media_type="text/csv", headers=headers)

 
