import pytest
from main import app
from fastapi.testclient import Test<PERSON>lient
from db import db_connector
from db.models.pdf_extractor import Document
from utils.modules.document_processor import results_dict

@pytest.fixture(scope='function')
def supervision_client(db_session):
    with TestClient(app) as client:
        # First create an admin user to add the supervision user
        admin_payload = {"username": "admin", "password": "Admin123!", "confirm_password": "Admin123!", "role": "admin", "full_name": "Admin User", "email": "<EMAIL>"}
        admin_reg_resp = client.post('/api/auth/register', json=admin_payload)
        assert admin_reg_resp.status_code == 200, f"Admin registration failed: {admin_reg_resp.text}"
        admin_login_resp = client.post('/api/auth/login', data={"username": "admin", "password": "Admin123!"})
        assert admin_login_resp.status_code == 200, f"Admin login failed: {admin_login_resp.text}"
        
        # Now create supervision user via admin endpoint
        supervision_payload = {
            "username": "supuser",
            "password": "Suppass123!",
            "role": "annotator",
            "full_name": "Sup User",
            "email": "<EMAIL>",
            "annotation_mode": "supervision"
        }
        add_user_resp = client.post('/api/admin/add-user', json=supervision_payload)
        assert add_user_resp.status_code == 200, f"Add supervision user failed: {add_user_resp.text}"
        
        # Login as supervision user
        login_resp = client.post('/api/auth/login', data={"username": "supuser", "password": "Suppass123!"})
        assert login_resp.status_code == 200, f"Supervision user login failed: {login_resp.text}"
        yield client

@pytest.fixture(scope='function')
def annotator_client_no_supervision(db_session):
    """Authenticated as annotator with default mode for supervision RBAC testing"""
    with TestClient(app) as client:
        payload = {"username": "anno_sup", "password": "AnnSup123!", "confirm_password": "AnnSup123!", "role": "annotator", "full_name": "Anno Sup", "email": "<EMAIL>"}
        client.post('/api/auth/register', json=payload)
        resp = client.post('/api/auth/login', data={"username": payload["username"], "password": payload["password"]})
        assert resp.status_code == 200
        yield client

def test_upload_review(supervision_client):
    resp = supervision_client.post('/api/supervision/upload', data={"document_type": "passport", "model_type": "standard"})
    assert resp.status_code in (200, 400, 403)
    if resp.status_code == 200:
        db = db_connector.SessionLocal()
        doc = db.query(Document).order_by(Document.id.desc()).first()
        assert doc is not None
        db.close()
    resp = supervision_client.get('/api/supervision/review')
    assert resp.status_code in (200, 400, 403)

def test_document_type_boundaries(supervision_client):
    resp = supervision_client.post('/api/supervision/upload', data={"document_type": "passport", "model_type": "standard"})
    assert resp.status_code in (200, 400, 403)
    resp = supervision_client.post('/api/supervision/upload', data={"document_type": "invalidtype", "model_type": "standard"})
    assert resp.status_code in (400, 403)
    resp = supervision_client.post('/api/supervision/upload', data={"document_type": "passport", "model_type": "enhanced"})
    assert resp.status_code in (200, 400, 403)
    resp = supervision_client.post('/api/supervision/upload', data={"document_type": "passport", "model_type": "badmodel"})
    assert resp.status_code in (200, 400, 403)

def test_bulk_export_download(supervision_client):
    payload = {"edited_data": {"field": "value"}, "filename": "bulk.csv"}
    resp = supervision_client.post('/api/supervision/download_csv', json=payload)
    assert resp.status_code in (200, 400, 403)
    resp = supervision_client.get('/api/supervision/download_csv/9999')
    assert resp.status_code in (200, 404, 500, 403)
    resp = supervision_client.get('/api/supervision/download_txt/9999')
    assert resp.status_code in (200, 404, 500, 403)

def test_list_drive_folders(supervision_client):
    # List drive folders with and without folder_id
    resp1 = supervision_client.post('/api/supervision/list-drive-folders', json={})
    assert resp1.status_code in (200, 400, 403, 500)
    resp2 = supervision_client.post('/api/supervision/list-drive-folders', json={'folder_id': 'fake'})
    assert resp2.status_code in (200, 400, 403, 500)

def test_check_status_and_document_image(supervision_client):
    # Check processing status for a nonexistent image_id
    resp_status = supervision_client.get('/api/supervision/check-status/12345')
    assert resp_status.status_code in (200, 404, 500, 403)
    # View document image for review for nonexistent image_id
    resp_image = supervision_client.get('/api/supervision/document-image/12345')
    assert resp_image.status_code in (200, 404, 500, 403)

def test_save_document(supervision_client):
    # Save extracted data to Google Sheets
    payload = {
        'image_id': 12345,
        'data': {'field': 'value'},
        'include_all_extracted_text': True,
        'verified': True,
        'corrections': {}
    }
    resp = supervision_client.post('/api/supervision/save-document', json=payload)
    assert resp.status_code in (200, 400, 403, 404, 500)

def test_unauthorized_supervision_endpoints():
    """Supervision endpoints should return 401 when unauthenticated."""
    client = TestClient(app)
    endpoints = [
        ('post', '/api/supervision/upload', {'data': {'document_type': 'passport', 'model_type': 'standard'}}),
        ('get', '/api/supervision/review', {}),
        ('post', '/api/supervision/list-drive-folders', {'json': {}}),
        ('post', '/api/supervision/download_csv', {'json': {'edited_data': [], 'filename': 'f.csv'}}),
        ('get', '/api/supervision/download_csv/1', {}),
        ('get', '/api/supervision/download_txt/1', {}),
    ]
    for method, path, kwargs in endpoints:
        resp = getattr(client, method)(path, **kwargs)
        assert resp.status_code == 401, f"Unauthorized {path} returned {resp.status_code}"

def test_review_response_shape(supervision_client):
    """GET /api/supervision/review returns a list of file records with expected fields."""
    resp = supervision_client.get('/api/supervision/review')
    if resp.status_code != 200:
        pytest.skip(f"Skipping shape test, status {resp.status_code}")
    data = resp.json()
    assert isinstance(data, list)
    for item in data:
        # Each item should include these keys
        assert set(item.keys()) >= {"id", "name", "path", "model_type", "source"}

def test_list_drive_folders_invalid_folder_type(supervision_client):
    """Invalid folder_id type should return 400 Bad Request or gracefully handle with 200."""
    # folder_id must be a string
    resp = supervision_client.post('/api/supervision/list-drive-folders', json={'folder_id': 123})
    assert resp.status_code in (200, 400)  # Allow both graceful handling and strict validation

def test_bulk_export_csv_headers(supervision_client):
    """POST /api/supervision/download_csv returns proper CSV headers and content-type."""
    payload = {"edited_data": {"f1": "v1"}, "filename": "test.csv"}
    resp = supervision_client.post('/api/supervision/download_csv', json=payload)
    if resp.status_code != 200:
        pytest.skip(f"Skipping header test, status {resp.status_code}")
    # Content-Type must be CSV
    content_type = resp.headers.get('content-type', '')
    assert 'text/csv' in content_type
    # Content-Disposition must include attachment and filename
    cd = resp.headers.get('content-disposition', '')
    assert 'attachment' in cd.lower()
    assert 'test.csv' in cd

def test_supervision_rbac_for_all_endpoints(annotator_client_no_supervision):
    """All supervision endpoints should require supervision annotator mode: unauthenticated->401, wrong-mode->403"""
    client_no_auth = TestClient(app)
    endpoints = [
        ('post', '/api/supervision/upload', {'data': {'document_type': 'passport', 'model_type': 'standard'}}),
        ('get', '/api/supervision/review', {}),
        ('post', '/api/supervision/list-drive-folders', {'json': {}}),
        ('post', '/api/supervision/download_csv', {'json': {'edited_data': [], 'filename': 'f.csv'}}),
        ('get', '/api/supervision/download_csv/1', {}),
        ('get', '/api/supervision/download_txt/1', {})
    ]
    for method, path, kwargs in endpoints:
        resp_unauth = getattr(client_no_auth, method)(path, **kwargs)
        assert resp_unauth.status_code == 401, f"Unauth {path} returned {resp_unauth.status_code}"
        resp_forbid = getattr(annotator_client_no_supervision, method)(path, **kwargs)
        assert resp_forbid.status_code == 403, f"Forbidden {path} returned {resp_forbid.status_code}"

@pytest.mark.parametrize("endpoint,data_key,media_type,disp_type", [
    ("download_csv", 'data', 'text/csv', 'attachment'),
    ("download_txt", 'txt_content', 'text/plain', 'attachment')
])
def test_download_by_id_success(supervision_client, endpoint, data_key, media_type, disp_type):
    """GET /api/supervision/{endpoint}/{id} should return 200 with correct media type and content"""
    demo_id = 'test123'
    # Inject a completed result
    results_dict[demo_id] = {
        'status': 'completed',
        'path': 'file.csv',
        'data': {'col': 'val'},
        'txt_content': 'line1\nline2'
    }
    resp = supervision_client.get(f'/api/supervision/{endpoint}/{demo_id}')
    assert resp.status_code == 200
    assert media_type in resp.headers.get('content-type', '')
    cd = resp.headers.get('content-disposition', '')
    assert disp_type in cd
    text = resp.text
    # Check that injected data appears in response
    if endpoint == "download_csv":
        assert 'col' in text and 'val' in text
    else:
        assert 'line1' in text and 'line2' in text
    # Clean up
    del results_dict[demo_id]

def test_download_csv_midrange_streaming(supervision_client):
    """Simulate a mid-range CSV export with many columns to test streaming"""
    demo_id = 'midcsv'
    # Inject mid-range data with 50 columns
    mid_data = {f'col{i}': f'val{i}' for i in range(50)}
    results_dict[demo_id] = {
        'status': 'completed',
        'path': 'mid.csv',
        'data': mid_data
    }
    resp = supervision_client.get(f'/api/supervision/download_csv/{demo_id}')
    assert resp.status_code == 200
    assert 'text/csv' in resp.headers.get('content-type', '')
    lines = resp.text.splitlines()
    # First line should have 50 data headers + 2 standard headers (Filename, Link to The file)
    headers = lines[0].split(',')
    assert len(headers) == 52  # 50 data columns + 2 standard columns
    # Second line should have corresponding values
    values = lines[1].split(',')
    assert len(values) == 52
    # Clean up
    del results_dict[demo_id]

def test_partial_download_csv_integrity(supervision_client):
    """
    Validate that partial-range requests to the CSV download endpoint can be reassembled into the full content without corruption.
    """
    payload = {
        "edited_data": {"field1": "value1", "field2": "value2"},
        "filename": "partial_test.csv"
    }
    # Fetch the full content
    full_resp = supervision_client.post(
        "/api/supervision/download_csv",
        json=payload
    )
    assert full_resp.status_code == 200, f"Full download failed: {full_resp.status_code}"
    full = full_resp.content
    size = len(full)
    assert size > 0, "Downloaded content is empty"

    # Determine chunk size for partial requests
    chunk_size = max(size // 4, 1)
    chunks = []

    # Fetch in multiple ranges
    for start in range(0, size, chunk_size):
        end = min(start + chunk_size - 1, size - 1)
        headers = {"Range": f"bytes={start}-{end}"}
        resp = supervision_client.post(
            "/api/supervision/download_csv",
            json=payload,
            headers=headers
        )
        assert resp.status_code in (200, 206), f"Unexpected status {resp.status_code} for Range {start}-{end}"
        if resp.status_code == 206:
            assert "Content-Range" in resp.headers, "Missing Content-Range header on partial response"
            expected = f"bytes {start}-{end}/{size}"
            assert resp.headers.get("Content-Range").startswith(expected), f"Content-Range header mismatch: {resp.headers.get('Content-Range')}"
            fragment = resp.content
        else:
            fragment = resp.content[start:end+1]
        chunks.append(fragment)

    reassembled = b"".join(chunks)
    assert reassembled == full, "Reassembled content does not match full download" 