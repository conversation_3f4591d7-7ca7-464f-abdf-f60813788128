{"name": "dadp-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test && npm run test:e2e", "test:api": "jest --testPathPattern=api-integration", "test:api:auth": "jest src/__tests__/api-integration/real-api.test.ts", "test:api:annotator": "jest src/__tests__/api-integration/annotator-api.test.ts", "test:api:auditor": "jest src/__tests__/api-integration/auditor-api.test.ts", "test:api:specialized": "jest src/__tests__/api-integration/specialized-apis.test.ts", "test:api:verbose": "jest --testPathPattern=api-integration --verbose", "test:production": "npm run test:api && npm run test:e2e"}, "dependencies": {"@types/react-syntax-highlighter": "^15.5.13", "apexcharts": "^4.7.0", "bootstrap": "^5.3.6", "date-fns": "^4.1.0", "jszip": "^3.10.1", "next": "^15.3.3", "react": "^18.2.0", "react-datepicker": "^8.4.0", "react-dom": "^18.2.0", "react-easy-crop": "^5.4.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.0.1", "react-image-crop": "^11.0.10", "react-syntax-highlighter": "^15.6.1", "react-zoom-pan-pinch": "^3.7.0"}, "devDependencies": {"@types/cropperjs": "^1.1.5", "@types/jszip": "^3.4.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.16", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@playwright/test": "^1.40.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@types/jest": "^29.5.8", "msw": "^2.0.11", "whatwg-fetch": "^3.6.19"}}