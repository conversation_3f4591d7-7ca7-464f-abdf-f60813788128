/**
 * REAL API Integration Tests - Specialized Endpoints
 * Tests for Note-OCR, Synthetic Dataset, Telegram, and other specialized APIs
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'

// Helper functions
async function makeAuthenticatedRequest(
  endpoint: string, 
  options: RequestInit = {}, 
  authToken?: string
): Promise<Response> {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...options.headers,
  }

  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
    credentials: 'include',
  })

  return response
}

async function loginUser(username: string, password: string): Promise<string | null> {
  try {
    const response = await makeAuthenticatedRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password })
    })

    if (response.ok) {
      const data = await response.json()
      return data.access_token || null
    }
    return null
  } catch (error) {
    console.error('Login failed:', error)
    return null
  }
}

const TEST_USER = {
  username: 'test_admin',
  password: 'test_password_123'
}

describe('REAL API Integration Tests - Note-OCR Endpoints', () => {
  jest.setTimeout(30000)
  let authToken: string

  beforeAll(async () => {
    authToken = await loginUser(TEST_USER.username, TEST_USER.password) || ''
    expect(authToken).toBeTruthy()
  })

  describe('GET /NoteOCR/images/extractor-mode', () => {
    it('should check OCR extractor mode status', async () => {
      const response = await makeAuthenticatedRequest('/NoteOCR/images/extractor-mode', {
        method: 'GET'
      }, authToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('message')
    })
  })

  describe('GET /NoteOCR/documents/', () => {
    it('should fetch processed documents list', async () => {
      const response = await makeAuthenticatedRequest('/NoteOCR/documents/', {
        method: 'GET'
      }, authToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
      if (data.length > 0) {
        expect(data[0]).toHaveProperty('id')
        expect(data[0]).toHaveProperty('filename')
        expect(data[0]).toHaveProperty('created_at')
      }
    })
  })

  describe('POST /NoteOCR/upload-pdf', () => {
    it('should handle PDF upload for OCR processing', async () => {
      // Create a mock PDF file for testing
      const mockPdfContent = new Blob(['%PDF-1.4 mock pdf content'], { type: 'application/pdf' })
      const formData = new FormData()
      formData.append('file', mockPdfContent, 'test.pdf')

      const response = await fetch(`${API_BASE_URL}/NoteOCR/upload-pdf`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        credentials: 'include',
        body: formData
      })

      // Note: This might return 400 if the mock PDF is invalid, which is expected
      expect([200, 400, 422]).toContain(response.status)
    })
  })

  describe('POST /NoteOCR/upload-image', () => {
    it('should handle image upload for OCR processing', async () => {
      // Create a mock image file for testing
      const mockImageContent = new Blob(['mock image content'], { type: 'image/jpeg' })
      const formData = new FormData()
      formData.append('file', mockImageContent, 'test.jpg')

      const response = await fetch(`${API_BASE_URL}/NoteOCR/upload-image`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        credentials: 'include',
        body: formData
      })

      // Note: This might return 400 if the mock image is invalid, which is expected
      expect([200, 400, 422]).toContain(response.status)
    })
  })
})

describe('REAL API Integration Tests - Synthetic Dataset Endpoints', () => {
  jest.setTimeout(30000)
  let authToken: string

  beforeAll(async () => {
    authToken = await loginUser(TEST_USER.username, TEST_USER.password) || ''
    expect(authToken).toBeTruthy()
  })

  describe('POST /synthetic-dataset/generate', () => {
    it('should generate synthetic dataset', async () => {
      const generateRequest = {
        dataset_type: 'text_classification',
        num_samples: 10,
        parameters: {
          topic: 'customer reviews',
          language: 'english',
          sentiment_distribution: {
            positive: 0.4,
            negative: 0.3,
            neutral: 0.3
          }
        }
      }

      const response = await makeAuthenticatedRequest('/synthetic-dataset/generate', {
        method: 'POST',
        body: JSON.stringify(generateRequest)
      }, authToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('task_id')
      expect(data).toHaveProperty('status')
    })
  })

  describe('GET /synthetic-dataset/status/{task_id}', () => {
    it('should check generation status', async () => {
      // First create a generation task
      const generateRequest = {
        dataset_type: 'simple_text',
        num_samples: 5
      }

      const generateResponse = await makeAuthenticatedRequest('/synthetic-dataset/generate', {
        method: 'POST',
        body: JSON.stringify(generateRequest)
      }, authToken)

      if (generateResponse.status === 200) {
        const generateData = await generateResponse.json()
        const taskId = generateData.task_id

        // Check status
        const response = await makeAuthenticatedRequest(`/synthetic-dataset/status/${taskId}`, {
          method: 'GET'
        }, authToken)

        expect(response.status).toBe(200)
        
        const data = await response.json()
        expect(data).toHaveProperty('task_id', taskId)
        expect(data).toHaveProperty('status')
        expect(['pending', 'processing', 'completed', 'failed']).toContain(data.status)
      }
    })
  })

  describe('GET /synthetic-dataset/download/{task_id}', () => {
    it('should download completed dataset', async () => {
      // This test assumes there's a completed task
      // In real scenarios, you'd wait for a task to complete or use a known completed task ID
      const mockTaskId = 'test_task_id'
      
      const response = await makeAuthenticatedRequest(`/synthetic-dataset/download/${mockTaskId}`, {
        method: 'GET'
      }, authToken)

      // Expect either 200 (if task exists and is complete) or 404 (if task doesn't exist)
      expect([200, 404]).toContain(response.status)
    })
  })
})

describe('REAL API Integration Tests - Telegram Endpoints', () => {
  jest.setTimeout(30000)
  let authToken: string

  beforeAll(async () => {
    authToken = await loginUser(TEST_USER.username, TEST_USER.password) || ''
    expect(authToken).toBeTruthy()
  })

  describe('GET /telegram/channels', () => {
    it('should fetch configured Telegram channels', async () => {
      const response = await makeAuthenticatedRequest('/telegram/channels', {
        method: 'GET'
      }, authToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
      if (data.length > 0) {
        expect(data[0]).toHaveProperty('channel_id')
        expect(data[0]).toHaveProperty('channel_name')
      }
    })
  })

  describe('POST /telegram/add-channel', () => {
    it('should add new Telegram channel', async () => {
      const channelData = {
        channel_id: '@test_channel',
        channel_name: 'Test Channel',
        description: 'Test channel for API integration'
      }

      const response = await makeAuthenticatedRequest('/telegram/add-channel', {
        method: 'POST',
        body: JSON.stringify(channelData)
      }, authToken)

      expect([200, 400]).toContain(response.status) // 400 if channel already exists
    })
  })

  describe('GET /telegram/fetch-data/{channel_id}', () => {
    it('should fetch data from Telegram channel', async () => {
      const testChannelId = 'test_channel_123'
      
      const response = await makeAuthenticatedRequest(`/telegram/fetch-data/${testChannelId}`, {
        method: 'GET'
      }, authToken)

      // Expect either 200 (success) or 404 (channel not found) or 400 (invalid channel)
      expect([200, 400, 404]).toContain(response.status)
    })
  })

  describe('GET /telegram/analytics/{channel_id}', () => {
    it('should fetch channel analytics', async () => {
      const testChannelId = 'test_channel_123'
      
      const response = await makeAuthenticatedRequest(`/telegram/analytics/${testChannelId}`, {
        method: 'GET'
      }, authToken)

      // Expect either 200 (success) or 404 (channel not found)
      expect([200, 404]).toContain(response.status)
    })
  })
})
