import pytest
from fastapi.testclient import TestClient
from main import app
from sqlalchemy import text, inspect
from db.db_connector import engine
from db.models.user import User
from core.security import verify_password

@pytest.fixture(scope="session")
def client():
    return TestClient(app)


def test_password_hashing_at_rest(client, db_session):
    """
    Verify that passwords are stored as secure hashes, not in plain text.
    """
    payload = {
        "username": "secureuser",
        "password": "SecurePass123!",
        "confirm_password": "SecurePass123!",
        "role": "annotator",
        "full_name": "Secure User",
        "email": "<EMAIL>"
    }
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code == 200, f"Registration failed: {resp.text}"
    u = db_session.query(User).filter_by(username=payload["username"]).one()
    assert u.password_hash != payload["password"], "Password stored in plain text!"
    assert verify_password(payload["password"], u.password_hash), "Password hash verification failed"


def test_sql_injection_orm(db_session):
    """Attempt SQL injection via ORM filters (e.g., "' OR '1'='1")."""
    safe_user = User(username='safeuser', full_name='Safe User', email='<EMAIL>', password_hash='h')
    db_session.add(safe_user)
    db_session.commit()
    malicious = "' OR '1'='1"
    injected = db_session.query(User).filter(User.username == malicious).all()
    assert injected == []


def test_sql_injection_raw(db_session):
    """Attempt SQL injection via raw SQL text query."""
    # Insert a safe row via raw SQL, including all NOT NULL columns
    db_session.execute(
        text(
            "INSERT INTO users (username, full_name, email, password_hash, role, created_at, is_active, annotator_mode) "
            "VALUES (:u, :f, :e, :p, :r, CURRENT_TIMESTAMP, 1, :mode)"
        ), {"u": "rawsafe", "f": "Raw Safe", "e": "<EMAIL>", "p": "h", "r": "annotator", "mode": "annotation"}
    )
    db_session.commit()
    malicious = "'; DROP TABLE users; --"
    query = f"SELECT username FROM users WHERE username = '{malicious}'"
    with pytest.raises(Exception):
        db_session.execute(text(query))
    assert 'users' in inspect(db_session.get_bind()).get_table_names()
    result = db_session.execute(text("SELECT username FROM users")).fetchall()
    assert ('rawsafe',) in result


def test_encryption_at_rest(client, db_session):
    """Verify that passwords are hashed and not stored plaintext in the DB file."""
    password = "Secret123!"
    payload = {
        "username": "encuser",
        "password": password,
        "confirm_password": password,
        "role": "annotator",
        "full_name": "Enc User",
        "email": "<EMAIL>"
    }
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code == 200, f"Registration failed: {resp.text}"
    u = db_session.query(User).filter_by(username=payload["username"]).one()
    hashed = u.password_hash
    assert password != hashed
    # Verify the hash in the DB directly via SQL query
    row_hash = db_session.execute(
        text("SELECT password_hash FROM users WHERE username=:u"),
        {"u": payload["username"]}
    ).scalar()
    assert password not in row_hash, "Plaintext password found in DB"
    assert hashed == row_hash, "Stored hash does not match computed hash"


def test_db_user_permissions():
    """Validate that database connection URL does not embed credentials."""
    url = engine.url
    assert url.username is None, "Database URL should not include a username"
    assert url.password is None, "Database URL should not include a password"


# API-level SQL injection tests
@pytest.mark.parametrize('endpoint,field', [
    ('/api/auth/register', 'username'),
    ('/api/knowledge-base/entries', 'title'),
    ('/api/annotator/save-labels', 'batch_name'),
])
def test_api_sql_injection(client, endpoint, field):
    """Attempt SQL injection via API endpoints and ensure proper error responses."""
    # Build payload for registration and knowledge-base endpoints
    if endpoint == '/api/annotator/save-labels':
        payload = {"labels": {"img1": "<script>alert(1)</script>"}, "verification_mode": False, "batch_name": "../etc/passwd"}
        resp = client.post(endpoint, json=payload)
    else:
        payload = {field: "test' OR 1=1;--", "password": "x", "confirm_password": "x", "role": "annotator", "full_name": "x", "email": "<EMAIL>"}
        resp = client.post(endpoint, json=payload)
    assert resp.status_code in (400, 422, 500, 401), f"Endpoint {endpoint} allowed SQL injection: {resp.status_code}"


def test_login_success(client, db_session):
    client.cookies.clear()
    payload = {
        "username": "loginuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "Login User",
        "email": "<EMAIL>"
    }
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code == 200, f"Registration failed: {resp.text}"
    login_resp = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    assert login_resp.status_code == 200, f"Login failed: {login_resp.text}"
    tokens = login_resp.json()
    assert 'access_token' in tokens and 'refresh_token' in tokens and tokens['token_type'] == 'bearer'
    # Cookies should be set
    assert 'access_token' in login_resp.cookies and 'refresh_token' in login_resp.cookies

def test_login_failure_invalid_credentials(client):
    client.cookies.clear()
    resp = client.post('/api/auth/login', data={'username': 'nosuchuser', 'password': 'wrongpass'})
    assert resp.status_code == 401

def test_protected_endpoints_require_auth(client):
    client.cookies.clear()
    for ep in ['/api/auth/me', '/api/auth/verify']:
        resp = client.get(ep)
        assert resp.status_code == 401

def test_protected_endpoint_with_valid_token(client, db_session):
    client.cookies.clear()
    payload = {
        "username": "meuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "Me User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    login_resp = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    token = login_resp.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    resp = client.get('/api/auth/me', headers=headers)
    assert resp.status_code == 200
    data = resp.json()
    assert data['username'] == payload['username']

def test_refresh_token_endpoint(client, db_session):
    client.cookies.clear()
    # invalid refresh token
    resp = client.post('/api/auth/refresh-token', json={'refresh_token': 'badtoken'})
    assert resp.status_code == 401
    # valid refresh token
    payload = {
        "username": "refreshuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "Refresh User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    login_resp = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    refresh_token = login_resp.json()['refresh_token']
    resp = client.post('/api/auth/refresh-token', json={'refresh_token': refresh_token})
    assert resp.status_code == 200
    data = resp.json()
    assert 'access_token' in data
    # wrong token type: using access_token as refresh
    access_token = login_resp.json()['access_token']
    resp_wrong = client.post('/api/auth/refresh-token', json={'refresh_token': access_token})
    assert resp_wrong.status_code == 401

def test_change_password_flow(client, db_session):
    client.cookies.clear()
    payload = {
        "username": "changepassuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "ChangePass User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    login_resp = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    token = login_resp.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    body = {
        "current_password": payload['password'],
        "new_password": "NewPassword123!",
        "confirm_password": "NewPassword123!"
    }
    resp = client.post('/api/auth/change-password', json=body, headers=headers)
    assert resp.status_code == 200
    # old password should fail
    resp_old = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    assert resp_old.status_code == 401
    # new password should succeed
    resp_new = client.post('/api/auth/login', data={'username': payload['username'], 'password': body['new_password']})
    assert resp_new.status_code == 200

def test_change_password_wrong_current(client, db_session):
    client.cookies.clear()
    payload = {
        "username": "changepassuser2",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "ChangePass User2",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    login_resp = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    token = login_resp.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    body = {
        "current_password": "WrongPass!",
        "new_password": "NewPassword123!",
        "confirm_password": "NewPassword123!"
    }
    resp = client.post('/api/auth/change-password', json=body, headers=headers)
    assert resp.status_code == 400

def test_logout_clears_cookies(client, db_session):
    client.cookies.clear()
    payload = {
        "username": "logoutuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "Logout User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    resp = client.post('/api/auth/logout')
    assert resp.status_code == 200
    assert 'access_token' not in resp.cookies and 'refresh_token' not in resp.cookies 

def test_verify_token_happy_path(client, db_session):
    client.cookies.clear()
    payload = {
        "username": "verifyuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "Verify User",
        "email": "<EMAIL>"
    }
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code == 200
    login_resp = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    token = login_resp.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    verify_resp = client.get('/api/auth/verify', headers=headers)
    assert verify_resp.status_code == 200

import pytest

@pytest.mark.parametrize('endpoint', [
    '/api/auth/register',
    '/api/auth/login',
    '/api/auth/logout',
    '/api/auth/refresh-token',
    '/api/auth/refresh',
    '/api/auth/verify',
    '/api/auth/change-password'
])
def test_cors_preflight_auth_endpoints(client, endpoint):
    headers = {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Authorization,Content-Type'
    }
    resp = client.options(endpoint, headers=headers)
    assert resp.status_code == 200, f"CORS preflight for {endpoint} returned {resp.status_code}"
    allow_origin = resp.headers.get('access-control-allow-origin')
    assert allow_origin in ('*', 'http://localhost:3000')
    allow_methods = resp.headers.get('access-control-allow-methods', '')
    assert 'GET' in allow_methods and 'POST' in allow_methods
    allow_headers = resp.headers.get('access-control-allow-headers', '')
    assert 'Authorization' in allow_headers and 'Content-Type' in allow_headers


def test_admin_endpoint_access_control(client, db_session):
    client.cookies.clear()
    # Unauthenticated
    resp = client.get('/api/admin/dashboard')
    assert resp.status_code == 401
    # Annotator role
    ann = {
        "username": "annuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "Annotator User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=ann)
    login_ann = client.post('/api/auth/login', data={'username': ann['username'], 'password': ann['password']})
    token_ann = login_ann.json()['access_token']
    headers_ann = {'Authorization': f'Bearer {token_ann}'}
    resp = client.get('/api/admin/dashboard', headers=headers_ann)
    assert resp.status_code == 403
    # Admin role
    adm = {
        "username": "adminuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "admin",
        "full_name": "Admin User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=adm)
    login_adm = client.post('/api/auth/login', data={'username': adm['username'], 'password': adm['password']})
    token_adm = login_adm.json()['access_token']
    headers_adm = {'Authorization': f'Bearer {token_adm}'}
    resp = client.get('/api/admin/dashboard', headers=headers_adm)
    assert resp.status_code == 200


def test_duplicate_registration(client):
    client.cookies.clear()
    payload = {
        "username": "dupuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "Dup User",
        "email": "<EMAIL>"
    }
    resp1 = client.post('/api/auth/register', json=payload)
    assert resp1.status_code == 200
    resp2 = client.post('/api/auth/register', json=payload)
    assert resp2.status_code == 400
    err = resp2.json().get('detail', '')
    assert 'already registered' in err.lower()


def test_cookie_security_attributes(client, db_session):
    client.cookies.clear()
    payload = {
        "username": "cookieattruser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "CookieAttr User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    login_resp = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    set_cookie = login_resp.headers.get('set-cookie', '')
    assert 'HttpOnly' in set_cookie
    assert 'SameSite=lax' in set_cookie


def test_refresh_token_wrong_type(client, db_session):
    client.cookies.clear()
    payload = {
        "username": "wrongtypeuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "WrongType User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    login_resp = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    access_token = login_resp.json()['access_token']
    resp = client.post('/api/auth/refresh-token', json={'refresh_token': access_token})
    assert resp.status_code == 401 

def test_cookie_refresh_flow(client, db_session):
    client.cookies.clear()
    # no cookie scenario
    resp1 = client.post('/api/auth/refresh')
    assert resp1.status_code == 401
    # register and login to get cookie
    payload = {
        "username": "refreshflowuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "RefreshFlow User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    login_resp = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    assert 'refresh_token' in login_resp.cookies
    # refresh and check new cookies
    resp2 = client.post('/api/auth/refresh')
    assert resp2.status_code == 200
    assert 'access_token' in resp2.cookies and 'refresh_token' in resp2.cookies


def test_register_schema_validation_password_mismatch(client):
    payload = {
        "username": "schemauser",
        "password": "Password123!",
        "confirm_password": "Password321!",
        "role": "annotator",
        "full_name": "Schema User",
        "email": "<EMAIL>"
    }
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code == 422


def test_change_password_schema_validation_mismatch(client, db_session):
    # prepare user
    payload = {
        "username": "schemachangepass",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "SchemaCP User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    login_resp = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    token = login_resp.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    body = {
        "current_password": payload['password'],
        "new_password": "NewPass123!",
        "confirm_password": "MismatchPass!"
    }
    resp = client.post('/api/auth/change-password', json=body, headers=headers)
    assert resp.status_code == 422


def test_method_not_allowed_for_register_and_login(client):
    resp = client.get('/api/auth/register')
    assert resp.status_code == 405
    resp2 = client.put('/api/auth/login', json={})
    assert resp2.status_code == 405


def test_malformed_bearer_header(client):
    headers = {'Authorization': 'BearerXYZ malformed'}
    resp = client.get('/api/auth/me', headers=headers)
    assert resp.status_code == 401


def test_tampered_jwt_rejected(client, db_session):
    payload = {
        "username": "tamperuser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "Tamper User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    login_resp = client.post('/api/auth/login', data={'username': payload['username'], 'password': payload['password']})
    token = login_resp.json()['access_token']
    # clear valid cookie so header token is used and invalidated
    client.cookies.clear()
    tampered = token[:-1] + ('a' if token[-1] != 'a' else 'b')
    headers = {'Authorization': f'Bearer {tampered}'}
    resp = client.get('/api/auth/me', headers=headers)
    assert resp.status_code == 401


def test_annotator_endpoint_role_control(client, db_session):
    client.cookies.clear()
    # Unauthenticated
    resp = client.get('/api/annotator/dashboard')
    assert resp.status_code == 401
    # Auditor role
    aud = {
        "username": "auduser",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "auditor",
        "full_name": "Auditor User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=aud)
    login_resp = client.post('/api/auth/login', data={'username': aud['username'], 'password': aud['password']})
    token = login_resp.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    resp = client.get('/api/annotator/dashboard', headers=headers)
    assert resp.status_code == 403
    # Annotator role
    ann = {
        "username": "realannotator",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "Real Annotator",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=ann)
    login_resp = client.post('/api/auth/login', data={'username': ann['username'], 'password': ann['password']})
    token = login_resp.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    resp = client.get('/api/annotator/dashboard', headers=headers)
    assert resp.status_code == 200


def test_auditor_endpoint_role_control(client, db_session):
    client.cookies.clear()
    # Unauthenticated
    resp = client.get('/api/auditor/modes')
    assert resp.status_code == 401
    # Annotator role
    ann = {
        "username": "annforaud",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "annotator",
        "full_name": "Ann Auditor",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=ann)
    login_resp = client.post('/api/auth/login', data={'username': ann['username'], 'password': ann['password']})
    token = login_resp.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    resp = client.get('/api/auditor/modes', headers=headers)
    assert resp.status_code == 403
    # Auditor role
    aud = {
        "username": "trueauditor",
        "password": "Password123!",
        "confirm_password": "Password123!",
        "role": "auditor",
        "full_name": "True Auditor",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=aud)
    login_resp = client.post('/api/auth/login', data={'username': aud['username'], 'password': aud['password']})
    token = login_resp.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    resp = client.get('/api/auditor/modes', headers=headers)
    assert resp.status_code == 200 

def test_cors_preflight_all_endpoints(client):
    endpoints = [
        '/api/auth/register', '/api/auth/login', '/api/auth/logout',
        '/api/auth/refresh-token', '/api/auth/refresh', '/api/auth/verify',
        '/api/auth/change-password', '/api/admin/dashboard',
        '/api/knowledge-base/entries'
    ]
    headers = {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Authorization,Content-Type'
    }
    for ep in endpoints:
        resp = client.options(ep, headers=headers)
        assert resp.status_code == 200, f"CORS preflight for {ep} returned {resp.status_code}"
        allow_origin = resp.headers.get('access-control-allow-origin')
        assert allow_origin in ('*', 'http://localhost:3000')
        allow_methods = resp.headers.get('access-control-allow-methods', '')
        assert 'GET' in allow_methods and 'POST' in allow_methods
        allow_headers = resp.headers.get('access-control-allow-headers', '')
        assert 'Authorization' in allow_headers and 'Content-Type' in allow_headers 