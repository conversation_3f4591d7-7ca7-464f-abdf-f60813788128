import logging
from typing import Optional, List, Dict, Any
from sqlalchemy.exc import SQLAlchemyError
from db.db_connector import SessionLocal
from db.models.image_annotation import ImageAnnotation
from db.models.image_verification import ImageVerification
import asyncio
from db.models.datasets import Datasets
from core.nas_connector import get_ftp_connector
import ast,json,os
from datetime import datetime,timezone
from urllib.parse import unquote
# Setup logging
logger = logging.getLogger('auditor')

class AuditorService:

    @staticmethod
    def get_audit_folder_path(verification_mode: Optional[str] = None) -> str:
        """
        Get the appropriate audit folder path based on verification mode.
        """
        if verification_mode == 'manual':
            return '/Data/DATP Datasets/AUDITING/Manual Task verification'
        elif verification_mode == 'agentic':
            return '/Data/DATP Datasets/AUDITING/Verification Task'
        return '/Data/DATP Datasets/AUDITING'

    @staticmethod
    def normalize_path(path: Optional[str]) -> str:
        """
        Normalize a path to ensure it has no double slashes and proper formatting.
        """
        if path is None:
            return '/'

        path = path.replace('\\', '/')
        while '//' in path:
            path = path.replace('//', '/')
        path = path.rstrip('/')
        
        if path and not path.startswith('/'):
            path = '/' + path
        
        return path or '/'

    async def get_audit_datasets(self, annotator_mode: str) -> List[str]:
        """Get available datasets for auditing."""
        try:
            def query_datasets(annotator_mode: str) -> List[str]:
                with SessionLocal() as session:
                    query = session.query(Datasets.dataset_name,Datasets.dataset_image_path)
                    if annotator_mode:
                        query = query.filter(Datasets.annotator_mode == annotator_mode)
                    return [(dataset_name,dataset_image_path) for (dataset_name,dataset_image_path) in query.all()]

            return await asyncio.to_thread(query_datasets, annotator_mode)

        except SQLAlchemyError as db_err:
            logger.exception(f"Database error while fetching datasets (mode={annotator_mode}): {db_err}")
        except Exception as e:
            logger.exception(f"Unexpected error while fetching datasets (mode={annotator_mode}): {e}")
        return []
    
    async def get_verifiers_for_dataset(self, dataset_name: str, annotation_mode: str) -> List[str]:
        """Get verifiers for a dataset."""
        try:
            def query_usernames():
                with SessionLocal() as session:
                    if annotation_mode == 'annotation':
                        query = session.query(ImageAnnotation.annotator_username).filter(
                            ImageAnnotation.dataset_name == dataset_name,ImageAnnotation.annotation_status == 'completed',ImageAnnotation.audit_status == False
                            ).distinct()
                    elif annotation_mode == 'verification':
                        query = session.query(ImageVerification.annotator_username).filter(
                            ImageVerification.dataset_name == dataset_name,ImageVerification.verification_status == 'completed',ImageVerification.audit_status == False
                            ).distinct()
                    else:
                        return []
                    return [annotator_username for (annotator_username,) in query.all()]
                
            return await asyncio.to_thread(query_usernames)

        except SQLAlchemyError as db_err:
            logger.exception(f"Database error while getting verifiers for dataset '{dataset_name}': {db_err}")
        except Exception as e:
            logger.exception(f"Unexpected error while getting verifiers for dataset '{dataset_name}': {e}")
        return []

    
    async def update_audit_record(self, mode: str, record_id: int, auditor_username: str, audit_status: bool, comments: Optional[str] = None) -> bool:
        """Update audit status, auditor username, and comments on the specified record."""
        try:
            def sync_update():
                with SessionLocal() as session:
                    if mode == 'annotation':
                        rec = session.get(ImageAnnotation, record_id)
                    elif mode == 'verification':
                        rec = session.get(ImageVerification, record_id)
                    else:
                        return False
                    if not rec:
                        return False
                    if rec.audit_status is False:
                        audit_batch = session.query(Datasets).filter(Datasets.dataset_name == rec.dataset_name).first()
                        audit_batch.audited_batch += 1
                    rec.auditor_username = auditor_username
                    rec.audit_status = audit_status
                    rec.audit_comments = comments
                    rec.audited_at = datetime.now(timezone.utc)
                    rec.audit_status = True
                    session.commit()
                    return True
            return await asyncio.to_thread(sync_update)
        except SQLAlchemyError as db_err:
            logger.exception(f"Database error updating audit record: {db_err}")
        except Exception as e:
            logger.exception(f"Unexpected error updating audit record: {e}")
        return False

    async def get_audit_history(self, auditor_username: str) -> List[Dict[str, Any]]:
        """Return each batch audited by the user, with its mode and audited status."""
        try:
            def sync_query():
                with SessionLocal() as session:
                    histories: List[Dict[str, Any]] = []
                    # annotation assignments
                    ann_results = session.query(
                        ImageAnnotation.dataset_name,
                        ImageAnnotation.dataset_batch_name,
                        ImageAnnotation.annotator_username,
                        ImageAnnotation.audited_at,
                        ImageAnnotation.audit_comments
                    ).filter(
                        ImageAnnotation.auditor_username == auditor_username,
                        ImageAnnotation.audit_status == True
                    ).all()
                    for dataset, batch, annotator_username, audited_at, audit_comments in ann_results:
                        histories.append({
                            'audited_at': audited_at,
                            'dataset_name': dataset,
                            'batch_name': batch,
                            'annotator_username': annotator_username,
                            'audit_comments': audit_comments
                        })
                    # verification assignments
                    ver_results = session.query(
                        ImageVerification.dataset_name,
                        ImageVerification.dataset_batch_name,
                        ImageVerification.annotator_username,
                        ImageVerification.audited_at,
                        ImageVerification.audit_comments
                    ).filter(
                        ImageVerification.auditor_username == auditor_username,
                        ImageVerification.audit_status == True
                    ).all()
                    for dataset, batch, annotator_username, audited_at, audit_comments in ver_results:
                        histories.append({
                            'audited_at': audited_at,
                            'dataset_name': dataset,
                            'batch_name': batch,
                            'annotator_username': annotator_username,
                            'audit_comments': audit_comments
                        })
                    return histories
            return await asyncio.to_thread(sync_query)
        except SQLAlchemyError as db_err:
            logger.exception(f"Database error fetching audit history: {db_err}")
        except Exception as e:
            logger.exception(f"Unexpected error fetching audit history: {e}")
        return []
        
    async def get_tasks(self, mode: str, dataset_name: str, verifier_username: str, file_name: str) -> List[Dict[str, Any]]:
        """Retrieve images and their labels for the given audit batch from NAS and bind them."""
        connector = await get_ftp_connector()
        if not connector:
            logger.error("NAS connector not available for tasks retrieval")
            return []
        def query_record():
            with SessionLocal() as session:
                model = ImageAnnotation if mode == 'annotation' else ImageVerification
                batch_name = os.path.splitext(file_name)[0]
                return session.query(model).filter(
                    model.dataset_name == dataset_name,
                    model.dataset_batch_name == batch_name,
                    model.annotator_username == verifier_username
                ).first()
        rec = await asyncio.to_thread(query_record)
        if not rec:
            return []
        # Parse image list
        try:
            images = json.loads(rec.images)
        except Exception:
            try:
                images = ast.literal_eval(rec.images)
            except Exception:
                images = []
        # Load label JSON
        raw_label_path = rec.label_file_path or ''
        decoded_label_path = unquote(raw_label_path)
        labels_file_path = self.normalize_path(decoded_label_path)
        labels_map: Dict[str, Any] = {}
        # Fetch and parse the labels JSON (keys are simple filenames)
        try:
            content = await connector.get_file_content(labels_file_path)
            if content:
                labels_map = json.loads(
                    content.decode('utf-8') if isinstance(content, (bytes, bytearray)) else content
                )
            else:
                logger.error(f"No content retrieved for labels file: {labels_file_path}")
        except Exception as e:
            logger.error(f"Error loading labels file {labels_file_path}: {e}")
        # Build tasks
        tasks: List[Dict[str, Any]] = []
        for img_path in images:
            # Normalize the path
            normalized_path = self.normalize_path(img_path)
            try:
                exists = await connector.file_exists(normalized_path)
            except Exception as e:
                logger.error(f"Error checking file existence for {normalized_path}: {e}")
                exists = False
            if exists:
                name = os.path.basename(normalized_path)
                no_ext = os.path.splitext(name)[0]
                label = labels_map.get(name) or labels_map.get(no_ext) or ""
                tasks.append({
                    'task_id': no_ext,
                    'image_path': normalized_path,
                    'labels': label
                })
        return tasks
    
    async def get_files(self, dataset_name: str, verifier_username: str, mode: str) -> List[str]:
        """Get list of JSON files for the selected dataset and verifier, filtered by mode."""
        try:
            def sync_update():
                with SessionLocal() as session:
                    if mode == 'annotation':
                        files = session.query(ImageAnnotation.dataset_batch_name).filter(
                            ImageAnnotation.dataset_name == dataset_name,
                            ImageAnnotation.annotator_username == verifier_username,
                            ImageAnnotation.annotation_status == 'completed',
                            ImageAnnotation.audit_status == False
                        ).distinct().all()
                    elif mode == 'verification':
                        files = session.query(ImageVerification.dataset_batch_name).filter(
                            ImageVerification.dataset_name == dataset_name,
                            ImageVerification.annotator_username == verifier_username,
                            ImageVerification.verification_status == 'completed',
                            ImageVerification.audit_status == False
                        ).distinct().all()
                    else:
                        return []
                    
                    # Convert batch names to JSON filenames and flatten the result
                    return [f"{batch[0]}.json" for batch in files] if files else []

            return await asyncio.to_thread(sync_update)
        except SQLAlchemyError as db_err:
            logger.exception(f"Database error fetching files: {db_err}")
            return []
        except Exception as e:
            logger.exception(f"Unexpected error fetching files: {e}")
            return []

# Create singleton instance
auditor_service = AuditorService()