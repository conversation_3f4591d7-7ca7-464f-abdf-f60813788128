import pytest
from main import app
from fastapi.testclient import TestClient

client = TestClient(app)

@pytest.fixture(scope='module')
def admin_token():
    payload = {"username": "tgadmin", "password": "Tgpass123!", "confirm_password": "Tgpass123!", "role": "admin", "full_name": "TG Admin", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=payload)
    resp = client.post('/api/auth/login', data={"username": "tgadmin", "password": "Tgpass123!"})
    return resp.cookies

def test_connect_invalid(admin_token):
    # Should fail with invalid API ID/hash
    payload = {"api_id": 123, "api_hash": "bad", "phone": "123"}
    resp = client.post('/api/telegram/connect', json=payload, cookies=admin_token)
    # route may be missing, invalid, or error
    assert resp.status_code in (404, 401, 400, 500)

def test_connect_success(monkeypatch, admin_token):
    # Simulate successful Telegram connection returning a session string
    async def fake_connect(api_id, api_hash, phone, tg_session):
        return {'session_str': 'fake-session'}
    monkeypatch.setattr('routes.telegram_fetch_data_routes.connect_to_telegram', fake_connect)
    resp = client.post(
        '/api/telegram/connect',
        json={'api_id': 1, 'api_hash': 'hash', 'phone': '123'},
        cookies=admin_token
    )
    assert resp.status_code == 200
    # Confirm tg_session cookie is set
    assert 'tg_session' in resp.cookies
    assert resp.cookies['tg_session'] == 'fake-session'

def test_verify_code_success(monkeypatch, admin_token):
    # Simulate verifying code and returning updated session
    async def fake_verify(code, session_str):
        return {'session_str': 'updated-session'}
    monkeypatch.setattr('routes.telegram_fetch_data_routes.verify_code', fake_verify)
    cookies = dict(admin_token)
    cookies['tg_session'] = 'old-session'
    resp = client.post(
        '/api/telegram/verify-code',
        json={'code': '1234'},
        cookies=cookies
    )
    assert resp.status_code == 200
    assert 'tg_session' in resp.cookies
    assert resp.cookies['tg_session'] == 'updated-session'

def test_verify_password_success(monkeypatch, admin_token):
    # Simulate verifying password and returning updated session
    async def fake_verify_pw(password, session_str):
        return {'session_str': 'pw-session'}
    monkeypatch.setattr('routes.telegram_fetch_data_routes.verify_password', fake_verify_pw)
    cookies = dict(admin_token)
    cookies['tg_session'] = 'old-session'
    resp = client.post(
        '/api/telegram/verify-password',
        json={'password': '4567'},
        cookies=cookies
    )
    assert resp.status_code == 200
    assert resp.cookies['tg_session'] == 'pw-session'

def test_check_auth_success(monkeypatch, admin_token):
    # Simulate cache miss
    async def fake_get_cached_auth_status():
        return None
    monkeypatch.setattr('routes.telegram_fetch_data_routes.get_cached_auth_status', fake_get_cached_auth_status)
    # Simulate successful auth check
    async def fake_check(session_str):
        return {'authenticated': True}
    monkeypatch.setattr('routes.telegram_fetch_data_routes.check_auth', fake_check)
    # Stub cache_auth_status to no-op
    async def fake_cache_auth_status(result):
        pass
    monkeypatch.setattr('routes.telegram_fetch_data_routes.cache_auth_status', fake_cache_auth_status)
    cookies = dict(admin_token)
    cookies['tg_session'] = 'sess-123'
    resp = client.get(
        '/api/telegram/check-auth?refresh=true',
        cookies=cookies
    )
    assert resp.status_code == 200
    assert resp.json().get('authenticated') is True

def test_channels_success(monkeypatch, admin_token):
    # Simulate no cached channels to force fresh fetch
    async def fake_get_cached_channels():
        return None
    monkeypatch.setattr('routes.telegram_fetch_data_routes.get_cached_channels', fake_get_cached_channels)
    # Simulate fetching user channels
    async def fake_get_user_channels(session_str):
        return [{'id': 1, 'title': 'Channel1'}]
    monkeypatch.setattr('routes.telegram_fetch_data_routes.get_user_channels', fake_get_user_channels)
    # Stub cache_channels to no-op
    async def fake_cache_channels(channel_list):
        pass
    monkeypatch.setattr('routes.telegram_fetch_data_routes.cache_channels', fake_cache_channels)
    cookies = dict(admin_token)
    cookies['tg_session'] = 'fake-session'
    resp = client.get('/api/telegram/channels?refresh=true', cookies=cookies)
    assert resp.status_code == 200
    assert resp.json() == {'channels': [{'id': 1, 'title': 'Channel1'}]}

def test_disconnect_success(monkeypatch, admin_token):
    # Stub disconnect and cache invalidation
    async def fake_disconnect(session_str):
        return {'message': 'Successfully disconnected from Telegram'}
    monkeypatch.setattr('routes.telegram_fetch_data_routes.disconnect', fake_disconnect)
    # Stub reset_session to avoid real session logic
    async def fake_reset_session(session_str):
        return {}
    monkeypatch.setattr('routes.telegram_fetch_data_routes.reset_session', fake_reset_session)
    async def fake_invalidate_auth_status_cache():
        pass
    monkeypatch.setattr('routes.telegram_fetch_data_routes.invalidate_auth_status_cache', fake_invalidate_auth_status_cache)
    cookies = dict(admin_token)
    cookies['tg_session'] = 'fake-session'
    resp = client.post('/api/telegram/disconnect', cookies=cookies)
    assert resp.status_code == 200
    assert resp.json() == {'message': 'Successfully disconnected from Telegram'}

def test_get_images_success(monkeypatch, admin_token):
    # Stub get_channel_images
    async def fake_get_channel_images(channel_id, date, session_str, limit):
        return ['img1', 'img2']
    monkeypatch.setattr('routes.telegram_fetch_data_routes.get_channel_images', fake_get_channel_images)
    # Stub client retrieval to skip title lookup
    async def fake_get_or_create_client(session_str):
        return None
    monkeypatch.setattr('documind.telegram_service.telegram_fetcher.get_or_create_client', fake_get_or_create_client)
    # Stub cache_channel_images to no-op
    async def fake_cache_channel_images(channel_id, date, images, title):
        pass
    monkeypatch.setattr('routes.telegram_fetch_data_routes.cache_channel_images', fake_cache_channel_images)
    cookies = dict(admin_token)
    cookies['tg_session'] = 'fake-session'
    resp = client.get('/api/telegram/images?channel_id=5&date=2021-01-01', cookies=cookies)
    assert resp.status_code == 200
    assert resp.json() == {'images': ['img1', 'img2']}

def test_get_dates_success(monkeypatch, admin_token):
    # Stub get_channel_dates
    async def fake_get_channel_dates(channel_id, session_str):
        return ['2021-01-01', '2021-01-02']
    monkeypatch.setattr('routes.telegram_fetch_data_routes.get_channel_dates', fake_get_channel_dates)
    cookies = dict(admin_token)
    cookies['tg_session'] = 'fake-session'
    resp = client.get('/api/telegram/dates?channel_id=7', cookies=cookies)
    assert resp.status_code == 200
    assert resp.json() == {'dates': ['2021-01-01', '2021-01-02']}

def test_download_image_success(monkeypatch, admin_token):
    # Stub download_image
    async def fake_download_image(message_id, channel_id, filename, session_str):
        return {'image_data': 'data', 'metadata': {}, 'status': 'downloaded'}
    monkeypatch.setattr('routes.telegram_fetch_data_routes.download_image', fake_download_image)
    cookies = dict(admin_token)
    cookies['tg_session'] = 'fake-session'
    resp = client.post('/api/telegram/download-image', json={'message_id':3,'channel_id':7}, cookies=cookies)
    assert resp.status_code == 200
    assert resp.json() == {'image_data': 'data', 'metadata': {}, 'status': 'downloaded'}

def test_download_multiple_success(monkeypatch, admin_token):
    # Stub download_image to always return image_data
    async def fake_download_image(message_id, channel_id, filename, session_str):
        return {'image_data': f'data_{message_id}', 'metadata': {}, 'status': 'ok'}
    monkeypatch.setattr('routes.telegram_fetch_data_routes.download_image', fake_download_image)
    images = [{'id':1,'channel_id':2},{'id':3,'channel_id':4}]
    cookies = dict(admin_token)
    cookies['tg_session'] = 'fake-session'
    resp = client.post('/api/telegram/download-multiple', json={'images': images}, cookies=cookies)
    assert resp.status_code == 200
    expected = {'downloaded': [
        {'message_id':1,'channel_id':2,'image_data':'data_1','metadata':{},'status':'ok'},
        {'message_id':3,'channel_id':4,'image_data':'data_3','metadata':{},'status':'ok'},
    ]}
    assert resp.json() == expected

def test_analytics_success(monkeypatch, admin_token):
    # Stub get_channel_analytics
    async def fake_get_channel_analytics(channel_id, session_str, days):
        return {'views': 100}
    monkeypatch.setattr('routes.telegram_fetch_data_routes.get_channel_analytics', fake_get_channel_analytics)
    # Stub cache_channel_analytics to no-op
    async def fake_cache_channel_analytics(channel_id, analytics):
        pass
    monkeypatch.setattr('routes.telegram_fetch_data_routes.cache_channel_analytics', fake_cache_channel_analytics)
    cookies = dict(admin_token)
    cookies['tg_session'] = 'fake-session'
    resp = client.get('/api/telegram/analytics?channel_id=11&days=7', cookies=cookies)
    assert resp.status_code == 200
    assert resp.json() == {'views': 100}

def test_upload_to_drive_success(monkeypatch, admin_token):
    # Stub DriveUploader.upload_telegram_images_from_cache
    def fake_upload(self, image_list, channel_name):
        return {'uploaded': len(image_list)}
    monkeypatch.setattr('routes.telegram_fetch_data_routes.DriveUploader.upload_telegram_images_from_cache', fake_upload)
    # Prepare a base64 image entry
    img = {'image_data': 'dGVzdA==', 'original_filename': 'orig'}
    cookies = dict(admin_token)
    cookies['tg_session'] = 'fake-session'
    resp = client.post('/api/telegram/upload-to-drive', json={'images':[img], 'channel_name':'TestCh'}, cookies=cookies)
    assert resp.status_code == 200
    assert resp.json() == {'uploaded': 1}

def test_export_analytics_csv_success(monkeypatch, admin_token):
    # Stub get_user_channels
    async def fake_get_user_channels(session_str):
        return [{'id':2,'title':'T2','username':'u2','participants_count':20}]
    monkeypatch.setattr('routes.telegram_fetch_data_routes.get_user_channels', fake_get_user_channels)
    # Stub get_channel_analytics
    async def fake_get_channel_analytics(channel_id, session_str):
        return {'channel':{'title':'T2','username':'u2','participants_count':20},'total_images':5,'dates':['d1','d2'],'first_date':'d1','last_date':'d2'}
    monkeypatch.setattr('routes.telegram_fetch_data_routes.get_channel_analytics', fake_get_channel_analytics)
    cookies = dict(admin_token)
    cookies['tg_session'] = 'fake-session'
    resp = client.get('/api/telegram/export-analytics-csv', cookies=cookies)
    assert resp.status_code == 200
    text = resp.text.splitlines()
    assert text[0] == 'Channel ID,Name,Username,Participants,Total Images,Days,First Date,Last Date'
    assert '2,T2,u2,20,5,2,d1,d2' in text[1]

def test_unauthorized_telegram_endpoints():
    """All Telegram endpoints should return 401 when no session cookie is provided."""
    client_no_auth = TestClient(app)
    endpoints = [
        ('post', '/api/telegram/connect', {'json': {'api_id': 1, 'api_hash': 'hash', 'phone': '123'}}),
        ('post', '/api/telegram/verify-code', {'json': {'code': '1234'}}),
        ('post', '/api/telegram/verify-password', {'json': {'password': '4567'}}),
        ('get', '/api/telegram/check-auth?refresh=true', {}),
        ('get', '/api/telegram/channels?refresh=true', {}),
        ('post', '/api/telegram/disconnect', {}),
        ('get', '/api/telegram/images?channel_id=1&date=2021-01-01', {}),
        ('get', '/api/telegram/dates?channel_id=1', {}),
        ('post', '/api/telegram/download-image', {'json': {'message_id': 1, 'channel_id': 2}}),
        ('post', '/api/telegram/download-multiple', {'json': {'images': []}}),
        ('get', '/api/telegram/analytics?channel_id=11&days=7', {}),
        ('post', '/api/telegram/upload-to-drive', {'json': {'images': [], 'channel_name': 'TestCh'}}),
        ('get', '/api/telegram/export-analytics-csv', {})
    ]
    for method, path, kwargs in endpoints:
        resp = getattr(client_no_auth, method)(path, **kwargs)
        assert resp.status_code == 401, f"Unauthorized {path} returned {resp.status_code}"

# Missing parameter validation tests
def test_images_missing_params(admin_token):
    """Missing query params for images endpoint should return 422."""
    resp = client.get('/api/telegram/images', cookies=admin_token)
    assert resp.status_code == 422

def test_dates_missing_params(admin_token):
    """Missing query params for dates endpoint should return 422."""
    resp = client.get('/api/telegram/dates', cookies=admin_token)
    assert resp.status_code == 422

def test_download_image_missing_fields(admin_token):
    """Missing JSON fields for download-image should return 422."""
    resp = client.post('/api/telegram/download-image', json={}, cookies=admin_token)
    assert resp.status_code == 422

def test_download_multiple_missing_fields(admin_token):
    """Missing JSON fields for download-multiple should return 422."""
    resp = client.post('/api/telegram/download-multiple', json={}, cookies=admin_token)
    assert resp.status_code == 422 