2025-07-05 16:16:16,069 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-05 16:16:16,069 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-05 16:19:24,436 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-05 16:19:24,436 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-05 16:36:14,851 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-05 16:36:14,851 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-05 16:46:47,577 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-05 16:46:47,578 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-07 11:48:13,798 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-07 11:48:13,798 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-07 11:55:22,344 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-07 11:55:22,344 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-07 12:18:25,132 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-07 12:18:25,133 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-07 12:35:10,190 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-07 12:35:10,191 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-07 14:48:49,351 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-07 14:48:49,351 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-07 14:59:44,549 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-07 14:59:44,549 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-11 10:47:29,375 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-11 10:47:29,375 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-11 11:32:49,823 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-11 11:32:49,824 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-11 11:35:44,378 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-11 11:35:44,378 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-11 11:38:26,958 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-11 11:38:26,958 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-11 11:40:23,817 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-11 11:40:23,817 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-11 11:41:33,411 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-11 11:41:33,411 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-11 14:10:23,076 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-11 14:10:23,079 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-11 14:45:45,792 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-11 14:45:45,792 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-11 14:57:09,633 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-11 14:57:09,633 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 10:25:17,628 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 10:25:17,628 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 11:04:25,131 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 11:04:25,131 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 11:19:59,416 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 11:19:59,419 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 12:56:42,986 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 12:56:42,986 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 13:10:08,215 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 13:10:08,216 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 13:17:18,846 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 13:17:18,846 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 13:20:35,064 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 13:20:35,064 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 13:41:31,397 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 13:41:31,398 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 13:56:46,764 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 13:56:46,764 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 14:06:35,101 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 14:06:35,101 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 14:14:39,139 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 14:14:39,139 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 14:23:27,891 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 14:23:27,891 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
2025-07-12 14:44:54,410 - admin_routes - ERROR - Error in dashboard route: Drive down
2025-07-12 14:44:54,410 - admin_routes - ERROR - Detailed error:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\routes\admin_routes.py", line 95, in dashboard
    drive_status = drive_client.is_configured()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1105, in __call__
    return self._mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1109, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\unittest\mock.py", line 1164, in _execute_mock_call
    raise effect
Exception: Drive down
