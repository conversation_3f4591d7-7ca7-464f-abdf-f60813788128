2025-07-07 11:13:44,851 - uvicorn.error - INFO - Started server process [55352]
2025-07-07 11:13:44,851 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 11:13:48,933 - uvicorn.error - INFO - Application startup complete.
2025-07-07 11:14:47,546 - uvicorn.error - INFO - Started server process [44136]
2025-07-07 11:14:47,546 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 11:14:51,634 - uvicorn.error - INFO - Application startup complete.
2025-07-07 11:20:00,251 - uvicorn.error - INFO - Shutting down
2025-07-07 11:20:00,366 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 11:20:00,368 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 11:20:00,368 - uvicorn.error - INFO - Finished server process [55352]
2025-07-07 11:20:00,368 - uvicorn.error - INFO - Finished server process [44136]
2025-07-07 11:20:06,268 - uvicorn.error - INFO - Started server process [37316]
2025-07-07 11:20:06,268 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 11:20:06,614 - uvicorn.error - INFO - Started server process [43164]
2025-07-07 11:20:06,614 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 11:20:10,354 - uvicorn.error - INFO - Application startup complete.
2025-07-07 11:20:10,375 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-07 11:20:10,715 - uvicorn.error - INFO - Application startup complete.
2025-07-07 11:20:16,314 - uvicorn.error - INFO - Started server process [53196]
2025-07-07 11:20:16,314 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 11:20:20,370 - uvicorn.error - INFO - Application startup complete.
2025-07-07 11:21:53,240 - uvicorn.error - INFO - Shutting down
2025-07-07 11:21:53,344 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 11:21:53,344 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 11:21:53,344 - uvicorn.error - INFO - Finished server process [53196]
2025-07-07 11:22:02,558 - uvicorn.error - INFO - Started server process [42072]
2025-07-07 11:22:02,558 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 11:22:06,629 - uvicorn.error - INFO - Application startup complete.
2025-07-07 11:51:00,381 - uvicorn.error - INFO - Shutting down
2025-07-07 11:51:00,491 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 11:51:00,499 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 11:51:00,501 - uvicorn.error - INFO - Finished server process [42072]
2025-07-07 11:51:00,726 - uvicorn.error - INFO - Shutting down
2025-07-07 11:51:00,868 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 11:51:00,889 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 11:51:00,893 - uvicorn.error - INFO - Finished server process [43164]
2025-07-07 11:51:09,304 - uvicorn.error - INFO - Started server process [23088]
2025-07-07 11:51:09,304 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 11:51:09,544 - uvicorn.error - INFO - Started server process [54732]
2025-07-07 11:51:09,544 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 11:51:13,408 - uvicorn.error - INFO - Application startup complete.
2025-07-07 11:51:13,641 - uvicorn.error - INFO - Application startup complete.
2025-07-07 11:51:20,673 - uvicorn.error - INFO - Shutting down
2025-07-07 11:51:20,688 - uvicorn.error - INFO - Shutting down
2025-07-07 11:51:20,781 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 11:51:20,782 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 11:51:20,782 - uvicorn.error - INFO - Finished server process [23088]
2025-07-07 11:51:20,796 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 11:51:20,796 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 11:51:20,796 - uvicorn.error - INFO - Finished server process [54732]
2025-07-07 11:51:24,890 - uvicorn.error - INFO - Started server process [46216]
2025-07-07 11:51:24,890 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 11:51:25,167 - uvicorn.error - INFO - Started server process [18328]
2025-07-07 11:51:25,168 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 11:51:29,010 - uvicorn.error - INFO - Application startup complete.
2025-07-07 11:51:29,289 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:06:46,627 - uvicorn.error - INFO - Shutting down
2025-07-07 12:06:46,627 - uvicorn.error - INFO - Shutting down
2025-07-07 12:06:46,736 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:06:46,737 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:06:46,738 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:06:46,738 - uvicorn.error - INFO - Finished server process [18328]
2025-07-07 12:06:46,738 - uvicorn.error - INFO - Finished server process [46216]
2025-07-07 12:06:51,687 - uvicorn.error - INFO - Started server process [7676]
2025-07-07 12:06:51,687 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:06:51,720 - uvicorn.error - INFO - Started server process [39436]
2025-07-07 12:06:51,721 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:06:55,750 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:06:55,783 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:06:59,249 - uvicorn.error - INFO - Shutting down
2025-07-07 12:06:59,265 - uvicorn.error - INFO - Shutting down
2025-07-07 12:06:59,357 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:06:59,357 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:06:59,357 - uvicorn.error - INFO - Finished server process [7676]
2025-07-07 12:06:59,373 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:06:59,373 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:06:59,374 - uvicorn.error - INFO - Finished server process [39436]
2025-07-07 12:07:02,952 - uvicorn.error - INFO - Started server process [30648]
2025-07-07 12:07:02,953 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:07:03,114 - uvicorn.error - INFO - Started server process [19104]
2025-07-07 12:07:03,115 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:07:07,039 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:07:07,208 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:20:18,283 - uvicorn.error - INFO - Shutting down
2025-07-07 12:20:18,283 - uvicorn.error - INFO - Shutting down
2025-07-07 12:20:18,391 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:20:18,391 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:20:18,391 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:20:18,392 - uvicorn.error - INFO - Finished server process [30648]
2025-07-07 12:20:18,392 - uvicorn.error - INFO - Finished server process [19104]
2025-07-07 12:20:22,890 - uvicorn.error - INFO - Started server process [13256]
2025-07-07 12:20:22,890 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:20:23,023 - uvicorn.error - INFO - Started server process [33800]
2025-07-07 12:20:23,024 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:20:26,981 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:20:27,119 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:20:48,439 - uvicorn.error - INFO - Shutting down
2025-07-07 12:20:48,454 - uvicorn.error - INFO - Shutting down
2025-07-07 12:20:48,549 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:20:48,550 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:20:48,550 - uvicorn.error - INFO - Finished server process [13256]
2025-07-07 12:20:48,564 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:20:48,564 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:20:48,565 - uvicorn.error - INFO - Finished server process [33800]
2025-07-07 12:20:53,632 - uvicorn.error - INFO - Started server process [37148]
2025-07-07 12:20:53,633 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:20:53,697 - uvicorn.error - INFO - Started server process [51544]
2025-07-07 12:20:53,697 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:20:57,689 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:20:57,715 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-07 12:20:57,798 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:20:57,808 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-07 12:21:02,592 - uvicorn.error - INFO - Started server process [13684]
2025-07-07 12:21:02,592 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:21:02,902 - uvicorn.error - INFO - Started server process [19584]
2025-07-07 12:21:02,903 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:21:06,702 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:21:06,995 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:22:53,819 - uvicorn.error - INFO - Shutting down
2025-07-07 12:22:53,927 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:22:53,927 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:22:53,928 - uvicorn.error - INFO - Finished server process [13684]
2025-07-07 12:22:57,835 - uvicorn.error - INFO - Started server process [17656]
2025-07-07 12:22:57,835 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:22:57,887 - uvicorn.error - INFO - Started server process [42012]
2025-07-07 12:22:57,887 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:23:01,923 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:23:01,968 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:23:22,953 - uvicorn.error - INFO - Shutting down
2025-07-07 12:23:22,968 - uvicorn.error - INFO - Shutting down
2025-07-07 12:23:23,063 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:23:23,063 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:23:23,064 - uvicorn.error - INFO - Finished server process [17656]
2025-07-07 12:23:23,078 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:23:23,079 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:23:23,079 - uvicorn.error - INFO - Finished server process [42012]
2025-07-07 12:23:27,006 - uvicorn.error - INFO - Started server process [14764]
2025-07-07 12:23:27,007 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:23:28,635 - uvicorn.error - INFO - Started server process [42164]
2025-07-07 12:23:28,635 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:23:31,106 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:23:31,113 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-07 12:23:32,729 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:23:35,237 - uvicorn.error - INFO - Started server process [34612]
2025-07-07 12:23:35,237 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:23:39,328 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:25:49,666 - uvicorn.error - INFO - Shutting down
2025-07-07 12:25:49,776 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:25:49,777 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:25:49,777 - uvicorn.error - INFO - Finished server process [42164]
2025-07-07 12:25:53,739 - uvicorn.error - INFO - Started server process [52140]
2025-07-07 12:25:53,740 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:25:53,911 - uvicorn.error - INFO - Started server process [19252]
2025-07-07 12:25:53,911 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:25:57,777 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:25:57,979 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:43:47,249 - uvicorn.error - INFO - Shutting down
2025-07-07 12:43:47,249 - uvicorn.error - INFO - Shutting down
2025-07-07 12:43:47,358 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:43:47,360 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:43:47,361 - uvicorn.error - INFO - Finished server process [52140]
2025-07-07 12:43:47,363 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:43:47,363 - uvicorn.error - INFO - Finished server process [19252]
2025-07-07 12:43:51,894 - uvicorn.error - INFO - Started server process [50652]
2025-07-07 12:43:51,895 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:43:51,975 - uvicorn.error - INFO - Started server process [10644]
2025-07-07 12:43:51,975 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:43:56,000 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:43:56,076 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:45:53,998 - uvicorn.error - INFO - Shutting down
2025-07-07 12:45:54,109 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:45:54,109 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:45:54,110 - uvicorn.error - INFO - Finished server process [10644]
2025-07-07 12:45:54,218 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:45:54,218 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:45:54,219 - uvicorn.error - INFO - Finished server process [50652]
2025-07-07 12:45:58,801 - uvicorn.error - INFO - Started server process [56252]
2025-07-07 12:45:58,801 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:45:58,858 - uvicorn.error - INFO - Started server process [39636]
2025-07-07 12:45:58,859 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:46:02,903 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:46:02,934 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:53:28,613 - uvicorn.error - INFO - Shutting down
2025-07-07 12:53:28,724 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:53:28,724 - uvicorn.error - INFO - Shutting down
2025-07-07 12:53:28,725 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:53:28,725 - uvicorn.error - INFO - Finished server process [39636]
2025-07-07 12:53:28,834 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 12:53:28,834 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 12:53:28,834 - uvicorn.error - INFO - Finished server process [56252]
2025-07-07 12:53:32,936 - uvicorn.error - INFO - Started server process [46896]
2025-07-07 12:53:32,937 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:53:33,077 - uvicorn.error - INFO - Started server process [37220]
2025-07-07 12:53:33,077 - uvicorn.error - INFO - Waiting for application startup.
2025-07-07 12:53:37,006 - uvicorn.error - INFO - Application startup complete.
2025-07-07 12:53:37,146 - uvicorn.error - INFO - Application startup complete.
2025-07-07 13:01:55,306 - uvicorn.error - INFO - Shutting down
2025-07-07 13:01:55,407 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 13:01:55,407 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 13:01:55,408 - uvicorn.error - INFO - Finished server process [37220]
2025-07-07 13:01:58,207 - uvicorn.error - INFO - Shutting down
2025-07-07 13:01:58,322 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-07 13:01:58,323 - uvicorn.error - INFO - Application shutdown complete.
2025-07-07 13:01:58,323 - uvicorn.error - INFO - Finished server process [46896]
2025-07-11 12:34:46,055 - uvicorn.error - INFO - Started server process [17588]
2025-07-11 12:34:46,059 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:34:50,124 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:35:22,010 - uvicorn.error - INFO - Started server process [20732]
2025-07-11 12:35:22,011 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:35:26,085 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:52:13,539 - uvicorn.error - INFO - Shutting down
2025-07-11 12:52:13,539 - uvicorn.error - INFO - Shutting down
2025-07-11 12:52:13,642 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:52:13,642 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:52:13,642 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:52:13,642 - uvicorn.error - INFO - Finished server process [17588]
2025-07-11 12:52:13,642 - uvicorn.error - INFO - Finished server process [20732]
2025-07-11 12:52:17,330 - uvicorn.error - INFO - Started server process [2104]
2025-07-11 12:52:17,340 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:52:17,510 - uvicorn.error - INFO - Started server process [29472]
2025-07-11 12:52:17,511 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:52:21,394 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:52:21,604 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:55:10,565 - uvicorn.error - INFO - Shutting down
2025-07-11 12:55:10,674 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:55:10,675 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:55:10,676 - uvicorn.error - INFO - Finished server process [29472]
2025-07-11 12:55:17,602 - uvicorn.error - INFO - Started server process [14356]
2025-07-11 12:55:17,602 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:55:21,693 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:56:26,104 - uvicorn.error - INFO - Shutting down
2025-07-11 12:56:26,208 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:56:26,208 - uvicorn.error - INFO - Shutting down
2025-07-11 12:56:26,209 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:56:26,209 - uvicorn.error - INFO - Finished server process [2104]
2025-07-11 12:56:26,320 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:56:26,320 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:56:26,321 - uvicorn.error - INFO - Finished server process [14356]
2025-07-11 12:56:30,755 - uvicorn.error - INFO - Started server process [10680]
2025-07-11 12:56:30,755 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:56:32,800 - uvicorn.error - INFO - Started server process [29376]
2025-07-11 12:56:32,801 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:56:34,848 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:56:34,879 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 12:56:36,913 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:56:38,913 - uvicorn.error - INFO - Started server process [27560]
2025-07-11 12:56:38,913 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:56:43,007 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:56:46,224 - uvicorn.error - INFO - Shutting down
2025-07-11 12:56:46,300 - uvicorn.error - INFO - Shutting down
2025-07-11 12:56:46,331 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:56:46,332 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:56:46,332 - uvicorn.error - INFO - Finished server process [27560]
2025-07-11 12:56:46,410 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:56:46,410 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:56:46,410 - uvicorn.error - INFO - Finished server process [29376]
2025-07-11 12:56:50,502 - uvicorn.error - INFO - Started server process [7380]
2025-07-11 12:56:50,502 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:56:51,603 - uvicorn.error - INFO - Started server process [11692]
2025-07-11 12:56:51,604 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:56:54,563 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:56:54,573 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 12:56:55,660 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:56:58,873 - uvicorn.error - INFO - Started server process [25320]
2025-07-11 12:56:58,873 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:57:01,045 - uvicorn.error - INFO - Shutting down
2025-07-11 12:57:01,155 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:57:01,155 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:57:01,156 - uvicorn.error - INFO - Finished server process [11692]
2025-07-11 12:57:02,987 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:57:03,000 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 12:57:06,443 - uvicorn.error - INFO - Started server process [29252]
2025-07-11 12:57:06,443 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:57:06,827 - uvicorn.error - INFO - Started server process [12456]
2025-07-11 12:57:06,827 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:57:10,510 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:57:10,883 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:57:13,978 - uvicorn.error - INFO - Shutting down
2025-07-11 12:57:14,044 - uvicorn.error - INFO - Shutting down
2025-07-11 12:57:14,079 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:57:14,079 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:57:14,080 - uvicorn.error - INFO - Finished server process [12456]
2025-07-11 12:57:14,147 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:57:14,148 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:57:14,148 - uvicorn.error - INFO - Finished server process [29252]
2025-07-11 12:57:18,447 - uvicorn.error - INFO - Started server process [17280]
2025-07-11 12:57:18,447 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:57:19,393 - uvicorn.error - INFO - Started server process [27772]
2025-07-11 12:57:19,393 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:57:22,517 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:57:22,525 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 12:57:23,480 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:57:26,153 - uvicorn.error - INFO - Started server process [29468]
2025-07-11 12:57:26,154 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:57:30,198 - uvicorn.error - INFO - Shutting down
2025-07-11 12:57:30,229 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:57:30,247 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 12:57:30,306 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:57:30,306 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:57:30,306 - uvicorn.error - INFO - Finished server process [27772]
2025-07-11 12:57:34,436 - uvicorn.error - INFO - Started server process [17844]
2025-07-11 12:57:34,437 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:57:35,531 - uvicorn.error - INFO - Started server process [28272]
2025-07-11 12:57:35,532 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:57:38,495 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:57:38,505 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 12:57:39,611 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:57:42,703 - uvicorn.error - INFO - Started server process [20648]
2025-07-11 12:57:42,703 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:57:45,681 - uvicorn.error - INFO - Shutting down
2025-07-11 12:57:45,790 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:57:45,790 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:57:45,791 - uvicorn.error - INFO - Finished server process [28272]
2025-07-11 12:57:46,790 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:57:46,803 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 12:57:50,997 - uvicorn.error - INFO - Started server process [29308]
2025-07-11 12:57:50,997 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:57:52,202 - uvicorn.error - INFO - Started server process [29052]
2025-07-11 12:57:52,202 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:57:55,074 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:57:55,090 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 12:57:56,309 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:57:59,426 - uvicorn.error - INFO - Started server process [5028]
2025-07-11 12:57:59,426 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:58:02,606 - uvicorn.error - INFO - Shutting down
2025-07-11 12:58:02,719 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 12:58:02,719 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 12:58:02,719 - uvicorn.error - INFO - Finished server process [29052]
2025-07-11 12:58:03,495 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:58:03,509 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 12:58:07,834 - uvicorn.error - INFO - Started server process [2560]
2025-07-11 12:58:07,834 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:58:08,144 - uvicorn.error - INFO - Started server process [19796]
2025-07-11 12:58:08,144 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:58:11,892 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:58:11,909 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 12:58:12,225 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:58:15,523 - uvicorn.error - INFO - Started server process [14220]
2025-07-11 12:58:15,524 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 12:58:19,575 - uvicorn.error - INFO - Application startup complete.
2025-07-11 12:58:19,643 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:00:36,457 - uvicorn.error - INFO - Started server process [22256]
2025-07-11 13:00:36,458 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:00:40,525 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:02:17,274 - uvicorn.error - INFO - Shutting down
2025-07-11 13:02:17,380 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:02:17,380 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:02:17,380 - uvicorn.error - INFO - Finished server process [22256]
2025-07-11 13:02:27,076 - uvicorn.error - INFO - Shutting down
2025-07-11 13:02:27,181 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:02:27,181 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:02:27,182 - uvicorn.error - INFO - Finished server process [19796]
2025-07-11 13:20:13,884 - uvicorn.error - INFO - Started server process [29460]
2025-07-11 13:20:13,884 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:20:14,896 - uvicorn.error - INFO - Started server process [27032]
2025-07-11 13:20:14,896 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:20:17,928 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:20:18,980 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:21:04,739 - uvicorn.error - INFO - Shutting down
2025-07-11 13:21:04,853 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:21:04,853 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:21:04,853 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:21:04,853 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:21:04,853 - uvicorn.error - INFO - Finished server process [29460]
2025-07-11 13:21:04,853 - uvicorn.error - INFO - Finished server process [27032]
2025-07-11 13:21:09,231 - uvicorn.error - INFO - Started server process [29076]
2025-07-11 13:21:09,231 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:21:10,365 - uvicorn.error - INFO - Started server process [20220]
2025-07-11 13:21:10,365 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:21:13,293 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:21:13,315 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:21:14,411 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:21:17,289 - uvicorn.error - INFO - Started server process [11828]
2025-07-11 13:21:17,290 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:21:21,362 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:30:20,561 - uvicorn.error - INFO - Shutting down
2025-07-11 13:30:20,581 - uvicorn.error - INFO - Shutting down
2025-07-11 13:30:20,663 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:30:20,663 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:30:20,663 - uvicorn.error - INFO - Finished server process [11828]
2025-07-11 13:30:20,697 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:30:20,697 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:30:20,698 - uvicorn.error - INFO - Finished server process [20220]
2025-07-11 13:30:25,221 - uvicorn.error - INFO - Started server process [9608]
2025-07-11 13:30:25,221 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:30:27,463 - uvicorn.error - INFO - Started server process [18592]
2025-07-11 13:30:27,464 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:30:29,277 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:30:29,277 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:30:29,423 - uvicorn.error - ERROR - Exception in ASGI application
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
GeneratorExit
2025-07-11 13:30:31,545 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:30:34,970 - uvicorn.error - INFO - Started server process [27144]
2025-07-11 13:30:34,970 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:30:36,598 - uvicorn.error - INFO - Shutting down
2025-07-11 13:30:36,701 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:30:36,701 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:30:36,701 - uvicorn.error - INFO - Finished server process [18592]
2025-07-11 13:30:39,038 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:30:39,051 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:30:43,603 - uvicorn.error - INFO - Started server process [27692]
2025-07-11 13:30:43,603 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:30:44,234 - uvicorn.error - INFO - Started server process [2744]
2025-07-11 13:30:44,234 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:30:47,679 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:30:48,297 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:34:31,327 - uvicorn.error - INFO - Shutting down
2025-07-11 13:34:31,437 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:34:31,437 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:34:31,437 - uvicorn.error - INFO - Finished server process [2744]
2025-07-11 13:34:31,437 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:34:31,438 - uvicorn.error - INFO - Finished server process [27692]
2025-07-11 13:34:35,504 - uvicorn.error - INFO - Started server process [29532]
2025-07-11 13:34:35,505 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:34:35,794 - uvicorn.error - INFO - Started server process [12904]
2025-07-11 13:34:35,795 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:34:39,582 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:34:39,878 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:34:45,030 - uvicorn.error - INFO - Shutting down
2025-07-11 13:34:45,145 - uvicorn.error - INFO - Shutting down
lication shutdown.
2025-07-11 13:34:45,146 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:34:45,146 - uvicorn.error - INFO - Finished server process [29532]
2025-07-11 13:34:45,262 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:34:45,262 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:34:45,262 - uvicorn.error - INFO - Finished server process [12904]
2025-07-11 13:34:48,739 - uvicorn.error - INFO - Started server process [24664]
2025-07-11 13:34:48,739 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:34:48,982 - uvicorn.error - INFO - Started server process [8456]
2025-07-11 13:34:48,983 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:34:52,810 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:34:53,040 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:35:13,248 - uvicorn.error - INFO - Shutting down
2025-07-11 13:35:13,349 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:35:13,350 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:35:13,350 - uvicorn.error - INFO - Finished server process [8456]
2025-07-11 13:35:13,461 - uvicorn.error - INFO - Shutting down
2025-07-11 13:35:13,562 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:35:13,562 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:35:13,562 - uvicorn.error - INFO - Finished server process [24664]
2025-07-11 13:35:17,352 - uvicorn.error - INFO - Started server process [7056]
2025-07-11 13:35:17,352 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:35:17,353 - uvicorn.error - INFO - Started server process [20080]
2025-07-11 13:35:17,353 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:35:21,425 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:35:21,456 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:35:21,779 - uvicorn.error - INFO - Shutting down
2025-07-11 13:35:21,834 - uvicorn.error - INFO - Shutting down
2025-07-11 13:35:21,887 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:35:21,887 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:35:21,887 - uvicorn.error - INFO - Finished server process [7056]
2025-07-11 13:35:21,945 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:35:21,945 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:35:21,945 - uvicorn.error - INFO - Finished server process [20080]
2025-07-11 13:35:25,409 - uvicorn.error - INFO - Started server process [29464]
2025-07-11 13:35:25,409 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:35:25,696 - uvicorn.error - INFO - Started server process [29404]
2025-07-11 13:35:25,696 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:35:29,479 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:35:29,479 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:35:29,766 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:35:29,766 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:35:33,263 - uvicorn.error - INFO - Started server process [2412]
2025-07-11 13:35:33,263 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:35:33,525 - uvicorn.error - INFO - Started server process [2740]
2025-07-11 13:35:33,533 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:35:37,330 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:35:37,336 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:35:37,607 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:35:37,618 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:35:41,393 - uvicorn.error - INFO - Started server process [29432]
2025-07-11 13:35:41,394 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:35:41,777 - uvicorn.error - INFO - Started server process [27856]
2025-07-11 13:35:41,777 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:35:45,456 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:35:45,463 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:35:45,844 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:35:45,863 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:35:49,673 - uvicorn.error - INFO - Started server process [29508]
2025-07-11 13:35:49,674 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:35:50,341 - uvicorn.error - INFO - Started server process [26972]
2025-07-11 13:35:50,342 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:35:53,766 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:35:54,423 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:36:24,911 - uvicorn.error - INFO - Shutting down
2025-07-11 13:36:25,026 - uvicorn.error - INFO - Shutting down
lication shutdown.
2025-07-11 13:36:25,026 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:36:25,026 - uvicorn.error - INFO - Finished server process [26972]
2025-07-11 13:36:25,135 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:36:25,135 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:36:25,135 - uvicorn.error - INFO - Finished server process [29508]
2025-07-11 13:36:28,894 - uvicorn.error - INFO - Started server process [12676]
2025-07-11 13:36:28,894 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:36:28,994 - uvicorn.error - INFO - Started server process [20912]
2025-07-11 13:36:28,994 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:36:32,958 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:36:32,966 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:36:33,066 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:36:33,074 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:36:36,989 - uvicorn.error - INFO - Started server process [11780]
2025-07-11 13:36:36,989 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:36:37,270 - uvicorn.error - INFO - Started server process [10980]
2025-07-11 13:36:37,270 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:36:41,064 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:36:41,069 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:36:41,360 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:36:41,372 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:36:44,681 - uvicorn.error - INFO - Started server process [10188]
2025-07-11 13:36:44,681 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:36:45,071 - uvicorn.error - INFO - Started server process [24912]
2025-07-11 13:36:45,071 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:36:48,777 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:36:48,785 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:36:49,125 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:36:49,131 - uvicorn.error - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 628, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 595, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1881, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-11 13:36:52,436 - uvicorn.error - INFO - Started server process [28468]
2025-07-11 13:36:52,436 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:36:53,055 - uvicorn.error - INFO - Started server process [9160]
2025-07-11 13:36:53,055 - uvicorn.error - INFO - Waiting for application startup.
2025-07-11 13:36:56,505 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:36:57,136 - uvicorn.error - INFO - Application startup complete.
2025-07-11 13:38:51,368 - uvicorn.error - INFO - Shutting down
2025-07-11 13:38:51,479 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:38:51,479 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:38:51,480 - uvicorn.error - INFO - Finished server process [9160]
2025-07-11 13:38:54,583 - uvicorn.error - INFO - Shutting down
2025-07-11 13:38:54,694 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-11 13:38:54,695 - uvicorn.error - INFO - Application shutdown complete.
2025-07-11 13:38:54,695 - uvicorn.error - INFO - Finished server process [28468]
2025-07-12 18:25:52,665 - uvicorn.error - INFO - Started server process [44016]
2025-07-12 18:25:52,666 - uvicorn.error - INFO - Waiting for application startup.
2025-07-12 18:25:56,743 - uvicorn.error - INFO - Application startup complete.
2025-07-14 10:46:12,062 - uvicorn.error - INFO - Started server process [16652]
2025-07-14 10:46:12,063 - uvicorn.error - INFO - Waiting for application startup.
2025-07-14 10:46:16,139 - uvicorn.error - INFO - Application startup complete.
2025-07-14 13:56:43,389 - uvicorn.error - INFO - Started server process [18460]
2025-07-14 13:56:43,390 - uvicorn.error - INFO - Waiting for application startup.
2025-07-14 13:56:47,460 - uvicorn.error - INFO - Application startup complete.
2025-07-14 14:01:47,407 - uvicorn.error - INFO - Shutting down
2025-07-14 14:01:47,510 - uvicorn.error - INFO - Waiting for application shutdown.
2025-07-14 14:01:47,510 - uvicorn.error - INFO - Application shutdown complete.
2025-07-14 14:01:47,510 - uvicorn.error - INFO - Finished server process [18460]
