2025-07-04 12:31:52,533 - auth - INFO - Created new user: adminuser
2025-07-04 12:31:53,843 - auth - INFO - Created new user: annouser
2025-07-04 12:31:54,327 - auth - INFO - Created new user: audituser
2025-07-04 12:31:54,792 - auth - INFO - Created new user: testuser
2025-07-04 12:31:55,439 - auth - INFO - Created new user: changepw
2025-07-04 12:31:55,860 - auth - INFO - Created new user: a
2025-07-04 12:31:56,075 - auth - INFO - Created new user: minpw
2025-07-04 12:31:56,299 - auth - INFO - Created new user: clientuser
2025-07-04 12:31:56,748 - auth - INFO - Created new user: concurrentuser
2025-07-04 12:31:58,268 - auth - INFO - Created new user: kbadmin
2025-07-04 12:31:58,842 - auth - INFO - Created new user: supuser
2025-07-04 12:31:59,307 - auth - INFO - Created new user: synthadmin
2025-07-04 12:31:59,790 - auth - INFO - Created new user: tgadmin
2025-07-04 12:53:36,043 - auth - INFO - Created new user: adminuser
2025-07-04 12:55:06,862 - auth - INFO - Created new user: annouser
2025-07-04 12:55:31,760 - auth - INFO - Created new user: audituser
2025-07-04 12:55:44,383 - auth - INFO - Created new user: testuser
2025-07-04 12:55:44,946 - auth - INFO - Created new user: changepw
2025-07-04 12:55:45,366 - auth - INFO - Created new user: a
2025-07-04 12:55:45,579 - auth - INFO - Created new user: minpw
2025-07-04 12:55:49,885 - auth - INFO - Created new user: clientuser
2025-07-04 12:55:54,361 - auth - INFO - Created new user: concurrentuser
2025-07-04 12:55:55,213 - auth - INFO - Created new user: kbadmin
2025-07-04 12:55:59,852 - auth - INFO - Created new user: supuser
2025-07-04 12:56:08,494 - auth - INFO - Created new user: synthadmin
2025-07-04 12:56:09,241 - auth - INFO - Created new user: tgadmin
2025-07-04 13:00:15,894 - auth - INFO - Created new user: adminuser
2025-07-04 13:00:21,764 - auth - INFO - Created new user: adminuser
2025-07-04 13:00:27,026 - auth - INFO - Created new user: adminuser
2025-07-04 13:00:32,302 - auth - INFO - Created new user: adminuser
2025-07-04 13:00:37,832 - auth - INFO - Created new user: adminuser
2025-07-04 13:00:43,133 - auth - INFO - Created new user: adminuser
2025-07-04 13:00:48,514 - auth - INFO - Created new user: adminuser
2025-07-04 13:00:53,929 - auth - INFO - Created new user: adminuser
2025-07-04 13:00:59,235 - auth - INFO - Created new user: adminuser
2025-07-04 13:01:04,633 - auth - INFO - Created new user: adminuser
2025-07-04 13:01:09,968 - auth - INFO - Created new user: adminuser
2025-07-04 13:01:15,411 - auth - INFO - Created new user: adminuser
2025-07-04 13:01:21,954 - auth - INFO - Created new user: adminuser
2025-07-04 13:01:27,334 - auth - INFO - Created new user: adminuser
2025-07-04 13:01:32,593 - auth - INFO - Created new user: adminuser
2025-07-04 13:01:37,977 - auth - INFO - Created new user: adminuser
2025-07-04 13:01:43,123 - auth - INFO - Created new user: adminuser
2025-07-04 13:01:48,266 - auth - INFO - Created new user: adminuser
2025-07-04 13:01:53,552 - auth - INFO - Created new user: adminuser
2025-07-04 13:01:58,837 - auth - INFO - Created new user: adminuser
2025-07-04 13:02:04,129 - auth - INFO - Created new user: adminuser
2025-07-04 13:02:13,385 - auth - INFO - Created new user: annouser
2025-07-04 13:02:18,664 - auth - INFO - Created new user: annouser
2025-07-04 13:02:23,913 - auth - INFO - Created new user: annouser
2025-07-04 13:02:29,215 - auth - INFO - Created new user: annouser
2025-07-04 13:02:34,544 - auth - INFO - Created new user: annouser
2025-07-04 13:02:39,798 - auth - INFO - Created new user: annouser
2025-07-04 13:02:45,169 - auth - INFO - Created new user: audituser
2025-07-04 13:02:50,479 - auth - INFO - Created new user: audituser
2025-07-04 13:02:55,824 - auth - INFO - Created new user: audituser
2025-07-04 13:03:01,119 - auth - INFO - Created new user: audituser
2025-07-04 13:03:01,530 - auth - INFO - Created new user: testuser
2025-07-04 13:03:02,121 - auth - INFO - Created new user: changepw
2025-07-04 13:03:02,533 - auth - INFO - Created new user: a
2025-07-04 13:03:02,748 - auth - INFO - Created new user: minpw
2025-07-04 13:03:07,882 - auth - INFO - Created new user: clientuser
2025-07-04 13:03:13,088 - auth - INFO - Created new user: clientuser
2025-07-04 13:03:13,507 - auth - INFO - Created new user: concurrentuser
2025-07-04 13:03:16,348 - auth - INFO - Created new user: kbadmin
2025-07-04 13:03:21,960 - auth - INFO - Created new user: supuser
2025-07-04 13:03:27,230 - auth - INFO - Created new user: supuser
2025-07-04 13:03:32,634 - auth - INFO - Created new user: supuser
2025-07-04 13:03:33,045 - auth - INFO - Created new user: synthadmin
2025-07-04 13:03:33,713 - auth - INFO - Created new user: tgadmin
2025-07-04 13:10:08,404 - auth - INFO - Created new user: adminuser
2025-07-04 13:10:14,062 - auth - INFO - Created new user: adminuser
2025-07-04 13:10:19,063 - auth - INFO - Created new user: adminuser
2025-07-04 13:10:24,183 - auth - INFO - Created new user: adminuser
2025-07-04 13:10:29,232 - auth - INFO - Created new user: adminuser
2025-07-04 13:10:34,379 - auth - INFO - Created new user: adminuser
2025-07-04 13:10:39,648 - auth - INFO - Created new user: adminuser
2025-07-04 13:10:44,828 - auth - INFO - Created new user: adminuser
2025-07-04 13:10:49,888 - auth - INFO - Created new user: adminuser
2025-07-04 13:10:55,115 - auth - INFO - Created new user: adminuser
2025-07-04 13:11:00,538 - auth - INFO - Created new user: adminuser
2025-07-04 13:11:05,765 - auth - INFO - Created new user: adminuser
2025-07-04 13:11:11,081 - auth - INFO - Created new user: adminuser
2025-07-04 13:11:16,456 - auth - INFO - Created new user: adminuser
2025-07-04 13:11:21,800 - auth - INFO - Created new user: adminuser
2025-07-04 13:11:27,153 - auth - INFO - Created new user: adminuser
2025-07-04 13:11:32,555 - auth - INFO - Created new user: adminuser
2025-07-04 13:11:37,872 - auth - INFO - Created new user: adminuser
2025-07-04 13:11:43,130 - auth - INFO - Created new user: adminuser
2025-07-04 13:11:48,341 - auth - INFO - Created new user: adminuser
2025-07-04 13:11:53,631 - auth - INFO - Created new user: adminuser
2025-07-04 13:12:02,892 - auth - INFO - Created new user: annouser
2025-07-04 13:12:08,116 - auth - INFO - Created new user: annouser
2025-07-04 13:12:13,366 - auth - INFO - Created new user: annouser
2025-07-04 13:12:18,555 - auth - INFO - Created new user: annouser
2025-07-04 13:12:23,811 - auth - INFO - Created new user: annouser
2025-07-04 13:12:29,026 - auth - INFO - Created new user: annouser
2025-07-04 13:12:34,254 - auth - INFO - Created new user: audituser
2025-07-04 13:12:39,539 - auth - INFO - Created new user: audituser
2025-07-04 13:12:44,776 - auth - INFO - Created new user: audituser
2025-07-04 13:12:50,108 - auth - INFO - Created new user: audituser
2025-07-04 13:12:50,516 - auth - INFO - Created new user: testuser
2025-07-04 13:12:51,200 - auth - INFO - Created new user: changepw
2025-07-04 13:12:51,608 - auth - INFO - Created new user: a
2025-07-04 13:12:51,820 - auth - INFO - Created new user: minpw
2025-07-04 13:12:56,980 - auth - INFO - Created new user: clientuser
2025-07-04 13:13:02,299 - auth - INFO - Created new user: clientuser
2025-07-04 13:13:02,703 - auth - INFO - Created new user: concurrentuser
2025-07-04 13:13:05,078 - auth - INFO - Created new user: kbadmin
2025-07-04 13:13:10,499 - auth - INFO - Created new user: supuser
2025-07-04 13:13:16,509 - auth - INFO - Created new user: supuser
2025-07-04 13:13:21,586 - auth - INFO - Created new user: supuser
2025-07-04 13:13:21,992 - auth - INFO - Created new user: synthadmin
2025-07-04 13:13:22,758 - auth - INFO - Created new user: tgadmin
2025-07-04 13:17:19,214 - auth - INFO - Created new user: adminuser
2025-07-04 13:17:24,889 - auth - INFO - Created new user: adminuser
2025-07-04 13:17:30,068 - auth - INFO - Created new user: adminuser
2025-07-04 13:17:35,146 - auth - INFO - Created new user: adminuser
2025-07-04 13:17:40,195 - auth - INFO - Created new user: adminuser
2025-07-04 13:17:45,234 - auth - INFO - Created new user: adminuser
2025-07-04 13:17:50,560 - auth - INFO - Created new user: adminuser
2025-07-04 13:17:55,673 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:00,729 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:05,820 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:11,102 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:16,269 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:21,617 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:26,641 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:31,687 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:36,813 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:42,030 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:47,370 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:52,608 - auth - INFO - Created new user: adminuser
2025-07-04 13:18:57,901 - auth - INFO - Created new user: adminuser
2025-07-04 13:19:02,992 - auth - INFO - Created new user: adminuser
2025-07-04 13:23:17,504 - auth - INFO - Created new user: adminuser
2025-07-04 13:23:23,433 - auth - INFO - Created new user: adminuser
2025-07-04 13:23:28,597 - auth - INFO - Created new user: adminuser
2025-07-04 13:23:33,818 - auth - INFO - Created new user: adminuser
2025-07-04 13:23:39,362 - auth - INFO - Created new user: adminuser
2025-07-04 13:23:44,900 - auth - INFO - Created new user: adminuser
2025-07-04 13:23:50,560 - auth - INFO - Created new user: adminuser
2025-07-04 13:23:56,334 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:01,906 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:06,977 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:11,961 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:16,946 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:22,422 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:27,609 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:32,984 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:38,352 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:43,584 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:48,874 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:54,155 - auth - INFO - Created new user: adminuser
2025-07-04 13:24:59,313 - auth - INFO - Created new user: adminuser
2025-07-04 13:25:04,639 - auth - INFO - Created new user: adminuser
2025-07-04 13:28:14,719 - auth - INFO - Created new user: adminuser
2025-07-04 13:28:20,586 - auth - INFO - Created new user: adminuser
2025-07-04 13:28:25,702 - auth - INFO - Created new user: adminuser
2025-07-04 13:28:30,721 - auth - INFO - Created new user: adminuser
2025-07-04 13:28:35,791 - auth - INFO - Created new user: adminuser
2025-07-04 13:28:40,820 - auth - INFO - Created new user: adminuser
2025-07-04 13:28:46,179 - auth - INFO - Created new user: adminuser
2025-07-04 13:28:51,319 - auth - INFO - Created new user: adminuser
2025-07-04 13:28:56,541 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:01,879 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:07,151 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:12,321 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:17,480 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:22,836 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:27,812 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:32,890 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:37,962 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:43,092 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:48,151 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:53,371 - auth - INFO - Created new user: adminuser
2025-07-04 13:29:58,395 - auth - INFO - Created new user: adminuser
2025-07-04 13:39:22,054 - auth - INFO - Created new user: adminuser
2025-07-04 13:39:27,673 - auth - INFO - Created new user: adminuser
2025-07-04 13:39:33,443 - auth - INFO - Created new user: adminuser
2025-07-04 13:39:33,817 - auth - INFO - Created new user: newuser
2025-07-04 13:39:38,711 - auth - INFO - Created new user: adminuser
2025-07-04 13:39:39,069 - auth - INFO - Created new user: a
2025-07-04 13:39:39,259 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu', 'A', '<EMAIL>', 'annotator', '$2b$12$Wwwle1dNvhcMT6vYN3ZLBuZmyqRnIaoJNWGu3qvOeblRZ.6oo7Stm', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-04 13:39:44,480 - auth - INFO - Created new user: adminuser
2025-07-04 13:39:49,531 - auth - INFO - Created new user: adminuser
2025-07-04 13:39:58,634 - auth - INFO - Created new user: adminuser
2025-07-04 13:40:03,769 - auth - INFO - Created new user: adminuser
2025-07-04 13:40:08,917 - auth - INFO - Created new user: adminuser
2025-07-04 13:40:14,039 - auth - INFO - Created new user: adminuser
2025-07-04 13:40:19,198 - auth - INFO - Created new user: adminuser
2025-07-04 13:40:24,402 - auth - INFO - Created new user: adminuser
2025-07-04 13:40:29,615 - auth - INFO - Created new user: adminuser
2025-07-04 13:40:34,823 - auth - INFO - Created new user: adminuser
2025-07-04 13:40:40,174 - auth - INFO - Created new user: adminuser
2025-07-04 13:40:45,397 - auth - INFO - Created new user: adminuser
2025-07-04 13:40:50,553 - auth - INFO - Created new user: adminuser
2025-07-04 13:40:55,772 - auth - INFO - Created new user: adminuser
2025-07-04 13:41:00,972 - auth - INFO - Created new user: adminuser
2025-07-04 13:41:06,185 - auth - INFO - Created new user: adminuser
2025-07-04 13:41:11,361 - auth - INFO - Created new user: adminuser
2025-07-04 13:45:44,699 - auth - INFO - Created new user: annouser
2025-07-04 13:45:49,998 - auth - INFO - Created new user: annouser
2025-07-04 13:45:55,021 - auth - INFO - Created new user: annouser
2025-07-04 13:46:00,131 - auth - INFO - Created new user: annouser
2025-07-04 13:46:05,298 - auth - INFO - Created new user: annouser
2025-07-04 13:46:10,471 - auth - INFO - Created new user: annouser
2025-07-04 13:47:40,062 - auth - INFO - Created new user: audituser
2025-07-04 13:47:45,370 - auth - INFO - Created new user: audituser
2025-07-04 13:47:50,469 - auth - INFO - Created new user: audituser
2025-07-04 13:47:55,879 - auth - INFO - Created new user: audituser
2025-07-04 13:51:23,639 - auth - INFO - Created new user: testuser
2025-07-04 13:51:24,703 - auth - INFO - Created new user: changepw
2025-07-04 13:51:25,268 - auth - INFO - Updated password for user: changepw
2025-07-04 13:51:25,665 - auth - INFO - Created new user: a
2025-07-04 13:51:25,888 - auth - INFO - Created new user: minpw
2025-07-04 13:52:38,168 - auth - INFO - Created new user: clientuser
2025-07-04 13:52:43,471 - auth - INFO - Created new user: clientuser
2025-07-04 13:54:33,615 - auth - INFO - Created new user: adminuser
2025-07-04 13:54:38,915 - auth - INFO - Created new user: adminuser
2025-07-04 13:54:43,981 - auth - INFO - Created new user: adminuser
2025-07-04 13:54:44,367 - auth - INFO - Created new user: newuser
2025-07-04 13:54:49,230 - auth - INFO - Created new user: adminuser
2025-07-04 13:54:49,644 - auth - INFO - Created new user: a
2025-07-04 13:54:49,859 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 13:54:50,064 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 13:54:55,610 - auth - INFO - Created new user: adminuser
2025-07-04 13:55:01,117 - auth - INFO - Created new user: adminuser
2025-07-04 13:55:10,233 - auth - INFO - Created new user: adminuser
2025-07-04 13:55:15,300 - auth - INFO - Created new user: adminuser
2025-07-04 13:55:20,836 - auth - INFO - Created new user: adminuser
2025-07-04 13:55:25,914 - auth - INFO - Created new user: adminuser
2025-07-04 13:55:30,946 - auth - INFO - Created new user: adminuser
2025-07-04 13:55:35,995 - auth - INFO - Created new user: adminuser
2025-07-04 13:55:41,009 - auth - INFO - Created new user: adminuser
2025-07-04 13:55:46,045 - auth - INFO - Created new user: adminuser
2025-07-04 13:55:51,091 - auth - INFO - Created new user: adminuser
2025-07-04 13:55:56,701 - auth - INFO - Created new user: adminuser
2025-07-04 13:56:02,057 - auth - INFO - Created new user: adminuser
2025-07-04 13:56:07,381 - auth - INFO - Created new user: adminuser
2025-07-04 13:56:12,626 - auth - INFO - Created new user: adminuser
2025-07-04 13:56:17,862 - auth - INFO - Created new user: adminuser
2025-07-04 13:56:22,956 - auth - INFO - Created new user: adminuser
2025-07-04 13:57:55,606 - auth - INFO - Created new user: adminuser
2025-07-04 13:58:00,862 - auth - INFO - Created new user: adminuser
2025-07-04 13:58:05,989 - auth - INFO - Created new user: adminuser
2025-07-04 13:58:06,361 - auth - INFO - Created new user: newuser
2025-07-04 13:58:11,614 - auth - INFO - Created new user: adminuser
2025-07-04 13:58:11,982 - auth - INFO - Created new user: a
2025-07-04 13:58:12,163 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 13:58:12,339 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 13:58:17,276 - auth - INFO - Created new user: adminuser
2025-07-04 13:58:22,830 - auth - INFO - Created new user: adminuser
2025-07-04 13:58:32,309 - auth - INFO - Created new user: adminuser
2025-07-04 13:58:37,326 - auth - INFO - Created new user: adminuser
2025-07-04 13:58:42,535 - auth - INFO - Created new user: adminuser
2025-07-04 13:58:47,627 - auth - INFO - Created new user: adminuser
2025-07-04 13:58:52,797 - auth - INFO - Created new user: adminuser
2025-07-04 13:58:57,787 - auth - INFO - Created new user: adminuser
2025-07-04 13:59:02,913 - auth - INFO - Created new user: adminuser
2025-07-04 13:59:08,315 - auth - INFO - Created new user: adminuser
2025-07-04 13:59:13,598 - auth - INFO - Created new user: adminuser
2025-07-04 13:59:19,069 - auth - INFO - Created new user: adminuser
2025-07-04 13:59:24,333 - auth - INFO - Created new user: adminuser
2025-07-04 13:59:29,816 - auth - INFO - Created new user: adminuser
2025-07-04 13:59:35,167 - auth - INFO - Created new user: adminuser
2025-07-04 13:59:40,584 - auth - INFO - Created new user: adminuser
2025-07-04 13:59:45,930 - auth - INFO - Created new user: adminuser
2025-07-04 14:07:33,000 - auth - INFO - Created new user: adminuser
2025-07-04 14:07:38,387 - auth - INFO - Created new user: adminuser
2025-07-04 14:07:43,516 - auth - INFO - Created new user: adminuser
2025-07-04 14:07:43,914 - auth - INFO - Created new user: newuser
2025-07-04 14:07:48,795 - auth - INFO - Created new user: adminuser
2025-07-04 14:07:49,153 - auth - INFO - Created new user: a
2025-07-04 14:07:49,344 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 14:07:49,540 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 14:07:54,731 - auth - INFO - Created new user: adminuser
2025-07-04 14:07:59,917 - auth - INFO - Created new user: adminuser
2025-07-04 14:08:09,318 - auth - INFO - Created new user: adminuser
2025-07-04 14:08:14,426 - auth - INFO - Created new user: adminuser
2025-07-04 14:08:19,631 - auth - INFO - Created new user: adminuser
2025-07-04 14:08:24,733 - auth - INFO - Created new user: adminuser
2025-07-04 14:08:30,100 - auth - INFO - Created new user: adminuser
2025-07-04 14:08:35,082 - auth - INFO - Created new user: adminuser
2025-07-04 14:08:40,111 - auth - INFO - Created new user: adminuser
2025-07-04 14:08:45,375 - auth - INFO - Created new user: adminuser
2025-07-04 14:08:50,642 - auth - INFO - Created new user: adminuser
2025-07-04 14:08:55,910 - auth - INFO - Created new user: adminuser
2025-07-04 14:09:01,109 - auth - INFO - Created new user: adminuser
2025-07-04 14:09:06,403 - auth - INFO - Created new user: adminuser
2025-07-04 14:09:11,465 - auth - INFO - Created new user: adminuser
2025-07-04 14:09:16,735 - auth - INFO - Created new user: adminuser
2025-07-04 14:09:22,081 - auth - INFO - Created new user: adminuser
2025-07-04 14:09:31,542 - auth - INFO - Created new user: annouser
2025-07-04 14:09:36,908 - auth - INFO - Created new user: annouser
2025-07-04 14:09:42,443 - auth - INFO - Created new user: annouser
2025-07-04 14:09:47,867 - auth - INFO - Created new user: annouser
2025-07-04 14:09:52,903 - auth - INFO - Created new user: annouser
2025-07-04 14:09:57,991 - auth - INFO - Created new user: annouser
2025-07-04 14:10:03,108 - auth - INFO - Created new user: audituser
2025-07-04 14:10:08,369 - auth - INFO - Created new user: audituser
2025-07-04 14:10:13,696 - auth - INFO - Created new user: audituser
2025-07-04 14:10:18,948 - auth - INFO - Created new user: audituser
2025-07-04 14:10:19,480 - auth - INFO - Created new user: testuser
2025-07-04 14:10:20,863 - auth - INFO - Created new user: changepw
2025-07-04 14:10:21,486 - auth - INFO - Updated password for user: changepw
2025-07-04 14:10:21,914 - auth - INFO - Created new user: a
2025-07-04 14:10:22,146 - auth - INFO - Created new user: minpw
2025-07-04 14:10:27,143 - auth - INFO - Created new user: clientuser
2025-07-04 14:10:32,403 - auth - INFO - Created new user: clientuser
2025-07-04 14:10:32,853 - auth - INFO - Created new user: concurrentuser
2025-07-04 14:10:35,615 - auth - INFO - Created new user: kbadmin
2025-07-04 14:10:41,907 - auth - INFO - Created new user: supuser
2025-07-04 14:10:47,203 - auth - INFO - Created new user: supuser
2025-07-04 14:10:52,520 - auth - INFO - Created new user: supuser
2025-07-04 14:10:52,920 - auth - INFO - Created new user: synthadmin
2025-07-04 14:10:53,662 - auth - INFO - Created new user: tgadmin
2025-07-04 14:58:36,213 - auth - INFO - Created new user: adminuser
2025-07-04 14:58:41,433 - auth - INFO - Created new user: adminuser
2025-07-04 14:58:46,615 - auth - INFO - Created new user: adminuser
2025-07-04 14:58:47,003 - auth - INFO - Created new user: newuser
2025-07-04 14:58:52,145 - auth - INFO - Created new user: adminuser
2025-07-04 14:58:52,503 - auth - INFO - Created new user: a
2025-07-04 14:58:52,691 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 14:58:52,876 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 14:58:57,835 - auth - INFO - Created new user: adminuser
2025-07-04 14:59:02,905 - auth - INFO - Created new user: adminuser
2025-07-04 14:59:12,502 - auth - INFO - Created new user: adminuser
2025-07-04 14:59:17,669 - auth - INFO - Created new user: adminuser
2025-07-04 14:59:22,709 - auth - INFO - Created new user: adminuser
2025-07-04 14:59:27,763 - auth - INFO - Created new user: adminuser
2025-07-04 14:59:33,017 - auth - INFO - Created new user: adminuser
2025-07-04 14:59:38,171 - auth - INFO - Created new user: adminuser
2025-07-04 14:59:43,546 - auth - INFO - Created new user: adminuser
2025-07-04 14:59:49,058 - auth - INFO - Created new user: adminuser
2025-07-04 14:59:54,246 - auth - INFO - Created new user: adminuser
2025-07-04 14:59:59,562 - auth - INFO - Created new user: adminuser
2025-07-04 15:00:04,776 - auth - INFO - Created new user: adminuser
2025-07-04 15:00:10,041 - auth - INFO - Created new user: adminuser
2025-07-04 15:00:15,385 - auth - INFO - Created new user: adminuser
2025-07-04 15:00:20,754 - auth - INFO - Created new user: adminuser
2025-07-04 15:00:25,980 - auth - INFO - Created new user: adminuser
2025-07-04 15:00:35,380 - auth - INFO - Created new user: annouser
2025-07-04 15:00:40,652 - auth - INFO - Created new user: annouser
2025-07-04 15:00:45,918 - auth - INFO - Created new user: annouser
2025-07-04 15:00:50,978 - auth - INFO - Created new user: annouser
2025-07-04 15:00:56,154 - auth - INFO - Created new user: annouser
2025-07-04 15:01:01,522 - auth - INFO - Created new user: annouser
2025-07-04 15:01:06,838 - auth - INFO - Created new user: audituser
2025-07-04 15:01:12,098 - auth - INFO - Created new user: audituser
2025-07-04 15:01:17,438 - auth - INFO - Created new user: audituser
2025-07-04 15:01:22,884 - auth - INFO - Created new user: audituser
2025-07-04 15:01:23,326 - auth - INFO - Created new user: testuser
2025-07-04 15:01:24,348 - auth - INFO - Created new user: changepw
2025-07-04 15:01:24,933 - auth - INFO - Updated password for user: changepw
2025-07-04 15:01:25,332 - auth - INFO - Created new user: a
2025-07-04 15:01:25,560 - auth - INFO - Created new user: minpw
2025-07-04 15:01:30,738 - auth - INFO - Created new user: clientuser
2025-07-04 15:01:35,927 - auth - INFO - Created new user: clientuser
2025-07-04 15:01:36,338 - auth - INFO - Created new user: concurrentuser
2025-07-04 15:01:38,294 - auth - INFO - Created new user: auser
2025-07-04 15:01:38,974 - auth - INFO - Created new user: kbadmin
2025-07-04 15:01:45,069 - auth - INFO - Created new user: supuser
2025-07-04 15:01:50,339 - auth - INFO - Created new user: supuser
2025-07-04 15:01:55,623 - auth - INFO - Created new user: supuser
2025-07-04 15:01:56,047 - auth - INFO - Created new user: synthadmin
2025-07-04 15:01:56,808 - auth - INFO - Created new user: tgadmin
2025-07-04 15:16:27,245 - auth - INFO - Created new user: adminuser
2025-07-04 15:16:32,727 - auth - INFO - Created new user: adminuser
2025-07-04 15:16:37,987 - auth - INFO - Created new user: adminuser
2025-07-04 15:16:38,354 - auth - INFO - Created new user: newuser
2025-07-04 15:16:43,416 - auth - INFO - Created new user: adminuser
2025-07-04 15:16:43,798 - auth - INFO - Created new user: a
2025-07-04 15:16:43,987 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 15:16:44,162 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 15:16:49,308 - auth - INFO - Created new user: adminuser
2025-07-04 15:16:54,605 - auth - INFO - Created new user: adminuser
2025-07-04 15:17:03,972 - auth - INFO - Created new user: adminuser
2025-07-04 15:17:09,452 - auth - INFO - Created new user: adminuser
2025-07-04 15:17:14,703 - auth - INFO - Created new user: adminuser
2025-07-04 15:17:19,971 - auth - INFO - Created new user: adminuser
2025-07-04 15:17:25,389 - auth - INFO - Created new user: adminuser
2025-07-04 15:17:30,480 - auth - INFO - Created new user: adminuser
2025-07-04 15:17:35,944 - auth - INFO - Created new user: adminuser
2025-07-04 15:17:41,310 - auth - INFO - Created new user: adminuser
2025-07-04 15:17:46,705 - auth - INFO - Created new user: adminuser
2025-07-04 15:17:52,069 - auth - INFO - Created new user: adminuser
2025-07-04 15:17:57,404 - auth - INFO - Created new user: adminuser
2025-07-04 15:18:02,750 - auth - INFO - Created new user: adminuser
2025-07-04 15:18:07,959 - auth - INFO - Created new user: adminuser
2025-07-04 15:18:13,299 - auth - INFO - Created new user: adminuser
2025-07-04 15:18:18,843 - auth - INFO - Created new user: adminuser
2025-07-04 15:18:28,139 - auth - INFO - Created new user: annouser
2025-07-04 15:18:33,583 - auth - INFO - Created new user: annouser
2025-07-04 15:18:38,954 - auth - INFO - Created new user: annouser
2025-07-04 15:18:44,166 - auth - INFO - Created new user: annouser
2025-07-04 15:18:49,739 - auth - INFO - Created new user: annouser
2025-07-04 15:18:55,103 - auth - INFO - Created new user: annouser
2025-07-04 15:19:00,856 - auth - INFO - Created new user: audituser
2025-07-04 15:19:06,773 - auth - INFO - Created new user: audituser
2025-07-04 15:19:12,671 - auth - INFO - Created new user: audituser
2025-07-04 15:19:18,574 - auth - INFO - Created new user: audituser
2025-07-04 15:19:24,499 - auth - INFO - Created new user: testuser
2025-07-04 15:19:41,910 - auth - INFO - Created new user: changepw
2025-07-04 15:19:42,453 - auth - INFO - Updated password for user: changepw
2025-07-04 15:19:48,170 - auth - INFO - Created new user: a
2025-07-04 15:19:53,160 - auth - INFO - Created new user: minpw
2025-07-04 15:19:58,114 - auth - INFO - Created new user: clientuser
2025-07-04 15:20:03,288 - auth - INFO - Created new user: clientuser
2025-07-04 15:20:03,704 - auth - INFO - Created new user: concurrentuser
2025-07-04 15:20:05,593 - auth - INFO - Created new user: auser
2025-07-04 15:20:05,804 - auth - INFO - Created new user: wpuser
2025-07-04 15:20:06,586 - auth - INFO - Created new user: kbadmin
2025-07-04 15:20:11,966 - auth - INFO - Created new user: supuser
2025-07-04 15:20:17,209 - auth - INFO - Created new user: supuser
2025-07-04 15:20:22,566 - auth - INFO - Created new user: supuser
2025-07-04 15:20:22,972 - auth - INFO - Created new user: synthadmin
2025-07-04 15:20:23,470 - auth - INFO - Created new user: tgadmin
2025-07-04 17:33:59,374 - auth - INFO - Created new user: adminuser
2025-07-04 17:34:04,668 - auth - INFO - Created new user: adminuser
2025-07-04 17:34:09,975 - auth - INFO - Created new user: adminuser
2025-07-04 17:34:10,356 - auth - INFO - Created new user: newuser
2025-07-04 17:34:15,355 - auth - INFO - Created new user: adminuser
2025-07-04 17:34:15,740 - auth - INFO - Created new user: a
2025-07-04 17:34:15,932 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 17:34:16,129 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-04 17:34:21,338 - auth - INFO - Created new user: adminuser
2025-07-04 17:34:26,588 - auth - INFO - Created new user: adminuser
2025-07-04 17:34:36,005 - auth - INFO - Created new user: adminuser
2025-07-04 17:34:41,355 - auth - INFO - Created new user: adminuser
2025-07-04 17:34:46,626 - auth - INFO - Created new user: adminuser
2025-07-04 17:34:51,995 - auth - INFO - Created new user: adminuser
2025-07-04 17:34:57,164 - auth - INFO - Created new user: adminuser
2025-07-04 17:35:02,516 - auth - INFO - Created new user: adminuser
2025-07-04 17:35:07,669 - auth - INFO - Created new user: adminuser
2025-07-04 17:35:13,100 - auth - INFO - Created new user: adminuser
2025-07-04 17:35:18,435 - auth - INFO - Created new user: adminuser
2025-07-04 17:35:23,794 - auth - INFO - Created new user: adminuser
2025-07-04 17:35:29,020 - auth - INFO - Created new user: adminuser
2025-07-04 17:35:34,252 - auth - INFO - Created new user: adminuser
2025-07-04 17:35:39,545 - auth - INFO - Created new user: adminuser
2025-07-04 17:35:44,961 - auth - INFO - Created new user: adminuser
2025-07-04 17:35:50,316 - auth - INFO - Created new user: adminuser
2025-07-04 17:35:59,675 - auth - INFO - Created new user: annouser
2025-07-04 17:36:04,989 - auth - INFO - Created new user: annouser
2025-07-04 17:36:10,183 - auth - INFO - Created new user: annouser
2025-07-04 17:36:15,392 - auth - INFO - Created new user: annouser
2025-07-04 17:36:20,875 - auth - INFO - Created new user: annouser
2025-07-04 17:36:26,226 - auth - INFO - Created new user: annouser
2025-07-04 17:36:31,513 - auth - INFO - Created new user: audituser
2025-07-04 17:36:36,666 - auth - INFO - Created new user: audituser
2025-07-04 17:36:42,036 - auth - INFO - Created new user: audituser
2025-07-04 17:36:47,359 - auth - INFO - Created new user: audituser
2025-07-04 17:36:52,815 - auth - INFO - Created new user: testuser
2025-07-04 17:37:07,838 - auth - INFO - Created new user: changepw
2025-07-04 17:37:08,408 - auth - INFO - Updated password for user: changepw
2025-07-04 17:37:13,920 - auth - INFO - Created new user: a
2025-07-04 17:37:18,983 - auth - INFO - Created new user: minpw
2025-07-04 17:37:24,211 - auth - INFO - Created new user: clientuser
2025-07-04 17:37:29,400 - auth - INFO - Created new user: clientuser
2025-07-04 17:37:29,826 - auth - INFO - Created new user: concurrentuser
2025-07-04 17:37:31,860 - auth - INFO - Created new user: auser
2025-07-04 17:37:32,081 - auth - INFO - Created new user: wpuser
2025-07-04 17:37:32,914 - auth - INFO - Created new user: kbadmin
2025-07-04 17:37:38,416 - auth - INFO - Created new user: supuser
2025-07-04 17:37:43,644 - auth - INFO - Created new user: supuser
2025-07-04 17:37:48,767 - auth - INFO - Created new user: supuser
2025-07-04 17:37:49,162 - auth - INFO - Created new user: synthadmin
2025-07-04 17:37:49,646 - auth - INFO - Created new user: tgadmin
2025-07-04 17:37:50,283 - auth - INFO - Created new user: authzanno
2025-07-04 17:37:50,709 - auth - INFO - Created new user: authzclient
2025-07-04 17:49:00,416 - auth - INFO - Created new user: auser
2025-07-04 17:49:30,126 - auth - INFO - Created new user: wpuser
2025-07-04 17:51:58,750 - auth - INFO - Created new user: kbadmin
2025-07-04 17:53:23,834 - auth - INFO - Created new user: supuser
2025-07-04 17:53:29,263 - auth - INFO - Created new user: supuser
2025-07-04 17:53:34,433 - auth - INFO - Created new user: supuser
2025-07-04 17:53:56,566 - auth - INFO - Created new user: synthadmin
2025-07-04 17:54:14,879 - auth - INFO - Created new user: tgadmin
2025-07-04 17:54:40,285 - auth - INFO - Created new user: testuser
2025-07-04 17:54:55,318 - auth - INFO - Created new user: changepw
2025-07-04 17:54:55,927 - auth - INFO - Updated password for user: changepw
2025-07-04 17:55:01,114 - auth - INFO - Created new user: a
2025-07-04 17:55:06,002 - auth - INFO - Created new user: minpw
2025-07-04 17:56:37,443 - auth - INFO - Created new user: authzanno
2025-07-04 17:56:38,341 - auth - INFO - Created new user: authzclient
2025-07-04 18:03:43,968 - auth - INFO - Created new user: wpuser
2025-07-04 18:14:54,741 - auth - INFO - Created new user: contractuser
2025-07-05 10:36:01,853 - auth - INFO - Created new user: contract_admin
2025-07-05 10:36:02,248 - auth - INFO - Created new user: contract_anno
2025-07-05 11:59:45,346 - auth - INFO - Created new user: adminuser
2025-07-05 11:59:50,939 - auth - INFO - Created new user: adminuser
2025-07-05 11:59:56,184 - auth - INFO - Created new user: adminuser
2025-07-05 11:59:56,615 - auth - INFO - Created new user: newuser
2025-07-05 12:00:01,750 - auth - INFO - Created new user: adminuser
2025-07-05 12:00:02,240 - auth - INFO - Created new user: a
2025-07-05 12:00:02,479 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 12:00:02,708 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 12:00:07,847 - auth - INFO - Created new user: adminuser
2025-07-05 12:00:13,095 - auth - INFO - Created new user: adminuser
2025-07-05 12:00:22,449 - auth - INFO - Created new user: adminuser
2025-07-05 12:00:27,643 - auth - INFO - Created new user: adminuser
2025-07-05 12:00:32,979 - auth - INFO - Created new user: adminuser
2025-07-05 12:00:38,253 - auth - INFO - Created new user: adminuser
2025-07-05 12:00:43,433 - auth - INFO - Created new user: adminuser
2025-07-05 12:00:48,617 - auth - INFO - Created new user: adminuser
2025-07-05 12:00:53,930 - auth - INFO - Created new user: adminuser
2025-07-05 12:00:59,125 - auth - INFO - Created new user: adminuser
2025-07-05 12:01:04,321 - auth - INFO - Created new user: adminuser
2025-07-05 12:01:09,663 - auth - INFO - Created new user: adminuser
2025-07-05 12:01:14,963 - auth - INFO - Created new user: adminuser
2025-07-05 12:01:20,474 - auth - INFO - Created new user: adminuser
2025-07-05 12:01:25,662 - auth - INFO - Created new user: adminuser
2025-07-05 12:01:30,783 - auth - INFO - Created new user: adminuser
2025-07-05 12:01:35,999 - auth - INFO - Created new user: adminuser
2025-07-05 12:01:47,151 - auth - INFO - Created new user: annouser
2025-07-05 12:01:53,378 - auth - INFO - Created new user: annouser
2025-07-05 12:01:59,379 - auth - INFO - Created new user: annouser
2025-07-05 12:02:05,437 - auth - INFO - Created new user: annouser
2025-07-05 12:02:11,672 - auth - INFO - Created new user: annouser
2025-07-05 12:02:17,648 - auth - INFO - Created new user: annouser
2025-07-05 12:02:23,661 - auth - INFO - Created new user: audituser
2025-07-05 12:02:29,644 - auth - INFO - Created new user: audituser
2025-07-05 12:02:35,387 - auth - INFO - Created new user: audituser
2025-07-05 12:02:40,691 - auth - INFO - Created new user: audituser
2025-07-05 12:02:45,999 - auth - INFO - Created new user: testuser
2025-07-05 12:03:00,981 - auth - INFO - Created new user: changepw
2025-07-05 12:03:01,533 - auth - INFO - Updated password for user: changepw
2025-07-05 12:03:06,854 - auth - INFO - Created new user: a
2025-07-05 12:03:11,862 - auth - INFO - Created new user: minpw
2025-07-05 12:03:19,236 - auth - INFO - Created new user: clientuser
2025-07-05 12:03:24,530 - auth - INFO - Created new user: clientuser
2025-07-05 12:03:24,973 - auth - INFO - Created new user: concurrentuser
2025-07-05 12:03:28,402 - auth - INFO - Created new user: auser
2025-07-05 12:03:31,667 - auth - INFO - Created new user: secureuser
2025-07-05 12:03:32,061 - auth - INFO - Created new user: perfuser
2025-07-05 12:03:32,530 - auth - INFO - Created new user: wpuser
2025-07-05 12:03:35,146 - auth - INFO - Created new user: kbadmin
2025-07-05 12:03:46,567 - auth - INFO - Created new user: supuser
2025-07-05 12:03:51,958 - auth - INFO - Created new user: supuser
2025-07-05 12:03:57,248 - auth - INFO - Created new user: supuser
2025-07-05 12:03:57,684 - auth - INFO - Created new user: synthadmin
2025-07-05 12:03:58,286 - auth - INFO - Created new user: tgadmin
2025-07-05 12:13:12,020 - auth - INFO - Created new user: adminuser
2025-07-05 12:13:17,675 - auth - INFO - Created new user: adminuser
2025-07-05 12:13:22,996 - auth - INFO - Created new user: adminuser
2025-07-05 12:13:23,377 - auth - INFO - Created new user: newuser
2025-07-05 12:13:28,343 - auth - INFO - Created new user: adminuser
2025-07-05 12:13:28,709 - auth - INFO - Created new user: a
2025-07-05 12:13:28,892 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 12:13:29,087 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 12:13:34,335 - auth - INFO - Created new user: adminuser
2025-07-05 12:13:39,492 - auth - INFO - Created new user: adminuser
2025-07-05 12:13:49,089 - auth - INFO - Created new user: adminuser
2025-07-05 12:13:54,498 - auth - INFO - Created new user: adminuser
2025-07-05 12:13:59,496 - auth - INFO - Created new user: adminuser
2025-07-05 12:14:04,674 - auth - INFO - Created new user: adminuser
2025-07-05 12:14:10,013 - auth - INFO - Created new user: adminuser
2025-07-05 12:14:15,055 - auth - INFO - Created new user: adminuser
2025-07-05 12:14:20,120 - auth - INFO - Created new user: adminuser
2025-07-05 12:14:25,176 - auth - INFO - Created new user: adminuser
2025-07-05 12:14:30,248 - auth - INFO - Created new user: adminuser
2025-07-05 12:14:35,342 - auth - INFO - Created new user: adminuser
2025-07-05 12:14:40,437 - auth - INFO - Created new user: adminuser
2025-07-05 12:14:45,467 - auth - INFO - Created new user: adminuser
2025-07-05 12:14:50,599 - auth - INFO - Created new user: adminuser
2025-07-05 12:14:55,897 - auth - INFO - Created new user: adminuser
2025-07-05 12:15:01,249 - auth - INFO - Created new user: adminuser
2025-07-05 12:15:12,170 - auth - INFO - Created new user: annouser
2025-07-05 12:15:17,447 - auth - INFO - Created new user: annouser
2025-07-05 12:15:22,831 - auth - INFO - Created new user: annouser
2025-07-05 12:15:28,433 - auth - INFO - Created new user: annouser
2025-07-05 12:15:33,534 - auth - INFO - Created new user: annouser
2025-07-05 12:15:38,649 - auth - INFO - Created new user: annouser
2025-07-05 12:15:43,739 - auth - INFO - Created new user: audituser
2025-07-05 12:15:48,809 - auth - INFO - Created new user: audituser
2025-07-05 12:15:53,874 - auth - INFO - Created new user: audituser
2025-07-05 12:15:58,921 - auth - INFO - Created new user: audituser
2025-07-05 12:16:03,996 - auth - INFO - Created new user: testuser
2025-07-05 12:16:18,449 - auth - INFO - Created new user: changepw
2025-07-05 12:16:19,019 - auth - INFO - Updated password for user: changepw
2025-07-05 12:16:24,109 - auth - INFO - Created new user: a
2025-07-05 12:16:28,939 - auth - INFO - Created new user: minpw
2025-07-05 12:16:31,739 - auth - INFO - Created new user: bulkadmin
2025-07-05 12:16:36,994 - auth - INFO - Created new user: clientuser
2025-07-05 12:16:41,999 - auth - INFO - Created new user: clientuser
2025-07-05 12:16:42,419 - auth - INFO - Created new user: concurrentuser
2025-07-05 12:16:45,649 - auth - INFO - Created new user: auser
2025-07-05 12:16:48,627 - auth - INFO - Created new user: secureuser
2025-07-05 12:16:49,031 - auth - INFO - Created new user: perfuser
2025-07-05 12:16:49,504 - auth - INFO - Created new user: wpuser
2025-07-05 12:16:51,539 - auth - INFO - Created new user: kbadmin
2025-07-05 12:16:53,314 - auth - INFO - Created new user: patchadmin
2025-07-05 12:17:02,229 - auth - INFO - Created new user: supuser
2025-07-05 12:17:07,339 - auth - INFO - Created new user: supuser
2025-07-05 12:17:12,369 - auth - INFO - Created new user: supuser
2025-07-05 12:17:12,797 - auth - INFO - Created new user: synthadmin
2025-07-05 12:17:13,319 - auth - INFO - Created new user: tgadmin
2025-07-05 12:24:46,572 - auth - INFO - Created new user: adminuser
2025-07-05 12:24:51,838 - auth - INFO - Created new user: adminuser
2025-07-05 12:24:57,749 - auth - INFO - Created new user: adminuser
2025-07-05 12:24:58,131 - auth - INFO - Created new user: newuser
2025-07-05 14:13:28,177 - auth - INFO - Created new user: adminuser
2025-07-05 14:13:33,414 - auth - INFO - Created new user: adminuser
2025-07-05 14:13:38,771 - auth - INFO - Created new user: adminuser
2025-07-05 14:13:39,149 - auth - INFO - Created new user: newuser
2025-07-05 14:13:44,073 - auth - INFO - Created new user: adminuser
2025-07-05 14:13:44,451 - auth - INFO - Created new user: a
2025-07-05 14:13:44,645 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 14:13:44,837 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 14:13:49,711 - auth - INFO - Created new user: adminuser
2025-07-05 14:13:55,047 - auth - INFO - Created new user: adminuser
2025-07-05 14:14:04,512 - auth - INFO - Created new user: adminuser
2025-07-05 14:14:09,701 - auth - INFO - Created new user: adminuser
2025-07-05 14:14:14,931 - auth - INFO - Created new user: adminuser
2025-07-05 14:14:20,184 - auth - INFO - Created new user: adminuser
2025-07-05 14:14:25,292 - auth - INFO - Created new user: adminuser
2025-07-05 14:14:30,480 - auth - INFO - Created new user: adminuser
2025-07-05 14:14:35,730 - auth - INFO - Created new user: adminuser
2025-07-05 14:14:40,742 - auth - INFO - Created new user: adminuser
2025-07-05 14:14:45,884 - auth - INFO - Created new user: adminuser
2025-07-05 14:14:50,967 - auth - INFO - Created new user: adminuser
2025-07-05 14:14:56,141 - auth - INFO - Created new user: adminuser
2025-07-05 14:15:01,563 - auth - INFO - Created new user: adminuser
2025-07-05 14:15:06,816 - auth - INFO - Created new user: adminuser
2025-07-05 14:15:12,003 - auth - INFO - Created new user: adminuser
2025-07-05 14:15:17,100 - auth - INFO - Created new user: adminuser
2025-07-05 14:15:26,368 - auth - INFO - Created new user: annouser
2025-07-05 14:15:33,010 - auth - INFO - Created new user: annouser
2025-07-05 14:15:39,520 - auth - INFO - Created new user: annouser
2025-07-05 14:15:45,692 - auth - INFO - Created new user: annouser
2025-07-05 14:15:51,140 - auth - INFO - Created new user: annouser
2025-07-05 14:15:56,432 - auth - INFO - Created new user: annouser
2025-07-05 14:16:01,740 - auth - INFO - Created new user: audituser
2025-07-05 14:16:07,044 - auth - INFO - Created new user: audituser
2025-07-05 14:16:12,320 - auth - INFO - Created new user: audituser
2025-07-05 14:16:17,670 - auth - INFO - Created new user: audituser
2025-07-05 14:16:23,188 - auth - INFO - Created new user: testuser
2025-07-05 14:16:38,660 - auth - INFO - Created new user: changepw
2025-07-05 14:16:39,284 - auth - INFO - Updated password for user: changepw
2025-07-05 14:16:44,523 - auth - INFO - Created new user: a
2025-07-05 14:16:49,652 - auth - INFO - Created new user: minpw
2025-07-05 14:16:58,252 - auth - INFO - Created new user: clientuser
2025-07-05 14:17:03,332 - auth - INFO - Created new user: clientuser
2025-07-05 14:17:04,465 - auth - INFO - Created new user: bulkadmin
2025-07-05 14:17:05,544 - auth - INFO - Created new user: bulkadmin
2025-07-05 14:17:08,892 - auth - INFO - Created new user: wpuser
2025-07-05 14:17:09,785 - auth - INFO - Created new user: kbadmin
2025-07-05 14:17:11,758 - auth - INFO - Created new user: perfuser
2025-07-05 14:17:19,575 - auth - INFO - Created new user: auser
2025-07-05 14:17:22,945 - auth - INFO - Created new user: secureuser
2025-07-05 14:17:25,870 - auth - INFO - Created new user: encuser
2025-07-05 14:17:31,120 - auth - INFO - Created new user: supuser
2025-07-05 14:17:37,020 - auth - INFO - Created new user: supuser
2025-07-05 14:17:42,852 - auth - INFO - Created new user: supuser
2025-07-05 14:17:43,290 - auth - INFO - Created new user: synthadmin
2025-07-05 14:17:43,910 - auth - INFO - Created new user: tgadmin
2025-07-05 14:17:44,352 - auth - INFO - Created new user: concurrentuser
2025-07-05 14:37:43,121 - auth - INFO - Created new user: adminuser
2025-07-05 14:37:48,474 - auth - INFO - Created new user: adminuser
2025-07-05 14:37:53,598 - auth - INFO - Created new user: adminuser
2025-07-05 14:37:53,985 - auth - INFO - Created new user: newuser
2025-07-05 14:37:59,220 - auth - INFO - Created new user: adminuser
2025-07-05 14:37:59,651 - auth - INFO - Created new user: a
2025-07-05 14:37:59,894 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 14:38:00,120 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 14:38:05,221 - auth - INFO - Created new user: adminuser
2025-07-05 14:38:10,592 - auth - INFO - Created new user: adminuser
2025-07-05 14:38:19,898 - auth - INFO - Created new user: adminuser
2025-07-05 14:38:25,172 - auth - INFO - Created new user: adminuser
2025-07-05 14:38:30,371 - auth - INFO - Created new user: adminuser
2025-07-05 14:38:35,637 - auth - INFO - Created new user: adminuser
2025-07-05 14:38:41,668 - auth - INFO - Created new user: adminuser
2025-07-05 14:38:46,888 - auth - INFO - Created new user: adminuser
2025-07-05 14:38:52,188 - auth - INFO - Created new user: adminuser
2025-07-05 14:38:57,421 - auth - INFO - Created new user: adminuser
2025-07-05 14:39:02,688 - auth - INFO - Created new user: adminuser
2025-07-05 14:39:07,858 - auth - INFO - Created new user: adminuser
2025-07-05 14:39:13,198 - auth - INFO - Created new user: adminuser
2025-07-05 14:39:18,471 - auth - INFO - Created new user: adminuser
2025-07-05 14:39:23,657 - auth - INFO - Created new user: adminuser
2025-07-05 14:39:28,897 - auth - INFO - Created new user: adminuser
2025-07-05 14:39:34,198 - auth - INFO - Created new user: adminuser
2025-07-05 14:39:43,522 - auth - INFO - Created new user: annouser
2025-07-05 14:39:48,671 - auth - INFO - Created new user: annouser
2025-07-05 14:39:53,838 - auth - INFO - Created new user: annouser
2025-07-05 14:39:59,072 - auth - INFO - Created new user: annouser
2025-07-05 14:40:04,288 - auth - INFO - Created new user: annouser
2025-07-05 14:40:09,620 - auth - INFO - Created new user: annouser
2025-07-05 14:40:14,938 - auth - INFO - Created new user: audituser
2025-07-05 14:40:20,148 - auth - INFO - Created new user: audituser
2025-07-05 14:40:25,353 - auth - INFO - Created new user: audituser
2025-07-05 14:40:30,528 - auth - INFO - Created new user: audituser
2025-07-05 14:40:35,720 - auth - INFO - Created new user: testuser
2025-07-05 14:40:50,798 - auth - INFO - Created new user: changepw
2025-07-05 14:40:51,388 - auth - INFO - Updated password for user: changepw
2025-07-05 14:40:56,570 - auth - INFO - Created new user: a
2025-07-05 14:41:01,614 - auth - INFO - Created new user: minpw
2025-07-05 14:41:10,458 - auth - INFO - Created new user: clientuser
2025-07-05 14:41:15,678 - auth - INFO - Created new user: clientuser
2025-07-05 14:41:16,828 - auth - INFO - Created new user: bulkadmin
2025-07-05 14:41:17,978 - auth - INFO - Created new user: bulkadmin
2025-07-05 14:41:21,288 - auth - INFO - Created new user: wpuser
2025-07-05 14:41:22,108 - auth - INFO - Created new user: kbadmin
2025-07-05 14:41:24,426 - auth - INFO - Created new user: perfuser
2025-07-05 14:41:33,470 - auth - INFO - Created new user: auser
2025-07-05 14:41:36,870 - auth - INFO - Created new user: secureuser
2025-07-05 14:41:40,237 - auth - INFO - Created new user: encuser
2025-07-05 14:41:45,538 - auth - INFO - Created new user: supuser
2025-07-05 14:41:50,838 - auth - INFO - Created new user: supuser
2025-07-05 14:41:56,198 - auth - INFO - Created new user: supuser
2025-07-05 14:41:56,638 - auth - INFO - Created new user: synthadmin
2025-07-05 14:41:57,188 - auth - INFO - Created new user: tgadmin
2025-07-05 14:41:57,621 - auth - INFO - Created new user: concurrentuser
2025-07-05 14:55:33,523 - auth - INFO - Created new user: secureuser
2025-07-05 14:55:36,589 - auth - INFO - Created new user: encuser
2025-07-05 15:00:29,422 - auth - INFO - Created new user: perfuser
2025-07-05 15:01:38,006 - auth - INFO - Created new user: secureuser
2025-07-05 15:01:40,840 - auth - INFO - Created new user: encuser
2025-07-05 15:05:37,272 - auth - INFO - Created new user: secureuser
2025-07-05 15:05:40,589 - auth - INFO - Created new user: encuser
2025-07-05 15:07:36,844 - auth - INFO - Created new user: secureuser
2025-07-05 15:07:39,890 - auth - INFO - Created new user: encuser
2025-07-05 15:07:52,181 - auth - INFO - Created new user: perfuser
2025-07-05 15:12:08,592 - auth - INFO - Created new user: perfuser
2025-07-05 15:17:27,452 - auth - INFO - Created new user: perfuser
2025-07-05 16:09:39,906 - auth - INFO - Created new user: wpuser
2025-07-05 16:16:15,874 - auth - INFO - Created new user: ehadmin
2025-07-05 16:16:16,918 - auth - INFO - Created new user: ehadmin
2025-07-05 16:19:22,999 - auth - INFO - Created new user: wpuser
2025-07-05 16:19:24,228 - auth - INFO - Created new user: ehadmin
2025-07-05 16:19:25,298 - auth - INFO - Created new user: ehadmin
2025-07-05 16:19:50,473 - auth - INFO - Created new user: bulkadmin
2025-07-05 16:19:52,128 - auth - INFO - Created new user: bulkadmin
2025-07-05 16:24:15,126 - auth - INFO - Created new user: bulkadmin
2025-07-05 16:24:16,673 - auth - INFO - Created new user: bulkadmin
2025-07-05 16:32:34,087 - auth - INFO - Created new user: adminuser
2025-07-05 16:32:39,494 - auth - INFO - Created new user: adminuser
2025-07-05 16:32:44,750 - auth - INFO - Created new user: adminuser
2025-07-05 16:32:45,171 - auth - INFO - Created new user: newuser
2025-07-05 16:32:50,193 - auth - INFO - Created new user: adminuser
2025-07-05 16:32:50,587 - auth - INFO - Created new user: a
2025-07-05 16:32:50,780 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 16:32:50,980 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 16:32:55,954 - auth - INFO - Created new user: adminuser
2025-07-05 16:33:01,190 - auth - INFO - Created new user: adminuser
2025-07-05 16:33:10,440 - auth - INFO - Created new user: adminuser
2025-07-05 16:33:15,836 - auth - INFO - Created new user: adminuser
2025-07-05 16:33:21,054 - auth - INFO - Created new user: adminuser
2025-07-05 16:33:26,176 - auth - INFO - Created new user: adminuser
2025-07-05 16:33:31,340 - auth - INFO - Created new user: adminuser
2025-07-05 16:33:36,560 - auth - INFO - Created new user: adminuser
2025-07-05 16:33:41,790 - auth - INFO - Created new user: adminuser
2025-07-05 16:33:47,006 - auth - INFO - Created new user: adminuser
2025-07-05 16:33:52,254 - auth - INFO - Created new user: adminuser
2025-07-05 16:33:57,470 - auth - INFO - Created new user: adminuser
2025-07-05 16:34:02,630 - auth - INFO - Created new user: adminuser
2025-07-05 16:34:07,830 - auth - INFO - Created new user: adminuser
2025-07-05 16:34:13,045 - auth - INFO - Created new user: adminuser
2025-07-05 16:34:18,190 - auth - INFO - Created new user: adminuser
2025-07-05 16:34:23,480 - auth - INFO - Created new user: adminuser
2025-07-05 16:34:32,754 - auth - INFO - Created new user: annouser
2025-07-05 16:34:37,940 - auth - INFO - Created new user: annouser
2025-07-05 16:34:43,180 - auth - INFO - Created new user: annouser
2025-07-05 16:34:48,343 - auth - INFO - Created new user: annouser
2025-07-05 16:34:53,600 - auth - INFO - Created new user: annouser
2025-07-05 16:34:58,754 - auth - INFO - Created new user: annouser
2025-07-05 16:35:03,965 - auth - INFO - Created new user: audituser
2025-07-05 16:35:09,110 - auth - INFO - Created new user: audituser
2025-07-05 16:35:14,340 - auth - INFO - Created new user: audituser
2025-07-05 16:35:20,430 - auth - INFO - Created new user: audituser
2025-07-05 16:35:26,410 - auth - INFO - Created new user: testuser
2025-07-05 16:35:43,642 - auth - INFO - Created new user: changepw
2025-07-05 16:35:44,238 - auth - INFO - Updated password for user: changepw
2025-07-05 16:35:50,221 - auth - INFO - Created new user: a
2025-07-05 16:35:55,637 - auth - INFO - Created new user: minpw
2025-07-05 16:36:01,289 - auth - INFO - Created new user: clientuser
2025-07-05 16:36:06,463 - auth - INFO - Created new user: clientuser
2025-07-05 16:36:07,952 - auth - INFO - Created new user: bulkadmin
2025-07-05 16:36:09,375 - auth - INFO - Created new user: bulkadmin
2025-07-05 16:36:13,335 - auth - INFO - Created new user: wpuser
2025-07-05 16:36:14,652 - auth - INFO - Created new user: ehadmin
2025-07-05 16:36:15,936 - auth - INFO - Created new user: ehadmin
2025-07-05 16:36:16,669 - auth - INFO - Created new user: kbadmin
2025-07-05 16:36:19,438 - auth - INFO - Created new user: perfuser
2025-07-05 16:36:27,963 - auth - INFO - Created new user: auser
2025-07-05 16:36:31,050 - auth - INFO - Created new user: secureuser
2025-07-05 16:36:33,640 - auth - INFO - Created new user: encuser
2025-07-05 16:36:38,666 - auth - INFO - Created new user: supuser
2025-07-05 16:36:43,659 - auth - INFO - Created new user: supuser
2025-07-05 16:36:48,752 - auth - INFO - Created new user: supuser
2025-07-05 16:36:49,167 - auth - INFO - Created new user: synthadmin
2025-07-05 16:36:49,687 - auth - INFO - Created new user: tgadmin
2025-07-05 16:36:50,110 - auth - INFO - Created new user: concurrentuser
2025-07-05 16:43:13,702 - auth - INFO - Created new user: adminuser
2025-07-05 16:43:18,905 - auth - INFO - Created new user: adminuser
2025-07-05 16:43:24,079 - auth - INFO - Created new user: adminuser
2025-07-05 16:43:24,455 - auth - INFO - Created new user: newuser
2025-07-05 16:43:29,271 - auth - INFO - Created new user: adminuser
2025-07-05 16:43:29,652 - auth - INFO - Created new user: a
2025-07-05 16:43:29,841 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 16:43:30,036 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-05 16:43:35,274 - auth - INFO - Created new user: adminuser
2025-07-05 16:43:40,616 - auth - INFO - Created new user: adminuser
2025-07-05 16:43:49,784 - auth - INFO - Created new user: adminuser
2025-07-05 16:43:54,883 - auth - INFO - Created new user: adminuser
2025-07-05 16:43:59,953 - auth - INFO - Created new user: adminuser
2025-07-05 16:44:05,162 - auth - INFO - Created new user: adminuser
2025-07-05 16:44:10,406 - auth - INFO - Created new user: adminuser
2025-07-05 16:44:15,706 - auth - INFO - Created new user: adminuser
2025-07-05 16:44:20,911 - auth - INFO - Created new user: adminuser
2025-07-05 16:44:26,132 - auth - INFO - Created new user: adminuser
2025-07-05 16:44:31,511 - auth - INFO - Created new user: adminuser
2025-07-05 16:44:36,815 - auth - INFO - Created new user: adminuser
2025-07-05 16:44:42,111 - auth - INFO - Created new user: adminuser
2025-07-05 16:44:47,415 - auth - INFO - Created new user: adminuser
2025-07-05 16:44:52,801 - auth - INFO - Created new user: adminuser
2025-07-05 16:44:58,112 - auth - INFO - Created new user: adminuser
2025-07-05 16:45:03,331 - auth - INFO - Created new user: adminuser
2025-07-05 16:45:12,602 - auth - INFO - Created new user: annouser
2025-07-05 16:45:17,831 - auth - INFO - Created new user: annouser
2025-07-05 16:45:22,972 - auth - INFO - Created new user: annouser
2025-07-05 16:45:28,151 - auth - INFO - Created new user: annouser
2025-07-05 16:45:33,351 - auth - INFO - Created new user: annouser
2025-07-05 16:45:38,581 - auth - INFO - Created new user: annouser
2025-07-05 16:45:43,796 - auth - INFO - Created new user: audituser
2025-07-05 16:45:49,032 - auth - INFO - Created new user: audituser
2025-07-05 16:45:54,201 - auth - INFO - Created new user: audituser
2025-07-05 16:45:59,556 - auth - INFO - Created new user: audituser
2025-07-05 16:46:04,802 - auth - INFO - Created new user: testuser
2025-07-05 16:46:19,782 - auth - INFO - Created new user: changepw
2025-07-05 16:46:20,321 - auth - INFO - Updated password for user: changepw
2025-07-05 16:46:25,492 - auth - INFO - Created new user: a
2025-07-05 16:46:30,331 - auth - INFO - Created new user: minpw
2025-07-05 16:46:36,278 - auth - INFO - Created new user: clientuser
2025-07-05 16:46:41,309 - auth - INFO - Created new user: clientuser
2025-07-05 16:46:42,513 - auth - INFO - Created new user: bulkadmin
2025-07-05 16:46:43,673 - auth - INFO - Created new user: bulkadmin
2025-07-05 16:46:46,389 - auth - INFO - Created new user: wpuser
2025-07-05 16:46:47,365 - auth - INFO - Created new user: ehadmin
2025-07-05 16:46:48,369 - auth - INFO - Created new user: ehadmin
2025-07-05 16:46:49,107 - auth - INFO - Created new user: kbadmin
2025-07-05 16:46:51,751 - auth - INFO - Created new user: perfuser
2025-07-05 16:47:03,994 - auth - INFO - Created new user: auser
2025-07-05 16:47:11,768 - auth - INFO - Created new user: secureuser
2025-07-05 16:47:16,961 - auth - INFO - Created new user: encuser
2025-07-05 16:47:21,999 - auth - INFO - Created new user: supuser
2025-07-05 16:47:27,089 - auth - INFO - Created new user: supuser
2025-07-05 16:47:32,528 - auth - INFO - Created new user: supuser
2025-07-05 16:47:32,948 - auth - INFO - Created new user: synthadmin
2025-07-05 16:47:33,459 - auth - INFO - Created new user: tgadmin
2025-07-05 16:47:33,858 - auth - INFO - Created new user: concurrentuser
2025-07-05 17:08:50,324 - auth - INFO - Created new user: testuser
2025-07-05 17:09:05,393 - auth - INFO - Created new user: changepw
2025-07-05 17:09:05,966 - auth - INFO - Updated password for user: changepw
2025-07-05 17:09:11,008 - auth - INFO - Created new user: a
2025-07-05 17:09:15,960 - auth - INFO - Created new user: minpw
2025-07-05 17:09:20,878 - auth - INFO - Created new user: logoutuser
2025-07-05 17:10:26,087 - auth - INFO - Created new user: annouser
2025-07-05 17:10:31,437 - auth - INFO - Created new user: annouser
2025-07-05 17:10:36,686 - auth - INFO - Created new user: annouser
2025-07-05 17:10:41,953 - auth - INFO - Created new user: annouser
2025-07-05 17:10:47,229 - auth - INFO - Created new user: annouser
2025-07-05 17:10:52,678 - auth - INFO - Created new user: annouser
2025-07-05 17:10:57,898 - auth - INFO - Created new user: annouser
2025-07-05 17:13:27,512 - auth - INFO - Created new user: annouser
2025-07-05 17:13:32,796 - auth - INFO - Created new user: annouser
2025-07-05 17:13:37,905 - auth - INFO - Created new user: annouser
2025-07-05 17:13:42,916 - auth - INFO - Created new user: annouser
2025-07-05 17:13:48,100 - auth - INFO - Created new user: annouser
2025-07-05 17:13:53,224 - auth - INFO - Created new user: annouser
2025-07-05 17:13:58,310 - auth - INFO - Created new user: annouser
2025-07-05 17:15:47,400 - auth - INFO - Created new user: annouser
2025-07-05 17:15:52,694 - auth - INFO - Created new user: annouser
2025-07-05 17:15:57,704 - auth - INFO - Created new user: annouser
2025-07-05 17:16:02,881 - auth - INFO - Created new user: annouser
2025-07-05 17:16:08,351 - auth - INFO - Created new user: annouser
2025-07-05 17:16:13,868 - auth - INFO - Created new user: annouser
2025-07-05 17:16:19,231 - auth - INFO - Created new user: annouser
2025-07-05 17:18:26,881 - auth - INFO - Created new user: annouser
2025-07-05 17:18:32,306 - auth - INFO - Created new user: annouser
2025-07-05 17:18:37,412 - auth - INFO - Created new user: annouser
2025-07-05 17:18:42,597 - auth - INFO - Created new user: annouser
2025-07-05 17:18:47,708 - auth - INFO - Created new user: annouser
2025-07-05 17:18:52,829 - auth - INFO - Created new user: annouser
2025-07-05 17:18:57,991 - auth - INFO - Created new user: annouser
2025-07-05 18:22:06,661 - auth - INFO - Created new user: tgadmin
2025-07-05 18:49:07,121 - auth - INFO - Created new user: kbadmin
2025-07-05 19:00:05,198 - auth - INFO - Created new user: supuser
2025-07-05 19:00:10,434 - auth - INFO - Created new user: supuser
2025-07-05 19:00:15,654 - auth - INFO - Created new user: supuser
2025-07-05 19:00:20,853 - auth - INFO - Created new user: supuser
2025-07-05 19:00:26,063 - auth - INFO - Created new user: supuser
2025-07-05 19:00:31,223 - auth - INFO - Created new user: supuser
2025-07-07 11:30:27,645 - auth - INFO - Created new user: <EMAIL>
2025-07-07 11:41:55,102 - auth - INFO - Updated password for user: <EMAIL>
2025-07-07 11:44:50,637 - auth - INFO - Created new user: adminuser
2025-07-07 11:44:55,410 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:00,017 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:00,418 - auth - INFO - Created new user: newuser
2025-07-07 11:45:04,829 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:05,208 - auth - INFO - Created new user: a
2025-07-07 11:45:05,400 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 11:45:05,588 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 11:45:10,004 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:14,597 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:23,338 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:27,956 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:32,602 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:37,246 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:41,875 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:46,498 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:51,130 - auth - INFO - Created new user: adminuser
2025-07-07 11:45:55,731 - auth - INFO - Created new user: adminuser
2025-07-07 11:46:00,397 - auth - INFO - Created new user: adminuser
2025-07-07 11:46:05,191 - auth - INFO - Created new user: adminuser
2025-07-07 11:46:09,992 - auth - INFO - Created new user: adminuser
2025-07-07 11:46:14,780 - auth - INFO - Created new user: adminuser
2025-07-07 11:46:19,575 - auth - INFO - Created new user: adminuser
2025-07-07 11:46:24,372 - auth - INFO - Created new user: adminuser
2025-07-07 11:46:29,162 - auth - INFO - Created new user: adminuser
2025-07-07 11:46:38,040 - auth - INFO - Created new user: annouser
2025-07-07 11:46:42,782 - auth - INFO - Created new user: annouser
2025-07-07 11:46:47,593 - auth - INFO - Created new user: annouser
2025-07-07 11:46:52,359 - auth - INFO - Created new user: annouser
2025-07-07 11:46:57,133 - auth - INFO - Created new user: annouser
2025-07-07 11:47:01,867 - auth - INFO - Created new user: annouser
2025-07-07 11:47:06,474 - auth - INFO - Created new user: annouser
2025-07-07 11:47:11,115 - auth - INFO - Created new user: audituser
2025-07-07 11:47:15,727 - auth - INFO - Created new user: audituser
2025-07-07 11:47:20,325 - auth - INFO - Created new user: audituser
2025-07-07 11:47:24,911 - auth - INFO - Created new user: audituser
2025-07-07 11:47:29,556 - auth - INFO - Created new user: testuser
2025-07-07 11:47:42,706 - auth - INFO - Created new user: changepw
2025-07-07 11:47:43,283 - auth - INFO - Updated password for user: changepw
2025-07-07 11:47:47,887 - auth - INFO - Created new user: a
2025-07-07 11:47:52,316 - auth - INFO - Created new user: minpw
2025-07-07 11:47:56,730 - auth - INFO - Created new user: logoutuser
2025-07-07 11:48:01,503 - auth - INFO - Created new user: clientuser
2025-07-07 11:48:06,107 - auth - INFO - Created new user: clientuser
2025-07-07 11:48:11,308 - auth - INFO - Created new user: bulkadmin
2025-07-07 11:48:12,073 - auth - INFO - Created new user: bulkadmin
2025-07-07 11:48:13,052 - auth - INFO - Created new user: wpuser
2025-07-07 11:48:13,591 - auth - INFO - Created new user: ehadmin
2025-07-07 11:48:14,154 - auth - INFO - Created new user: ehadmin
2025-07-07 11:48:14,873 - auth - INFO - Created new user: kbadmin
2025-07-07 11:48:15,828 - auth - INFO - Created new user: perfuser
2025-07-07 11:48:18,370 - auth - INFO - Created new user: auser
2025-07-07 11:48:19,395 - auth - INFO - Created new user: secureuser
2025-07-07 11:48:20,402 - auth - INFO - Created new user: encuser
2025-07-07 11:48:24,898 - auth - INFO - Created new user: supuser
2025-07-07 11:48:29,541 - auth - INFO - Created new user: supuser
2025-07-07 11:48:34,310 - auth - INFO - Created new user: supuser
2025-07-07 11:48:39,018 - auth - INFO - Created new user: supuser
2025-07-07 11:48:43,728 - auth - INFO - Created new user: supuser
2025-07-07 11:48:48,412 - auth - INFO - Created new user: supuser
2025-07-07 11:48:48,821 - auth - INFO - Created new user: synthadmin
2025-07-07 11:48:49,331 - auth - INFO - Created new user: tgadmin
2025-07-07 11:48:52,525 - auth - INFO - Created new user: concurrentuser
2025-07-07 11:52:01,584 - auth - INFO - Created new user: adminuser
2025-07-07 11:52:06,426 - auth - INFO - Created new user: adminuser
2025-07-07 11:52:11,035 - auth - INFO - Created new user: adminuser
2025-07-07 11:52:11,422 - auth - INFO - Created new user: newuser
2025-07-07 11:52:15,797 - auth - INFO - Created new user: adminuser
2025-07-07 11:52:16,181 - auth - INFO - Created new user: a
2025-07-07 11:52:16,368 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 11:52:16,561 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 11:52:20,950 - auth - INFO - Created new user: adminuser
2025-07-07 11:52:25,529 - auth - INFO - Created new user: adminuser
2025-07-07 11:52:34,173 - auth - INFO - Created new user: adminuser
2025-07-07 11:52:38,735 - auth - INFO - Created new user: adminuser
2025-07-07 11:52:43,311 - auth - INFO - Created new user: adminuser
2025-07-07 11:52:47,940 - auth - INFO - Created new user: adminuser
2025-07-07 11:52:52,572 - auth - INFO - Created new user: adminuser
2025-07-07 11:52:57,208 - auth - INFO - Created new user: adminuser
2025-07-07 11:53:01,800 - auth - INFO - Created new user: adminuser
2025-07-07 11:53:06,378 - auth - INFO - Created new user: adminuser
2025-07-07 11:53:10,960 - auth - INFO - Created new user: adminuser
2025-07-07 11:53:15,571 - auth - INFO - Created new user: adminuser
2025-07-07 11:53:20,202 - auth - INFO - Created new user: adminuser
2025-07-07 11:53:24,796 - auth - INFO - Created new user: adminuser
2025-07-07 11:53:29,400 - auth - INFO - Created new user: adminuser
2025-07-07 11:53:34,016 - auth - INFO - Created new user: adminuser
2025-07-07 11:53:38,645 - auth - INFO - Created new user: adminuser
2025-07-07 11:53:47,330 - auth - INFO - Created new user: annouser
2025-07-07 11:53:51,972 - auth - INFO - Created new user: annouser
2025-07-07 11:53:56,592 - auth - INFO - Created new user: annouser
2025-07-07 11:54:01,210 - auth - INFO - Created new user: annouser
2025-07-07 11:54:05,830 - auth - INFO - Created new user: annouser
2025-07-07 11:54:10,444 - auth - INFO - Created new user: annouser
2025-07-07 11:54:15,079 - auth - INFO - Created new user: annouser
2025-07-07 11:54:19,669 - auth - INFO - Created new user: audituser
2025-07-07 11:54:24,277 - auth - INFO - Created new user: audituser
2025-07-07 11:54:28,883 - auth - INFO - Created new user: audituser
2025-07-07 11:54:33,489 - auth - INFO - Created new user: audituser
2025-07-07 11:54:38,122 - auth - INFO - Created new user: testuser
2025-07-07 11:54:51,108 - auth - INFO - Created new user: changepw
2025-07-07 11:54:51,663 - auth - INFO - Updated password for user: changepw
2025-07-07 11:54:56,232 - auth - INFO - Created new user: a
2025-07-07 11:55:00,659 - auth - INFO - Created new user: minpw
2025-07-07 11:55:05,056 - auth - INFO - Created new user: logoutuser
2025-07-07 11:55:09,803 - auth - INFO - Created new user: clientuser
2025-07-07 11:55:14,414 - auth - INFO - Created new user: clientuser
2025-07-07 11:55:19,044 - auth - INFO - Created new user: concuradmin
2025-07-07 11:55:19,989 - auth - INFO - Created new user: bulkadmin
2025-07-07 11:55:20,641 - auth - INFO - Created new user: bulkadmin
2025-07-07 11:55:21,593 - auth - INFO - Created new user: wpuser
2025-07-07 11:55:22,136 - auth - INFO - Created new user: ehadmin
2025-07-07 11:55:22,688 - auth - INFO - Created new user: ehadmin
2025-07-07 11:55:23,448 - auth - INFO - Created new user: kbadmin
2025-07-07 11:55:28,505 - auth - INFO - Created new user: supuser
2025-07-07 11:55:29,145 - auth - INFO - Created new user: perfuser
2025-07-07 11:55:31,800 - auth - INFO - Created new user: auser
2025-07-07 11:55:32,920 - auth - INFO - Created new user: secureuser
2025-07-07 11:55:34,040 - auth - INFO - Created new user: encuser
2025-07-07 11:55:38,575 - auth - INFO - Created new user: supuser
2025-07-07 11:55:43,287 - auth - INFO - Created new user: supuser
2025-07-07 11:55:48,092 - auth - INFO - Created new user: supuser
2025-07-07 11:55:52,807 - auth - INFO - Created new user: supuser
2025-07-07 11:55:57,494 - auth - INFO - Created new user: supuser
2025-07-07 11:56:02,177 - auth - INFO - Created new user: supuser
2025-07-07 11:56:02,577 - auth - INFO - Created new user: synthadmin
2025-07-07 11:56:03,092 - auth - INFO - Created new user: tgadmin
2025-07-07 11:56:07,649 - auth - INFO - Created new user: concurrentuser
2025-07-07 11:57:10,226 - auth - INFO - Created new user: <EMAIL>
2025-07-07 11:57:11,344 - auth - INFO - Updated password for user: <EMAIL>
2025-07-07 12:15:00,764 - auth - INFO - Created new user: adminuser
2025-07-07 12:15:05,488 - auth - INFO - Created new user: adminuser
2025-07-07 12:15:10,095 - auth - INFO - Created new user: adminuser
2025-07-07 12:15:10,491 - auth - INFO - Created new user: newuser
2025-07-07 12:15:14,889 - auth - INFO - Created new user: adminuser
2025-07-07 12:15:15,278 - auth - INFO - Created new user: a
2025-07-07 12:15:15,481 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 12:15:15,677 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 12:15:20,089 - auth - INFO - Created new user: adminuser
2025-07-07 12:15:24,693 - auth - INFO - Created new user: adminuser
2025-07-07 12:15:33,349 - auth - INFO - Created new user: adminuser
2025-07-07 12:15:37,940 - auth - INFO - Created new user: adminuser
2025-07-07 12:15:42,529 - auth - INFO - Created new user: adminuser
2025-07-07 12:15:47,139 - auth - INFO - Created new user: adminuser
2025-07-07 12:15:51,860 - auth - INFO - Created new user: adminuser
2025-07-07 12:15:56,431 - auth - INFO - Created new user: adminuser
2025-07-07 12:16:01,034 - auth - INFO - Created new user: adminuser
2025-07-07 12:16:05,631 - auth - INFO - Created new user: adminuser
2025-07-07 12:16:10,249 - auth - INFO - Created new user: adminuser
2025-07-07 12:16:14,852 - auth - INFO - Created new user: adminuser
2025-07-07 12:16:19,511 - auth - INFO - Created new user: adminuser
2025-07-07 12:16:24,319 - auth - INFO - Created new user: adminuser
2025-07-07 12:16:29,117 - auth - INFO - Created new user: adminuser
2025-07-07 12:16:33,931 - auth - INFO - Created new user: adminuser
2025-07-07 12:16:38,721 - auth - INFO - Created new user: adminuser
2025-07-07 12:16:47,637 - auth - INFO - Created new user: annouser
2025-07-07 12:16:48,021 - auth - INFO - Created new user: <EMAIL>
2025-07-07 12:16:52,444 - auth - INFO - Created new user: annouser
2025-07-07 12:16:57,369 - auth - INFO - Created new user: annouser
2025-07-07 12:17:02,135 - auth - INFO - Created new user: annouser
2025-07-07 12:17:06,932 - auth - INFO - Created new user: annouser
2025-07-07 12:17:11,714 - auth - INFO - Created new user: annouser
2025-07-07 12:17:16,560 - auth - INFO - Created new user: annouser
2025-07-07 12:17:21,394 - auth - INFO - Created new user: audituser
2025-07-07 12:17:26,241 - auth - INFO - Created new user: audituser
2025-07-07 12:17:31,039 - auth - INFO - Created new user: audituser
2025-07-07 12:17:35,795 - auth - INFO - Created new user: audituser
2025-07-07 12:17:40,598 - auth - INFO - Created new user: testuser
2025-07-07 12:17:54,067 - auth - INFO - Created new user: changepw
2025-07-07 12:17:54,795 - auth - INFO - Updated password for user: changepw
2025-07-07 12:17:59,510 - auth - INFO - Created new user: a
2025-07-07 12:18:03,896 - auth - INFO - Created new user: minpw
2025-07-07 12:18:08,273 - auth - INFO - Created new user: logoutuser
2025-07-07 12:18:13,055 - auth - INFO - Created new user: clientuser
2025-07-07 12:18:17,639 - auth - INFO - Created new user: clientuser
2025-07-07 12:18:22,252 - auth - INFO - Created new user: concuradmin
2025-07-07 12:18:22,794 - auth - INFO - Created new user: bulkadmin
2025-07-07 12:18:23,435 - auth - INFO - Created new user: bulkadmin
2025-07-07 12:18:24,393 - auth - INFO - Created new user: wpuser
2025-07-07 12:18:24,928 - auth - INFO - Created new user: ehadmin
2025-07-07 12:18:25,460 - auth - INFO - Created new user: ehadmin
2025-07-07 12:18:26,167 - auth - INFO - Created new user: kbadmin
2025-07-07 12:18:31,136 - auth - INFO - Created new user: supuser
2025-07-07 12:18:31,759 - auth - INFO - Created new user: perfuser
2025-07-07 12:18:34,268 - auth - INFO - Created new user: auser
2025-07-07 12:18:35,278 - auth - INFO - Created new user: secureuser
2025-07-07 12:18:36,332 - auth - INFO - Created new user: encuser
2025-07-07 12:18:40,845 - auth - INFO - Created new user: supuser
2025-07-07 12:18:45,553 - auth - INFO - Created new user: supuser
2025-07-07 12:18:50,267 - auth - INFO - Created new user: supuser
2025-07-07 12:18:54,989 - auth - INFO - Created new user: supuser
2025-07-07 12:18:59,679 - auth - INFO - Created new user: supuser
2025-07-07 12:19:04,389 - auth - INFO - Created new user: supuser
2025-07-07 12:19:04,799 - auth - INFO - Created new user: synthadmin
2025-07-07 12:19:05,317 - auth - INFO - Created new user: tgadmin
2025-07-07 12:19:08,820 - auth - INFO - Created new user: concurrentuser
2025-07-07 12:31:42,349 - auth - INFO - Created new user: adminuser
2025-07-07 12:31:47,044 - auth - INFO - Created new user: adminuser
2025-07-07 12:31:51,631 - auth - INFO - Created new user: adminuser
2025-07-07 12:31:52,010 - auth - INFO - Created new user: newuser
2025-07-07 12:31:56,436 - auth - INFO - Created new user: adminuser
2025-07-07 12:31:56,879 - auth - INFO - Created new user: a
2025-07-07 12:31:57,086 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 12:31:57,288 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 12:32:01,740 - auth - INFO - Created new user: adminuser
2025-07-07 12:32:06,519 - auth - INFO - Created new user: adminuser
2025-07-07 12:32:15,371 - auth - INFO - Created new user: adminuser
2025-07-07 12:32:20,191 - auth - INFO - Created new user: adminuser
2025-07-07 12:32:24,963 - auth - INFO - Created new user: adminuser
2025-07-07 12:32:29,811 - auth - INFO - Created new user: adminuser
2025-07-07 12:32:34,570 - auth - INFO - Created new user: adminuser
2025-07-07 12:32:39,234 - auth - INFO - Created new user: adminuser
2025-07-07 12:32:43,940 - auth - INFO - Created new user: adminuser
2025-07-07 12:32:48,538 - auth - INFO - Created new user: adminuser
2025-07-07 12:32:53,205 - auth - INFO - Created new user: adminuser
2025-07-07 12:32:58,033 - auth - INFO - Created new user: adminuser
2025-07-07 12:33:02,839 - auth - INFO - Created new user: adminuser
2025-07-07 12:33:07,690 - auth - INFO - Created new user: adminuser
2025-07-07 12:33:12,504 - auth - INFO - Created new user: adminuser
2025-07-07 12:33:17,335 - auth - INFO - Created new user: adminuser
2025-07-07 12:33:22,110 - auth - INFO - Created new user: adminuser
2025-07-07 12:33:30,836 - auth - INFO - Created new user: annouser
2025-07-07 12:33:35,457 - auth - INFO - Created new user: annouser
2025-07-07 12:33:40,147 - auth - INFO - Created new user: annouser
2025-07-07 12:33:44,804 - auth - INFO - Created new user: annouser
2025-07-07 12:33:49,611 - auth - INFO - Created new user: annouser
2025-07-07 12:33:54,402 - auth - INFO - Created new user: annouser
2025-07-07 12:33:59,317 - auth - INFO - Created new user: annouser
2025-07-07 12:34:04,151 - auth - INFO - Created new user: audituser
2025-07-07 12:34:09,015 - auth - INFO - Created new user: audituser
2025-07-07 12:34:13,812 - auth - INFO - Created new user: audituser
2025-07-07 12:34:18,666 - auth - INFO - Created new user: audituser
2025-07-07 12:34:23,497 - auth - INFO - Created new user: testuser
2025-07-07 12:34:36,901 - auth - INFO - Created new user: changepw
2025-07-07 12:34:37,597 - auth - INFO - Updated password for user: changepw
2025-07-07 12:34:42,410 - auth - INFO - Created new user: a
2025-07-07 12:34:46,988 - auth - INFO - Created new user: minpw
2025-07-07 12:34:51,494 - auth - INFO - Created new user: logoutuser
2025-07-07 12:34:56,552 - auth - INFO - Created new user: clientuser
2025-07-07 12:35:01,378 - auth - INFO - Created new user: clientuser
2025-07-07 12:35:06,176 - auth - INFO - Created new user: concuradmin
2025-07-07 12:35:06,915 - auth - INFO - Created new user: bulkadmin
2025-07-07 12:35:07,827 - auth - INFO - Created new user: bulkadmin
2025-07-07 12:35:09,224 - auth - INFO - Created new user: wpuser
2025-07-07 12:35:09,919 - auth - INFO - Created new user: ehadmin
2025-07-07 12:35:10,646 - auth - INFO - Created new user: ehadmin
2025-07-07 12:35:11,706 - auth - INFO - Created new user: kbadmin
2025-07-07 12:35:17,299 - auth - INFO - Created new user: supuser
2025-07-07 12:35:19,893 - auth - INFO - Created new user: perfuser
2025-07-07 12:35:24,225 - auth - INFO - Created new user: auser
2025-07-07 12:35:25,720 - auth - INFO - Created new user: secureuser
2025-07-07 12:35:40,038 - auth - INFO - Created new user: encuser
2025-07-07 12:35:49,046 - auth - INFO - Created new user: supuser
2025-07-07 12:35:58,578 - auth - INFO - Created new user: supuser
2025-07-07 12:36:06,101 - auth - INFO - Created new user: supuser
2025-07-07 12:36:13,166 - auth - INFO - Created new user: supuser
2025-07-07 12:36:19,996 - auth - INFO - Created new user: supuser
2025-07-07 12:36:26,828 - auth - INFO - Created new user: supuser
2025-07-07 12:36:27,309 - auth - INFO - Created new user: synthadmin
2025-07-07 12:36:28,171 - auth - INFO - Created new user: tgadmin
2025-07-07 12:36:32,232 - auth - INFO - Created new user: concurrentuser
2025-07-07 14:43:57,469 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:02,258 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:06,844 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:07,228 - auth - INFO - Created new user: newuser
2025-07-07 14:44:11,627 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:12,001 - auth - INFO - Created new user: a
2025-07-07 14:44:12,192 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 14:44:12,384 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 14:44:16,779 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:21,416 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:30,118 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:34,697 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:39,291 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:43,849 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:48,441 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:53,023 - auth - INFO - Created new user: adminuser
2025-07-07 14:44:57,673 - auth - INFO - Created new user: adminuser
2025-07-07 14:45:02,370 - auth - INFO - Created new user: adminuser
2025-07-07 14:45:06,956 - auth - INFO - Created new user: adminuser
2025-07-07 14:45:11,636 - auth - INFO - Created new user: adminuser
2025-07-07 14:45:16,262 - auth - INFO - Created new user: adminuser
2025-07-07 14:45:20,877 - auth - INFO - Created new user: adminuser
2025-07-07 14:45:25,501 - auth - INFO - Created new user: adminuser
2025-07-07 14:45:30,102 - auth - INFO - Created new user: adminuser
2025-07-07 14:45:34,672 - auth - INFO - Created new user: adminuser
2025-07-07 14:45:43,272 - auth - INFO - Created new user: anno_user
2025-07-07 14:45:47,863 - auth - INFO - Created new user: anno_user
2025-07-07 14:45:52,450 - auth - INFO - Created new user: anno_user
2025-07-07 14:45:57,234 - auth - INFO - Created new user: adminuser
2025-07-07 14:46:01,812 - auth - INFO - Created new user: adminuser
2025-07-07 14:46:02,018 - auth - INFO - Updated user: adminuser
2025-07-07 14:46:02,019 - auth - INFO - Updated user: adminuser
2025-07-07 14:46:06,767 - auth - INFO - Created new user: adminuser
2025-07-07 14:46:11,346 - auth - INFO - Created new user: adminuser
2025-07-07 14:46:15,948 - auth - INFO - Created new user: adminuser
2025-07-07 14:46:20,534 - auth - INFO - Created new user: annouser
2025-07-07 14:46:25,144 - auth - INFO - Created new user: annouser
2025-07-07 14:46:29,750 - auth - INFO - Created new user: annouser
2025-07-07 14:46:34,361 - auth - INFO - Created new user: annouser
2025-07-07 14:46:38,989 - auth - INFO - Created new user: annouser
2025-07-07 14:46:43,562 - auth - INFO - Created new user: annouser
2025-07-07 14:46:48,164 - auth - INFO - Created new user: annouser
2025-07-07 14:46:52,782 - auth - INFO - Created new user: annouser
2025-07-07 14:46:57,343 - auth - INFO - Created new user: annouser
2025-07-07 14:47:01,916 - auth - INFO - Created new user: annouser
2025-07-07 14:47:06,481 - auth - INFO - Created new user: audituser
2025-07-07 14:47:11,058 - auth - INFO - Created new user: audituser
2025-07-07 14:47:15,623 - auth - INFO - Created new user: audituser
2025-07-07 14:47:20,211 - auth - INFO - Created new user: audituser
2025-07-07 14:47:24,909 - auth - INFO - Created new user: audituser
2025-07-07 14:47:29,532 - auth - INFO - Created new user: audituser
2025-07-07 14:47:34,389 - auth - INFO - Created new user: audituser
2025-07-07 14:47:39,037 - auth - INFO - Created new user: wrongaudit
2025-07-07 14:47:43,724 - auth - INFO - Created new user: testuser
2025-07-07 14:47:56,766 - auth - INFO - Created new user: changepw
2025-07-07 14:47:57,311 - auth - INFO - Updated password for user: changepw
2025-07-07 14:48:01,856 - auth - INFO - Created new user: a
2025-07-07 14:48:06,235 - auth - INFO - Created new user: minpw
2025-07-07 14:48:10,620 - auth - INFO - Created new user: logoutuser
2025-07-07 14:48:23,541 - auth - INFO - Created new user: cpuser
2025-07-07 14:48:28,297 - auth - INFO - Created new user: du
2025-07-07 14:48:33,012 - auth - INFO - Created new user: clientuser
2025-07-07 14:48:37,535 - auth - INFO - Created new user: clientuser
2025-07-07 14:48:42,026 - auth - INFO - Created new user: notclient
2025-07-07 14:48:46,586 - auth - INFO - Created new user: concuradmin
2025-07-07 14:48:47,108 - auth - INFO - Created new user: bulkadmin
2025-07-07 14:48:47,721 - auth - INFO - Created new user: bulkadmin
2025-07-07 14:48:48,639 - auth - INFO - Created new user: wpuser
2025-07-07 14:48:49,151 - auth - INFO - Created new user: ehadmin
2025-07-07 14:48:49,689 - auth - INFO - Created new user: ehadmin
2025-07-07 14:48:50,420 - auth - INFO - Created new user: kbadmin
2025-07-07 14:48:51,030 - auth - INFO - Created new user: kbaudit
2025-07-07 14:48:55,998 - auth - INFO - Created new user: supuser
2025-07-07 14:48:56,619 - auth - INFO - Created new user: perfuser
2025-07-07 14:48:59,093 - auth - INFO - Created new user: auser
2025-07-07 14:48:59,891 - auth - INFO - Created new user: secureuser
2025-07-07 14:49:00,856 - auth - INFO - Created new user: encuser
2025-07-07 14:49:05,335 - auth - INFO - Created new user: supuser
2025-07-07 14:49:10,019 - auth - INFO - Created new user: supuser
2025-07-07 14:49:14,652 - auth - INFO - Created new user: supuser
2025-07-07 14:49:19,304 - auth - INFO - Created new user: supuser
2025-07-07 14:49:24,058 - auth - INFO - Created new user: supuser
2025-07-07 14:49:28,697 - auth - INFO - Created new user: supuser
2025-07-07 14:49:33,346 - auth - INFO - Created new user: supuser
2025-07-07 14:49:38,011 - auth - INFO - Created new user: supuser
2025-07-07 14:49:42,653 - auth - INFO - Created new user: supuser
2025-07-07 14:49:47,339 - auth - INFO - Created new user: anno_sup
2025-07-07 14:49:52,026 - auth - INFO - Created new user: supuser
2025-07-07 14:49:56,674 - auth - INFO - Created new user: supuser
2025-07-07 14:50:01,312 - auth - INFO - Created new user: supuser
2025-07-07 14:50:01,712 - auth - INFO - Created new user: synthadmin
2025-07-07 14:50:02,228 - auth - INFO - Created new user: radio_user
2025-07-07 14:50:02,661 - auth - INFO - Created new user: tgadmin
2025-07-07 14:50:06,279 - auth - INFO - Created new user: concurrentuser
2025-07-07 14:54:49,139 - auth - INFO - Created new user: adminuser
2025-07-07 14:54:53,832 - auth - INFO - Created new user: adminuser
2025-07-07 14:54:58,406 - auth - INFO - Created new user: adminuser
2025-07-07 14:54:58,763 - auth - INFO - Created new user: newuser
2025-07-07 14:55:03,125 - auth - INFO - Created new user: adminuser
2025-07-07 14:55:03,499 - auth - INFO - Created new user: a
2025-07-07 14:55:03,673 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 14:55:03,867 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 14:55:08,264 - auth - INFO - Created new user: adminuser
2025-07-07 14:55:12,834 - auth - INFO - Created new user: adminuser
2025-07-07 14:55:21,499 - auth - INFO - Created new user: adminuser
2025-07-07 14:55:26,121 - auth - INFO - Created new user: adminuser
2025-07-07 14:55:30,748 - auth - INFO - Created new user: adminuser
2025-07-07 14:55:35,337 - auth - INFO - Created new user: adminuser
2025-07-07 14:55:39,947 - auth - INFO - Created new user: adminuser
2025-07-07 14:55:44,557 - auth - INFO - Created new user: adminuser
2025-07-07 14:55:49,182 - auth - INFO - Created new user: adminuser
2025-07-07 14:55:53,780 - auth - INFO - Created new user: adminuser
2025-07-07 14:55:58,380 - auth - INFO - Created new user: adminuser
2025-07-07 14:56:02,955 - auth - INFO - Created new user: adminuser
2025-07-07 14:56:07,577 - auth - INFO - Created new user: adminuser
2025-07-07 14:56:12,182 - auth - INFO - Created new user: adminuser
2025-07-07 14:56:16,779 - auth - INFO - Created new user: adminuser
2025-07-07 14:56:21,390 - auth - INFO - Created new user: adminuser
2025-07-07 14:56:26,042 - auth - INFO - Created new user: adminuser
2025-07-07 14:56:34,737 - auth - INFO - Created new user: anno_user
2025-07-07 14:56:39,311 - auth - INFO - Created new user: anno_user
2025-07-07 14:56:43,913 - auth - INFO - Created new user: anno_user
2025-07-07 14:56:48,668 - auth - INFO - Created new user: adminuser
2025-07-07 14:56:53,258 - auth - INFO - Created new user: adminuser
2025-07-07 14:56:53,457 - auth - INFO - Updated user: adminuser
2025-07-07 14:56:53,462 - auth - INFO - Updated user: adminuser
2025-07-07 14:56:58,342 - auth - INFO - Created new user: adminuser
2025-07-07 14:57:02,970 - auth - INFO - Created new user: adminuser
2025-07-07 14:57:07,556 - auth - INFO - Created new user: adminuser
2025-07-07 14:57:12,133 - auth - INFO - Created new user: annouser
2025-07-07 14:57:16,742 - auth - INFO - Created new user: annouser
2025-07-07 14:57:21,342 - auth - INFO - Created new user: annouser
2025-07-07 14:57:25,984 - auth - INFO - Created new user: annouser
2025-07-07 14:57:30,725 - auth - INFO - Created new user: annouser
2025-07-07 14:57:35,513 - auth - INFO - Created new user: annouser
2025-07-07 14:57:40,366 - auth - INFO - Created new user: annouser
2025-07-07 14:57:45,263 - auth - INFO - Created new user: annouser
2025-07-07 14:57:50,141 - auth - INFO - Created new user: annouser
2025-07-07 14:57:54,991 - auth - INFO - Created new user: annouser
2025-07-07 14:57:59,853 - auth - INFO - Created new user: audituser
2025-07-07 14:58:04,692 - auth - INFO - Created new user: audituser
2025-07-07 14:58:09,621 - auth - INFO - Created new user: audituser
2025-07-07 14:58:14,438 - auth - INFO - Created new user: audituser
2025-07-07 14:58:19,348 - auth - INFO - Created new user: audituser
2025-07-07 14:58:23,962 - auth - INFO - Created new user: audituser
2025-07-07 14:58:28,808 - auth - INFO - Created new user: audituser
2025-07-07 14:58:33,503 - auth - INFO - Created new user: wrongaudit
2025-07-07 14:58:38,164 - auth - INFO - Created new user: testuser
2025-07-07 14:58:51,256 - auth - INFO - Created new user: changepw
2025-07-07 14:58:51,793 - auth - INFO - Updated password for user: changepw
2025-07-07 14:58:56,423 - auth - INFO - Created new user: a
2025-07-07 14:59:00,900 - auth - INFO - Created new user: minpw
2025-07-07 14:59:05,342 - auth - INFO - Created new user: logoutuser
2025-07-07 14:59:18,471 - auth - INFO - Created new user: cpuser
2025-07-07 14:59:23,279 - auth - INFO - Created new user: du
2025-07-07 14:59:28,022 - auth - INFO - Created new user: clientuser
2025-07-07 14:59:32,606 - auth - INFO - Created new user: clientuser
2025-07-07 14:59:37,069 - auth - INFO - Created new user: notclient
2025-07-07 14:59:41,708 - auth - INFO - Created new user: concuradmin
2025-07-07 14:59:42,236 - auth - INFO - Created new user: bulkadmin
2025-07-07 14:59:42,864 - auth - INFO - Created new user: bulkadmin
2025-07-07 14:59:43,806 - auth - INFO - Created new user: wpuser
2025-07-07 14:59:44,353 - auth - INFO - Created new user: ehadmin
2025-07-07 14:59:44,900 - auth - INFO - Created new user: ehadmin
2025-07-07 14:59:45,645 - auth - INFO - Created new user: kbadmin
2025-07-07 14:59:46,245 - auth - INFO - Created new user: kbaudit
2025-07-07 14:59:51,193 - auth - INFO - Created new user: supuser
2025-07-07 14:59:51,821 - auth - INFO - Created new user: perfuser
2025-07-07 14:59:54,255 - auth - INFO - Created new user: auser
2025-07-07 14:59:55,048 - auth - INFO - Created new user: secureuser
2025-07-07 14:59:56,077 - auth - INFO - Created new user: encuser
2025-07-07 15:00:00,618 - auth - INFO - Created new user: supuser
2025-07-07 15:00:05,320 - auth - INFO - Created new user: supuser
2025-07-07 15:00:10,008 - auth - INFO - Created new user: supuser
2025-07-07 15:00:14,693 - auth - INFO - Created new user: supuser
2025-07-07 15:00:19,364 - auth - INFO - Created new user: supuser
2025-07-07 15:00:24,022 - auth - INFO - Created new user: supuser
2025-07-07 15:00:28,724 - auth - INFO - Created new user: supuser
2025-07-07 15:00:33,386 - auth - INFO - Created new user: supuser
2025-07-07 15:00:38,062 - auth - INFO - Created new user: supuser
2025-07-07 15:00:42,703 - auth - INFO - Created new user: anno_sup
2025-07-07 15:00:47,377 - auth - INFO - Created new user: supuser
2025-07-07 15:00:52,072 - auth - INFO - Created new user: supuser
2025-07-07 15:00:56,783 - auth - INFO - Created new user: supuser
2025-07-07 15:00:57,166 - auth - INFO - Created new user: synthadmin
2025-07-07 15:00:57,678 - auth - INFO - Created new user: radio_user
2025-07-07 15:00:58,099 - auth - INFO - Created new user: tgadmin
2025-07-07 15:01:01,729 - auth - INFO - Created new user: concurrentuser
2025-07-07 17:06:38,329 - auth - INFO - Created new user: adminuser
2025-07-07 17:06:43,168 - auth - INFO - Created new user: adminuser
2025-07-07 17:06:47,921 - auth - INFO - Created new user: adminuser
2025-07-07 17:06:48,337 - auth - INFO - Created new user: newuser
2025-07-07 17:06:52,791 - auth - INFO - Created new user: adminuser
2025-07-07 17:06:53,249 - auth - INFO - Created new user: a
2025-07-07 17:06:53,506 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 17:06:53,743 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 17:06:58,288 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:02,981 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:08,028 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:16,770 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:21,384 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:26,004 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:30,664 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:35,321 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:39,974 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:44,644 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:49,263 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:53,933 - auth - INFO - Created new user: adminuser
2025-07-07 17:07:58,602 - auth - INFO - Created new user: adminuser
2025-07-07 17:08:03,205 - auth - INFO - Created new user: adminuser
2025-07-07 17:08:07,846 - auth - INFO - Created new user: adminuser
2025-07-07 17:08:12,479 - auth - INFO - Created new user: adminuser
2025-07-07 17:08:17,099 - auth - INFO - Created new user: adminuser
2025-07-07 17:08:21,757 - auth - INFO - Created new user: adminuser
2025-07-07 17:08:30,520 - auth - INFO - Created new user: anno_user
2025-07-07 17:08:35,111 - auth - INFO - Created new user: anno_user
2025-07-07 17:08:39,750 - auth - INFO - Created new user: anno_user
2025-07-07 17:08:44,645 - auth - INFO - Created new user: adminuser
2025-07-07 17:08:49,245 - auth - INFO - Created new user: adminuser
2025-07-07 17:08:49,447 - auth - INFO - Updated user: adminuser
2025-07-07 17:08:49,454 - auth - INFO - Updated user: adminuser
2025-07-07 17:08:53,877 - auth - INFO - Created new user: adminuser
2025-07-07 17:08:58,585 - auth - INFO - Created new user: adminuser
2025-07-07 17:09:03,278 - auth - INFO - Created new user: adminuser
2025-07-07 17:19:17,834 - auth - INFO - Created new user: audituser
2025-07-07 17:19:22,704 - auth - INFO - Created new user: audituser
2025-07-07 17:19:27,304 - auth - INFO - Created new user: audituser
2025-07-07 17:19:32,067 - auth - INFO - Created new user: audituser
2025-07-07 17:19:36,706 - auth - INFO - Created new user: audituser
2025-07-07 17:19:41,803 - auth - INFO - Created new user: audituser
2025-07-07 17:19:46,886 - auth - INFO - Created new user: audituser
2025-07-07 17:19:52,436 - auth - INFO - Created new user: audituser
2025-07-07 17:19:57,689 - auth - INFO - Created new user: wrongaudit
2025-07-07 17:23:48,645 - auth - INFO - Created new user: adminuser
2025-07-07 17:23:53,411 - auth - INFO - Created new user: adminuser
2025-07-07 17:23:58,030 - auth - INFO - Created new user: adminuser
2025-07-07 17:23:58,412 - auth - INFO - Created new user: newuser
2025-07-07 17:24:02,792 - auth - INFO - Created new user: adminuser
2025-07-07 17:24:03,176 - auth - INFO - Created new user: a
2025-07-07 17:24:03,370 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 17:24:03,563 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-07 17:24:07,950 - auth - INFO - Created new user: adminuser
2025-07-07 17:24:12,552 - auth - INFO - Created new user: adminuser
2025-07-07 17:24:17,193 - auth - INFO - Created new user: adminuser
2025-07-07 17:24:25,860 - auth - INFO - Created new user: adminuser
2025-07-07 17:24:30,477 - auth - INFO - Created new user: adminuser
2025-07-07 17:24:35,077 - auth - INFO - Created new user: adminuser
2025-07-07 17:24:39,687 - auth - INFO - Created new user: adminuser
2025-07-07 17:24:44,294 - auth - INFO - Created new user: adminuser
2025-07-07 17:24:48,963 - auth - INFO - Created new user: adminuser
2025-07-07 17:24:53,595 - auth - INFO - Created new user: adminuser
2025-07-07 17:24:58,208 - auth - INFO - Created new user: adminuser
2025-07-07 17:25:02,780 - auth - INFO - Created new user: adminuser
2025-07-07 17:25:07,361 - auth - INFO - Created new user: adminuser
2025-07-07 17:25:11,978 - auth - INFO - Created new user: adminuser
2025-07-07 17:25:16,578 - auth - INFO - Created new user: adminuser
2025-07-07 17:25:21,203 - auth - INFO - Created new user: adminuser
2025-07-07 17:25:25,796 - auth - INFO - Created new user: adminuser
2025-07-07 17:25:30,402 - auth - INFO - Created new user: adminuser
2025-07-07 17:25:39,045 - auth - INFO - Created new user: anno_user
2025-07-07 17:25:43,677 - auth - INFO - Created new user: anno_user
2025-07-07 17:25:48,252 - auth - INFO - Created new user: anno_user
2025-07-07 17:25:53,024 - auth - INFO - Created new user: adminuser
2025-07-07 17:25:57,609 - auth - INFO - Created new user: adminuser
2025-07-07 17:25:57,817 - auth - INFO - Updated user: adminuser
2025-07-07 17:25:57,817 - auth - INFO - Updated user: adminuser
2025-07-07 17:26:02,243 - auth - INFO - Created new user: adminuser
2025-07-07 17:26:07,244 - auth - INFO - Created new user: adminuser
2025-07-07 17:26:11,874 - auth - INFO - Created new user: adminuser
2025-07-07 17:27:49,096 - auth - INFO - Created new user: audituser
2025-07-07 17:27:53,937 - auth - INFO - Created new user: audituser
2025-07-07 17:27:58,547 - auth - INFO - Created new user: audituser
2025-07-07 17:28:03,179 - auth - INFO - Created new user: audituser
2025-07-07 17:28:07,779 - auth - INFO - Created new user: audituser
2025-07-07 17:28:12,364 - auth - INFO - Created new user: audituser
2025-07-07 17:28:17,017 - auth - INFO - Created new user: audituser
2025-07-07 17:28:21,632 - auth - INFO - Created new user: audituser
2025-07-07 17:28:26,647 - auth - INFO - Created new user: wrongaudit
2025-07-07 17:41:41,575 - auth - INFO - Created new user: annouser
2025-07-07 17:41:46,417 - auth - INFO - Created new user: annouser
2025-07-07 17:41:51,049 - auth - INFO - Created new user: annouser
2025-07-07 17:41:55,682 - auth - INFO - Created new user: annouser
2025-07-07 17:42:00,300 - auth - INFO - Created new user: annouser
2025-07-07 17:42:04,914 - auth - INFO - Created new user: annouser
2025-07-07 17:42:09,529 - auth - INFO - Created new user: annouser
2025-07-07 17:42:14,166 - auth - INFO - Created new user: annouser
2025-07-07 17:42:19,247 - auth - INFO - Created new user: annouser
2025-07-07 17:42:23,849 - auth - INFO - Created new user: annouser
2025-07-07 17:46:30,692 - auth - INFO - Created new user: audituser
2025-07-07 17:46:35,535 - auth - INFO - Created new user: audituser
2025-07-07 17:46:40,140 - auth - INFO - Created new user: audituser
2025-07-07 17:46:44,756 - auth - INFO - Created new user: audituser
2025-07-07 17:46:49,336 - auth - INFO - Created new user: audituser
2025-07-07 17:46:53,938 - auth - INFO - Created new user: audituser
2025-07-07 17:46:58,566 - auth - INFO - Created new user: audituser
2025-07-07 17:47:03,155 - auth - INFO - Created new user: audituser
2025-07-07 17:47:08,227 - auth - INFO - Created new user: wrongaudit
2025-07-07 17:48:21,186 - auth - INFO - Created new user: annouser
2025-07-07 17:48:25,993 - auth - INFO - Created new user: annouser
2025-07-07 17:48:30,555 - auth - INFO - Created new user: annouser
2025-07-07 17:48:35,215 - auth - INFO - Created new user: annouser
2025-07-07 17:48:39,826 - auth - INFO - Created new user: annouser
2025-07-07 17:48:44,452 - auth - INFO - Created new user: annouser
2025-07-07 17:48:49,069 - auth - INFO - Created new user: annouser
2025-07-07 17:48:53,685 - auth - INFO - Created new user: annouser
2025-07-07 17:48:58,283 - auth - INFO - Created new user: annouser
2025-07-07 17:49:02,883 - auth - INFO - Created new user: annouser
2025-07-07 18:00:45,497 - auth - INFO - Created new user: audituser
2025-07-07 18:00:50,284 - auth - INFO - Created new user: audituser
2025-07-07 18:00:54,868 - auth - INFO - Created new user: audituser
2025-07-07 18:00:59,445 - auth - INFO - Created new user: audituser
2025-07-07 18:01:04,051 - auth - INFO - Created new user: audituser
2025-07-07 18:01:08,676 - auth - INFO - Created new user: audituser
2025-07-07 18:01:13,289 - auth - INFO - Created new user: audituser
2025-07-07 18:01:17,891 - auth - INFO - Created new user: audituser
2025-07-07 18:01:22,487 - auth - INFO - Created new user: wrongaudit
2025-07-07 18:16:03,971 - auth - INFO - Created new user: audituser
2025-07-07 18:16:08,958 - auth - INFO - Created new user: audituser
2025-07-07 18:16:13,723 - auth - INFO - Created new user: audituser
2025-07-07 18:16:18,489 - auth - INFO - Created new user: audituser
2025-07-07 18:16:23,236 - auth - INFO - Created new user: audituser
2025-07-07 18:16:28,023 - auth - INFO - Created new user: audituser
2025-07-07 18:16:32,768 - auth - INFO - Created new user: audituser
2025-07-07 18:16:37,527 - auth - INFO - Created new user: audituser
2025-07-07 18:16:42,285 - auth - INFO - Created new user: wrongaudit
2025-07-07 18:25:41,644 - auth - INFO - Created new user: testuser
2025-07-07 18:25:54,653 - auth - INFO - Created new user: changepw
2025-07-07 18:25:55,206 - auth - INFO - Updated password for user: changepw
2025-07-07 18:25:59,774 - auth - INFO - Created new user: a
2025-07-07 18:26:04,141 - auth - INFO - Created new user: minpw
2025-07-07 18:26:08,513 - auth - INFO - Created new user: logoutuser
2025-07-07 18:26:21,949 - auth - INFO - Created new user: cpuser
2025-07-07 18:26:26,872 - auth - INFO - Created new user: du
2025-07-07 18:35:14,110 - auth - INFO - Created new user: testuser
2025-07-07 18:35:27,164 - auth - INFO - Created new user: changepw
2025-07-07 18:35:27,700 - auth - INFO - Updated password for user: changepw
2025-07-07 18:35:32,284 - auth - INFO - Created new user: a
2025-07-07 18:35:36,633 - auth - INFO - Created new user: minpw
2025-07-07 18:35:41,024 - auth - INFO - Created new user: logoutuser
2025-07-07 18:35:54,004 - auth - INFO - Created new user: cpuser
2025-07-07 18:35:58,787 - auth - INFO - Created new user: du
2025-07-07 18:35:59,166 - auth - INFO - Created new user: deluser
2025-07-08 14:20:09,185 - auth - INFO - Created new user: concurrentuser
2025-07-08 15:57:23,038 - auth - INFO - Created new user: secureuser
2025-07-08 15:57:23,822 - auth - INFO - Created new user: encuser
2025-07-08 15:57:24,182 - auth - INFO - Created new user: loginuser
2025-07-08 15:57:24,700 - auth - INFO - Created new user: meuser
2025-07-08 15:57:25,218 - auth - INFO - Created new user: refreshuser
2025-07-08 15:57:25,725 - auth - INFO - Created new user: cookieuser
2025-07-08 15:57:26,237 - auth - INFO - Created new user: changepassuser
2025-07-08 15:57:26,785 - auth - INFO - Updated password for user: changepassuser
2025-07-08 15:57:27,468 - auth - INFO - Created new user: changepassuser2
2025-07-08 15:57:28,151 - auth - INFO - Created new user: logoutuser
2025-07-08 16:36:09,376 - auth - INFO - Created new user: secureuser
2025-07-08 16:36:10,237 - auth - INFO - Created new user: encuser
2025-07-08 16:36:10,736 - auth - INFO - Created new user: loginuser
2025-07-08 16:36:11,312 - auth - INFO - Created new user: meuser
2025-07-08 16:36:11,886 - auth - INFO - Created new user: refreshuser
2025-07-08 16:36:12,444 - auth - INFO - Created new user: cookieuser
2025-07-08 16:36:12,965 - auth - INFO - Created new user: changepassuser
2025-07-08 16:36:13,521 - auth - INFO - Updated password for user: changepassuser
2025-07-08 16:36:14,233 - auth - INFO - Created new user: changepassuser2
2025-07-08 16:36:14,952 - auth - INFO - Created new user: logoutuser
2025-07-08 16:36:15,468 - auth - INFO - Created new user: verifyuser
2025-07-08 16:36:16,022 - auth - INFO - Created new user: annuser
2025-07-08 16:36:16,419 - auth - INFO - Created new user: adminuser
2025-07-08 16:36:16,819 - auth - INFO - Created new user: dupuser
2025-07-08 16:36:17,521 - auth - INFO - Created new user: cookieattruser
2025-07-08 16:36:18,055 - auth - INFO - Created new user: wrongtypeuser
2025-07-08 16:36:18,586 - auth - INFO - Created new user: refreshcookieuser
2025-07-08 16:36:19,123 - auth - INFO - Created new user: schemachangepass
2025-07-08 16:36:19,661 - auth - INFO - Created new user: tamperuser
2025-07-08 16:36:20,185 - auth - INFO - Created new user: auduser
2025-07-08 16:36:20,568 - auth - INFO - Created new user: realannotator
2025-07-08 16:36:21,094 - auth - INFO - Created new user: annforaud
2025-07-08 16:36:21,490 - auth - INFO - Created new user: trueauditor
2025-07-08 16:40:57,483 - auth - INFO - Created new user: secureuser
2025-07-08 16:40:58,250 - auth - INFO - Created new user: encuser
2025-07-08 16:40:58,590 - auth - INFO - Created new user: loginuser
2025-07-08 16:40:59,115 - auth - INFO - Created new user: meuser
2025-07-08 16:40:59,627 - auth - INFO - Created new user: refreshuser
2025-07-08 16:41:00,125 - auth - INFO - Created new user: cookieuser
2025-07-08 16:41:00,625 - auth - INFO - Created new user: changepassuser
2025-07-08 16:41:01,168 - auth - INFO - Updated password for user: changepassuser
2025-07-08 16:41:01,874 - auth - INFO - Created new user: changepassuser2
2025-07-08 16:41:02,544 - auth - INFO - Created new user: logoutuser
2025-07-08 16:41:03,048 - auth - INFO - Created new user: verifyuser
2025-07-08 16:41:03,595 - auth - INFO - Created new user: annuser
2025-07-08 16:41:03,994 - auth - INFO - Created new user: adminuser
2025-07-08 16:41:04,394 - auth - INFO - Created new user: dupuser
2025-07-08 16:41:04,708 - auth - INFO - Created new user: cookieattruser
2025-07-08 16:41:05,209 - auth - INFO - Created new user: wrongtypeuser
2025-07-08 16:41:05,714 - auth - INFO - Created new user: refreshcookieuser
2025-07-08 16:41:06,227 - auth - INFO - Created new user: schemachangepass
2025-07-08 16:41:06,745 - auth - INFO - Created new user: tamperuser
2025-07-08 16:41:07,251 - auth - INFO - Created new user: auduser
2025-07-08 16:41:07,629 - auth - INFO - Created new user: realannotator
2025-07-08 16:41:08,140 - auth - INFO - Created new user: annforaud
2025-07-08 16:41:08,504 - auth - INFO - Created new user: trueauditor
2025-07-08 16:51:41,212 - auth - INFO - Created new user: secureuser
2025-07-08 16:51:41,978 - auth - INFO - Created new user: encuser
2025-07-08 16:51:42,310 - auth - INFO - Created new user: loginuser
2025-07-08 16:51:42,839 - auth - INFO - Created new user: meuser
2025-07-08 16:51:43,344 - auth - INFO - Created new user: refreshuser
2025-07-08 16:51:43,852 - auth - INFO - Created new user: changepassuser
2025-07-08 16:51:44,395 - auth - INFO - Updated password for user: changepassuser
2025-07-08 16:51:45,075 - auth - INFO - Created new user: changepassuser2
2025-07-08 16:51:45,766 - auth - INFO - Created new user: logoutuser
2025-07-08 16:51:46,273 - auth - INFO - Created new user: verifyuser
2025-07-08 16:51:46,813 - auth - INFO - Created new user: annuser
2025-07-08 16:51:47,199 - auth - INFO - Created new user: adminuser
2025-07-08 16:51:47,579 - auth - INFO - Created new user: dupuser
2025-07-08 16:51:47,895 - auth - INFO - Created new user: cookieattruser
2025-07-08 16:51:48,410 - auth - INFO - Created new user: wrongtypeuser
2025-07-08 16:51:48,912 - auth - INFO - Created new user: refreshflowuser
2025-07-08 16:51:49,426 - auth - INFO - Created new user: schemachangepass
2025-07-08 16:51:49,947 - auth - INFO - Created new user: tamperuser
2025-07-08 16:51:50,472 - auth - INFO - Created new user: auduser
2025-07-08 16:51:50,854 - auth - INFO - Created new user: realannotator
2025-07-08 16:51:51,358 - auth - INFO - Created new user: annforaud
2025-07-08 16:51:51,731 - auth - INFO - Created new user: trueauditor
2025-07-08 17:47:24,296 - auth - INFO - Created new user: auser
2025-07-08 18:25:14,520 - auth - INFO - Created new user: perfuser
2025-07-11 10:23:22,521 - auth - INFO - Created new user: concuradmin
2025-07-11 10:23:22,967 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser1', 'Concurrent User 1', '<EMAIL>', 'annotator', '$2b$12$s5ujUFGpriLmJm/tA6UPQOipRW8S2osWeKAH6r8MbDiEB/tLnReU2', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 10:23:22,967 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser0', 'Concurrent User 0', '<EMAIL>', 'annotator', '$2b$12$wGoNG.csI84Cjy3D5n1YF.MV2W1jZFnSQPYgeQtESOrMw5fHQ6B/G', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 10:23:22,967 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 10:23:22,967 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser4', 'Concurrent User 4', '<EMAIL>', 'annotator', '$2b$12$eqJZ2kDQHQh.PYuEfqNWguwqtZSKIkLfv4kVP5t6atfuf0xUD5/1e', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 10:23:22,967 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot start a transaction within a transaction
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser3', 'Concurrent User 3', '<EMAIL>', 'annotator', '$2b$12$2GP3COcjg7dA5IXnnXsOMOuyhBjosTW4Qs2bwJ08qCbNJ2P7OA8h2', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 10:23:23,526 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$sXE696Zg6gBCoPyn82FlXugt8gkwO0jOWQXmEGPj2hkjoENaMhTUu', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 10:23:23,527 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$ty/rZVzYGa/OSDS7QOG3/OwPoexu0/ZffSrCsvF1P/up2v87yYg8a', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-11 10:23:23,530 - auth - INFO - Created new user: duplicateuser
2025-07-11 10:23:23,720 - auth - INFO - Created new user: annotator_conc
2025-07-11 10:23:24,720 - auth - INFO - Created new user: fileadmin
2025-07-11 10:23:24,925 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 10:25:14,929 - auth - INFO - Created new user: concuradmin
2025-07-11 10:25:15,377 - auth - INFO - Created new user: concuser1
2025-07-11 10:25:15,377 - auth - INFO - Created new user: concuser0
2025-07-11 10:25:15,381 - auth - INFO - Created new user: concuser3
2025-07-11 10:25:15,385 - auth - INFO - Created new user: concuser4
2025-07-11 10:25:15,953 - auth - INFO - Created new user: duplicateuser2
2025-07-11 10:25:15,953 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser2', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$4yZaFUWIs7NZxI51KW1vwuYCKoHkEUvUd28DCM2ClMRUw6fVcnbqS', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-11 10:25:15,969 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser2', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$kIRSt17jwzuUj52jiffWAuFiieW.1Au7yH6DnS8iYrqZKKrivwsSG', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-11 10:25:16,164 - auth - INFO - Created new user: annotator_conc2
2025-07-11 10:25:16,988 - auth - INFO - Created new user: fileadmin2
2025-07-11 10:28:22,350 - auth - INFO - Created new user: concuradmin1752209902124
2025-07-11 10:28:22,813 - auth - INFO - Created new user: concuser17522099025951
2025-07-11 10:28:22,816 - auth - INFO - Created new user: concuser17522099025952
2025-07-11 10:28:22,821 - auth - INFO - Created new user: concuser17522099025950
2025-07-11 10:28:22,822 - auth - INFO - Created new user: concuser17522099025954
2025-07-11 10:28:22,825 - auth - INFO - Created new user: concuser17522099025953
2025-07-11 10:28:23,030 - auth - INFO - Created new user: duplicateuser1752209902829
2025-07-11 10:28:23,032 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752209902829', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$lyFVY39nPjyKkHTSxwUZZeI.JvaBO/InySVEKmhCoCCTBXFPTR5TO', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-11 10:28:23,039 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752209902829', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$yUUZ3YiQJAB8VRY/F4iLHOaNWwyKnnv4PaZLStHnMcZyw.4XxPFb.', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-11 10:34:13,931 - auth - INFO - Created new user: kbadmin
2025-07-11 10:34:14,629 - auth - INFO - Created new user: kbaudit
2025-07-11 10:47:28,656 - auth - INFO - Created new user: wpuser
2025-07-11 10:47:29,168 - auth - INFO - Created new user: ehadmin
2025-07-11 10:47:29,708 - auth - INFO - Created new user: ehadmin
2025-07-11 11:01:51,871 - auth - INFO - Created new user: authzanno
2025-07-11 11:01:52,254 - auth - INFO - Created new user: authzauditor
2025-07-11 11:01:52,636 - auth - INFO - Created new user: authzclient
2025-07-11 11:01:53,011 - auth - INFO - Created new user: authzadmin
2025-07-11 11:10:17,870 - auth - INFO - Created new user: smoketest
2025-07-11 11:28:04,183 - auth - INFO - Created new user: contract_admin
2025-07-11 11:28:04,559 - auth - INFO - Created new user: contract_anno
2025-07-11 11:28:04,929 - auth - INFO - Created new user: contract_auditor
2025-07-11 11:28:05,303 - auth - INFO - Created new user: contract_client
2025-07-11 11:32:49,061 - auth - INFO - Created new user: wpuser
2025-07-11 11:32:49,613 - auth - INFO - Created new user: ehadmin
2025-07-11 11:32:50,163 - auth - INFO - Created new user: ehadmin
2025-07-11 11:35:42,707 - auth - INFO - Created new user: wpuser
2025-07-11 11:35:44,178 - auth - INFO - Created new user: ehadmin
2025-07-11 11:35:44,692 - auth - INFO - Created new user: ehadmin
2025-07-11 11:35:45,211 - auth - INFO - Created new user: ehannotator
2025-07-11 11:35:45,723 - auth - INFO - Created new user: ehannotator
2025-07-11 11:35:46,643 - auth - INFO - Created new user: largeuser
2025-07-11 11:35:46,829 - auth - INFO - Created new user: concurrentuser
2025-07-11 11:38:26,245 - auth - INFO - Created new user: wpuser
2025-07-11 11:38:26,756 - auth - INFO - Created new user: ehadmin
2025-07-11 11:38:27,300 - auth - INFO - Created new user: ehadmin
2025-07-11 11:38:27,832 - auth - INFO - Created new user: ehannotator
2025-07-11 11:38:28,360 - auth - INFO - Created new user: ehannotator
2025-07-11 11:38:29,352 - auth - INFO - Created new user: ehannotator
2025-07-11 11:38:29,818 - auth - INFO - Created new user: largeuser
2025-07-11 11:38:30,018 - auth - INFO - Created new user: concurrentuser
2025-07-11 11:40:23,050 - auth - INFO - Created new user: wpuser
2025-07-11 11:40:23,600 - auth - INFO - Created new user: ehadmin
2025-07-11 11:40:24,166 - auth - INFO - Created new user: ehadmin
2025-07-11 11:40:24,688 - auth - INFO - Created new user: ehannotator
2025-07-11 11:40:25,235 - auth - INFO - Created new user: ehannotator
2025-07-11 11:40:25,782 - auth - INFO - Created new user: ehannotator
2025-07-11 11:40:26,561 - auth - INFO - Created new user: largeuser
2025-07-11 11:40:26,748 - auth - INFO - Created new user: concurrentuser
2025-07-11 11:41:32,695 - auth - INFO - Created new user: wpuser
2025-07-11 11:41:33,207 - auth - INFO - Created new user: ehadmin
2025-07-11 11:41:33,739 - auth - INFO - Created new user: ehadmin
2025-07-11 11:41:34,256 - auth - INFO - Created new user: ehannotator
2025-07-11 11:41:34,765 - auth - INFO - Created new user: ehannotator
2025-07-11 11:41:35,310 - auth - INFO - Created new user: ehannotator
2025-07-11 11:41:35,728 - auth - INFO - Created new user: largeuser
2025-07-11 11:41:35,927 - auth - INFO - Created new user: concurrentuser
2025-07-11 11:41:36,889 - auth - ERROR - Database error in get_user_by_username: (sqlite3.OperationalError) no such table: users
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.full_name AS users_full_name, users.email AS users_email, users.role AS users_role, users.password_hash AS users_password_hash, users.created_at AS users_created_at, users.last_login AS users_last_login, users.is_active AS users_is_active, users.annotator_mode AS users_annotator_mode 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('contract_admin', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 12:36:33,406 - auth - INFO - Created new user: load_admin_2188
2025-07-11 12:53:15,916 - auth - INFO - Created new user: load_admin_5793
2025-07-11 13:00:48,193 - auth - INFO - Created new user: load_admin_2951
2025-07-11 13:21:58,648 - auth - INFO - Created new user: test_admin
2025-07-11 13:31:43,491 - auth - INFO - Created new user: load_annotator_6730
2025-07-11 13:32:29,647 - auth - INFO - Created new user: load_annotator_7206
2025-07-11 13:32:30,654 - auth - INFO - Created new user: load_auditor_7159
2025-07-11 13:32:32,649 - auth - INFO - Created new user: load_client_6059
2025-07-11 13:37:03,652 - auth - INFO - Created new user: load_annotator_6677
2025-07-11 13:38:14,367 - auth - INFO - Created new user: load_annotator_1941
2025-07-11 13:38:15,358 - auth - INFO - Created new user: load_auditor_7708
2025-07-11 13:38:17,419 - auth - INFO - Created new user: load_client_4097
2025-07-11 13:57:17,864 - auth - INFO - Created new user: contract_test_user
2025-07-11 13:57:18,087 - auth - INFO - Created new user: contract_login_user
2025-07-11 14:05:25,268 - auth - INFO - Created new user: adminuser
2025-07-11 14:05:30,019 - auth - INFO - Created new user: adminuser
2025-07-11 14:05:34,604 - auth - INFO - Created new user: adminuser
2025-07-11 14:05:34,983 - auth - INFO - Created new user: newuser
2025-07-11 14:05:39,416 - auth - INFO - Created new user: adminuser
2025-07-11 14:05:39,799 - auth - INFO - Created new user: a
2025-07-11 14:05:39,982 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-11 14:05:40,184 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-11 14:05:44,575 - auth - INFO - Created new user: adminuser
2025-07-11 14:05:49,166 - auth - INFO - Created new user: adminuser
2025-07-11 14:05:53,755 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:02,432 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:07,041 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:11,632 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:16,233 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:20,832 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:25,452 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:30,057 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:34,661 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:39,279 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:43,897 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:48,509 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:53,121 - auth - INFO - Created new user: adminuser
2025-07-11 14:06:57,806 - auth - INFO - Created new user: adminuser
2025-07-11 14:07:02,408 - auth - INFO - Created new user: adminuser
2025-07-11 14:07:07,019 - auth - INFO - Created new user: adminuser
2025-07-11 14:07:15,718 - auth - INFO - Created new user: anno_user
2025-07-11 14:07:20,313 - auth - INFO - Created new user: anno_user
2025-07-11 14:07:24,867 - auth - INFO - Created new user: anno_user
2025-07-11 14:07:29,658 - auth - INFO - Created new user: adminuser
2025-07-11 14:07:34,264 - auth - INFO - Created new user: adminuser
2025-07-11 14:07:34,478 - auth - INFO - Updated user: adminuser
2025-07-11 14:07:34,485 - auth - INFO - Updated user: adminuser
2025-07-11 14:07:38,914 - auth - INFO - Created new user: adminuser
2025-07-11 14:07:43,565 - auth - INFO - Created new user: adminuser
2025-07-11 14:07:48,145 - auth - INFO - Created new user: adminuser
2025-07-11 14:07:52,769 - auth - INFO - Created new user: annouser
2025-07-11 14:07:57,409 - auth - INFO - Created new user: annouser
2025-07-11 14:08:02,041 - auth - INFO - Created new user: annouser
2025-07-11 14:08:06,659 - auth - INFO - Created new user: annouser
2025-07-11 14:08:11,239 - auth - INFO - Created new user: annouser
2025-07-11 14:08:15,862 - auth - INFO - Created new user: annouser
2025-07-11 14:08:20,524 - auth - INFO - Created new user: annouser
2025-07-11 14:08:25,142 - auth - INFO - Created new user: annouser
2025-07-11 14:08:29,750 - auth - INFO - Created new user: annouser
2025-07-11 14:08:34,386 - auth - INFO - Created new user: annouser
2025-07-11 14:08:39,045 - auth - INFO - Created new user: audituser
2025-07-11 14:08:43,695 - auth - INFO - Created new user: audituser
2025-07-11 14:08:48,346 - auth - INFO - Created new user: audituser
2025-07-11 14:08:52,954 - auth - INFO - Created new user: audituser
2025-07-11 14:08:57,622 - auth - INFO - Created new user: audituser
2025-07-11 14:09:02,268 - auth - INFO - Created new user: audituser
2025-07-11 14:09:06,894 - auth - INFO - Created new user: audituser
2025-07-11 14:09:11,470 - auth - INFO - Created new user: audituser
2025-07-11 14:09:16,066 - auth - INFO - Created new user: wrongaudit
2025-07-11 14:09:20,731 - auth - INFO - Created new user: testuser
2025-07-11 14:09:33,741 - auth - INFO - Created new user: changepw
2025-07-11 14:09:34,312 - auth - INFO - Updated password for user: changepw
2025-07-11 14:09:38,917 - auth - INFO - Created new user: a
2025-07-11 14:09:43,326 - auth - INFO - Created new user: minpw
2025-07-11 14:09:47,749 - auth - INFO - Created new user: logoutuser
2025-07-11 14:10:00,737 - auth - INFO - Created new user: cpuser
2025-07-11 14:10:05,519 - auth - INFO - Created new user: du
2025-07-11 14:10:05,909 - auth - INFO - Created new user: deluser
2025-07-11 14:10:10,492 - auth - INFO - Created new user: clientuser
2025-07-11 14:10:15,093 - auth - INFO - Created new user: clientuser
2025-07-11 14:10:19,559 - auth - INFO - Created new user: notclient
2025-07-11 14:10:19,963 - auth - INFO - Created new user: concuradmin1752223219764
2025-07-11 14:10:20,414 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot commit transaction - SQL statements in progress
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:10:20,415 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17522232202013', 'Concurrent User 17522232202013', '<EMAIL>', 'annotator', '$2b$12$kvILHLS2eYiPlhr.Xm5LXulahjbyovFdWseq1xncB1SKVbBT5Uioa', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 14:10:20,419 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17522232202011', 'Concurrent User 17522232202011', '<EMAIL>', 'annotator', '$2b$12$UBiUD1aL..7eo8DHEdkuVe7SnPzQ/APeOOxQY5gHRQUeTUtpg8XJy', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 14:10:20,424 - auth - INFO - Created new user: concuser17522232202012
2025-07-11 14:10:20,636 - auth - INFO - Created new user: duplicateuser1752223220431
2025-07-11 14:10:20,639 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752223220431', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$g7.ovAsmguWniqeiqB4AAOBhdpgQ48EIT1G4BF3sd8VGf.u9CbnNm', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-11 14:10:20,642 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752223220431', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$GnbN1fLnMHUpQtKqfIEK5.LcFipiHRFM1w890NoabPEmuWeq.KaDi', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-11 14:10:20,843 - auth - INFO - Created new user: annotator_conc2
2025-07-11 14:10:21,760 - auth - INFO - Created new user: fileadmin2
2025-07-11 14:10:21,999 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 14:10:22,488 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Error binding parameter 0 - probably unsupported type.
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.full_name AS users_full_name, users.email AS users_email, users.role AS users_role, users.password_hash AS users_password_hash, users.created_at AS users_created_at, users.last_login AS users_last_login, users.is_active AS users_is_active, users.annotator_mode AS users_annotator_mode 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('wpuser', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 14:10:22,870 - auth - INFO - Created new user: ehadmin
2025-07-11 14:10:23,421 - auth - INFO - Created new user: ehadmin
2025-07-11 14:10:23,957 - auth - INFO - Created new user: ehannotator
2025-07-11 14:10:24,495 - auth - INFO - Created new user: ehannotator
2025-07-11 14:10:25,081 - auth - INFO - Created new user: ehannotator
2025-07-11 14:10:25,531 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('largeuser', 'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA ... (9702 characters truncated) ... AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA', '<EMAIL>', 'annotator', '$2b$12$rd8Q9i3z2WEg83/RPCtYN.4l5BCdQRGXFRiqrPtQq8fGnlHgGDusm', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:10:25,728 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concurrentuser', 'Concurrent User', '<EMAIL>', 'annotator', '$2b$12$sVQXmM8yTVlORDS74DwVZ.MCQUKZlPsO0cRCZeWorBX.sDFq/izX2', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:10:26,948 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('kbadmin', 'KB Admin', '<EMAIL>', 'admin', '$2b$12$sbNNnFuQLuYqFz47p7NvbefcN93dGB79BuQWzhyX.TnhlC1e/2T/a', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:10:27,249 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('kbaudit', 'KB Audit', '<EMAIL>', 'annotator', '$2b$12$kd1wxplWJ9dLmBnUxu5msu0RFtp36L1Hir6tdWL79swQUQDXKbrlu', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:10:28,026 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('perfuser', 'Performance User', '<EMAIL>', 'annotator', '$2b$12$lG334Q6g7FyXDUiO64.ocOEFEn1ANP4fB1sABs4el/4b1UzI.XYHu', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:10:29,933 - auth - INFO - Created new user: auser
2025-07-11 14:10:30,599 - auth - INFO - Created new user: secureuser
2025-07-11 14:10:31,493 - auth - INFO - Created new user: encuser
2025-07-11 14:10:31,862 - auth - INFO - Created new user: loginuser
2025-07-11 14:10:32,420 - auth - INFO - Created new user: meuser
2025-07-11 14:10:32,980 - auth - INFO - Created new user: refreshuser
2025-07-11 14:10:33,547 - auth - INFO - Created new user: changepassuser
2025-07-11 14:10:34,117 - auth - INFO - Updated password for user: changepassuser
2025-07-11 14:10:34,857 - auth - INFO - Created new user: changepassuser2
2025-07-11 14:10:35,583 - auth - INFO - Created new user: logoutuser
2025-07-11 14:10:36,120 - auth - INFO - Created new user: verifyuser
2025-07-11 14:10:36,682 - auth - INFO - Created new user: annuser
2025-07-11 14:10:37,066 - auth - INFO - Created new user: adminuser
2025-07-11 14:10:37,467 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('dupuser', 'Dup User', '<EMAIL>', 'annotator', '$2b$12$Uf7ebhU6QDBpFCtaAys7ju4PP3/YgwZ/IlShKu1hhRhXxneEMAnm.', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:10:37,781 - auth - INFO - Created new user: cookieattruser
2025-07-11 14:10:38,308 - auth - INFO - Created new user: wrongtypeuser
2025-07-11 14:10:38,823 - auth - INFO - Created new user: refreshflowuser
2025-07-11 14:10:39,347 - auth - INFO - Created new user: schemachangepass
2025-07-11 14:10:39,910 - auth - INFO - Created new user: tamperuser
2025-07-11 14:10:40,435 - auth - INFO - Created new user: auduser
2025-07-11 14:10:40,824 - auth - INFO - Created new user: realannotator
2025-07-11 14:10:41,336 - auth - INFO - Created new user: annforaud
2025-07-11 14:10:41,736 - auth - INFO - Created new user: trueauditor
2025-07-11 14:10:46,336 - auth - INFO - Created new user: supuser
2025-07-11 14:10:50,927 - auth - INFO - Created new user: supuser
2025-07-11 14:10:55,522 - auth - INFO - Created new user: supuser
2025-07-11 14:11:00,136 - auth - INFO - Created new user: supuser
2025-07-11 14:11:04,716 - auth - INFO - Created new user: supuser
2025-07-11 14:11:09,336 - auth - INFO - Created new user: supuser
2025-07-11 14:11:14,033 - auth - INFO - Created new user: supuser
2025-07-11 14:11:18,631 - auth - INFO - Created new user: supuser
2025-07-11 14:11:23,207 - auth - INFO - Created new user: supuser
2025-07-11 14:11:27,770 - auth - INFO - Created new user: anno_sup
2025-07-11 14:11:32,349 - auth - INFO - Created new user: supuser
2025-07-11 14:11:36,911 - auth - INFO - Created new user: supuser
2025-07-11 14:11:41,511 - auth - INFO - Created new user: supuser
2025-07-11 14:11:46,080 - auth - INFO - Created new user: supuser
2025-07-11 14:11:46,474 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('synthadmin', 'Synth Admin', '<EMAIL>', 'admin', '$2b$12$4owA7BNocSprHpUDKj9j0.ROzC8z38v1rqjqRvovTXL5fzoOhuolG', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:11:46,698 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('radio_user', 'Radio User', '<EMAIL>', 'annotator', '$2b$12$vR2U.vpGfpx/iOSJN.6TyuUoopRliSUvUEKOsvOWUz7rvwqK9V7Qq', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:11:46,934 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('tgadmin', 'TG Admin', '<EMAIL>', 'admin', '$2b$12$/9T.nJywtfp93Aqait7jleV.67Rj7ceiCzelj9i3S8ldsguKhwOMa', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:11:47,380 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concurrentuser', 'Concurrent User', '<EMAIL>', 'annotator', '$2b$12$G.vkSEvDk.5psRu/eatBXub7SGOHA/wt4mRFmo7UvK5gI78fw5jo.', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:40:47,983 - auth - INFO - Created new user: adminuser
2025-07-11 14:40:52,767 - auth - INFO - Created new user: adminuser
2025-07-11 14:40:57,336 - auth - INFO - Created new user: adminuser
2025-07-11 14:40:57,707 - auth - INFO - Created new user: newuser
2025-07-11 14:41:02,077 - auth - INFO - Created new user: adminuser
2025-07-11 14:41:02,441 - auth - INFO - Created new user: a
2025-07-11 14:41:02,637 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-11 14:41:02,816 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-11 14:41:07,204 - auth - INFO - Created new user: adminuser
2025-07-11 14:41:11,811 - auth - INFO - Created new user: adminuser
2025-07-11 14:41:16,397 - auth - INFO - Created new user: adminuser
2025-07-11 14:41:25,065 - auth - INFO - Created new user: adminuser
2025-07-11 14:41:29,616 - auth - INFO - Created new user: adminuser
2025-07-11 14:41:34,221 - auth - INFO - Created new user: adminuser
2025-07-11 14:41:38,812 - auth - INFO - Created new user: adminuser
2025-07-11 14:41:43,411 - auth - INFO - Created new user: adminuser
2025-07-11 14:41:48,026 - auth - INFO - Created new user: adminuser
2025-07-11 14:41:52,649 - auth - INFO - Created new user: adminuser
2025-07-11 14:41:57,237 - auth - INFO - Created new user: adminuser
2025-07-11 14:42:01,869 - auth - INFO - Created new user: adminuser
2025-07-11 14:42:06,479 - auth - INFO - Created new user: adminuser
2025-07-11 14:42:11,091 - auth - INFO - Created new user: adminuser
2025-07-11 14:42:15,672 - auth - INFO - Created new user: adminuser
2025-07-11 14:42:20,266 - auth - INFO - Created new user: adminuser
2025-07-11 14:42:24,830 - auth - INFO - Created new user: adminuser
2025-07-11 14:42:29,384 - auth - INFO - Created new user: adminuser
2025-07-11 14:42:38,015 - auth - INFO - Created new user: anno_user
2025-07-11 14:42:42,618 - auth - INFO - Created new user: anno_user
2025-07-11 14:42:47,216 - auth - INFO - Created new user: anno_user
2025-07-11 14:42:51,980 - auth - INFO - Created new user: adminuser
2025-07-11 14:42:56,570 - auth - INFO - Created new user: adminuser
2025-07-11 14:42:56,792 - auth - INFO - Updated user: adminuser
2025-07-11 14:42:56,799 - auth - INFO - Updated user: adminuser
2025-07-11 14:43:01,207 - auth - INFO - Created new user: adminuser
2025-07-11 14:43:05,831 - auth - INFO - Created new user: adminuser
2025-07-11 14:43:10,453 - auth - INFO - Created new user: adminuser
2025-07-11 14:43:15,099 - auth - INFO - Created new user: annouser
2025-07-11 14:43:19,736 - auth - INFO - Created new user: annouser
2025-07-11 14:43:24,352 - auth - INFO - Created new user: annouser
2025-07-11 14:43:28,945 - auth - INFO - Created new user: annouser
2025-07-11 14:43:33,556 - auth - INFO - Created new user: annouser
2025-07-11 14:43:38,164 - auth - INFO - Created new user: annouser
2025-07-11 14:43:42,753 - auth - INFO - Created new user: annouser
2025-07-11 14:43:47,363 - auth - INFO - Created new user: annouser
2025-07-11 14:43:51,976 - auth - INFO - Created new user: annouser
2025-07-11 14:43:56,602 - auth - INFO - Created new user: annouser
2025-07-11 14:44:01,212 - auth - INFO - Created new user: audituser
2025-07-11 14:44:05,848 - auth - INFO - Created new user: audituser
2025-07-11 14:44:10,465 - auth - INFO - Created new user: audituser
2025-07-11 14:44:15,092 - auth - INFO - Created new user: audituser
2025-07-11 14:44:19,705 - auth - INFO - Created new user: audituser
2025-07-11 14:44:24,333 - auth - INFO - Created new user: audituser
2025-07-11 14:44:28,970 - auth - INFO - Created new user: audituser
2025-07-11 14:44:33,601 - auth - INFO - Created new user: audituser
2025-07-11 14:44:38,216 - auth - INFO - Created new user: wrongaudit
2025-07-11 14:44:42,884 - auth - INFO - Created new user: testuser
2025-07-11 14:44:55,982 - auth - INFO - Created new user: changepw
2025-07-11 14:44:56,548 - auth - INFO - Updated password for user: changepw
2025-07-11 14:45:01,120 - auth - INFO - Created new user: a
2025-07-11 14:45:05,556 - auth - INFO - Created new user: minpw
2025-07-11 14:45:09,974 - auth - INFO - Created new user: logoutuser
2025-07-11 14:45:23,013 - auth - INFO - Created new user: cpuser
2025-07-11 14:45:27,796 - auth - INFO - Created new user: du
2025-07-11 14:45:28,177 - auth - INFO - Created new user: deluser
2025-07-11 14:45:32,763 - auth - INFO - Created new user: clientuser
2025-07-11 14:45:37,374 - auth - INFO - Created new user: clientuser
2025-07-11 14:45:41,885 - auth - INFO - Created new user: notclient
2025-07-11 14:45:42,299 - auth - INFO - Created new user: concuradmin1752225342097
2025-07-11 14:45:42,746 - auth - INFO - Created new user: concuser17522253425253
2025-07-11 14:45:42,746 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17522253425250', 'Concurrent User 17522253425250', '<EMAIL>', 'annotator', '$2b$12$rus70RTA4F2NM/p4V.ItOu.C7u52/J30uGZa68sMu0fGV9G6iqOIm', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 14:45:42,746 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17522253425252', 'Concurrent User 17522253425252', '<EMAIL>', 'annotator', '$2b$12$ArP9tYGGWbSdvRMvrqEGkeVhOGrHKnT0qKxi8W2fA5paiaEzWLXie', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 14:45:42,746 - auth - ERROR - Database error in create_user: Could not refresh instance '<User at 0x16e49b9dde0>'
2025-07-11 14:45:42,746 - auth - INFO - Created new user: concuser17522253425251
2025-07-11 14:45:43,044 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752225342746', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$iVYG1Bh7HlRL3AiqWltbwuCAEgaOBh6s77SChaE5LYc/4xgwc4ZKO', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-11 14:45:43,044 - auth - INFO - Created new user: duplicateuser1752225342746
2025-07-11 14:45:43,044 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752225342746', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$4.1usz6O6IY.KH5qzWsBRutvAljRaXbO8l5fS6uX9VXXMdh.sPFKS', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-11 14:45:43,234 - auth - INFO - Created new user: annotator_conc2
2025-07-11 14:45:44,065 - auth - INFO - Created new user: fileadmin2
2025-07-11 14:45:44,310 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-11 14:45:44,953 - auth - INFO - Created new user: wpuser
2025-07-11 14:45:45,589 - auth - INFO - Created new user: ehadmin
2025-07-11 14:45:46,159 - auth - INFO - Created new user: ehadmin
2025-07-11 14:45:46,704 - auth - INFO - Created new user: ehannotator
2025-07-11 14:45:47,292 - auth - INFO - Created new user: ehannotator
2025-07-11 14:45:47,880 - auth - INFO - Created new user: ehannotator
2025-07-11 14:45:48,300 - auth - INFO - Created new user: largeuser
2025-07-11 14:45:48,495 - auth - INFO - Created new user: concurrentuser
2025-07-11 14:45:54,549 - auth - INFO - Created new user: testadmin
2025-07-11 14:45:59,160 - auth - INFO - Created new user: testadmin
2025-07-11 14:46:03,744 - auth - INFO - Created new user: testadmin
2025-07-11 14:46:08,355 - auth - INFO - Created new user: testadmin
2025-07-11 14:46:12,901 - auth - INFO - Created new user: testadmin
2025-07-11 14:46:17,520 - auth - INFO - Created new user: testadmin
2025-07-11 14:46:22,118 - auth - INFO - Created new user: testadmin
2025-07-11 14:46:26,739 - auth - INFO - Created new user: testadmin
2025-07-11 14:46:31,348 - auth - INFO - Created new user: testadmin
2025-07-11 14:46:35,980 - auth - INFO - Created new user: testadmin
2025-07-11 14:46:40,428 - auth - INFO - Created new user: testannotator
2025-07-11 14:46:41,301 - auth - INFO - Created new user: perfuser
2025-07-11 14:46:43,082 - auth - INFO - Created new user: auser
2025-07-11 14:46:43,660 - auth - INFO - Created new user: secureuser
2025-07-11 14:46:44,385 - auth - INFO - Created new user: encuser
2025-07-11 14:46:44,728 - auth - INFO - Created new user: loginuser
2025-07-11 14:46:45,253 - auth - INFO - Created new user: meuser
2025-07-11 14:46:45,744 - auth - INFO - Created new user: refreshuser
2025-07-11 14:46:46,274 - auth - INFO - Created new user: changepassuser
2025-07-11 14:46:46,822 - auth - INFO - Updated password for user: changepassuser
2025-07-11 14:46:47,534 - auth - INFO - Created new user: changepassuser2
2025-07-11 14:46:48,222 - auth - INFO - Created new user: logoutuser
2025-07-11 14:46:48,711 - auth - INFO - Created new user: verifyuser
2025-07-11 14:46:49,246 - auth - INFO - Created new user: annuser
2025-07-11 14:46:49,630 - auth - INFO - Created new user: adminuser
2025-07-11 14:46:50,001 - auth - INFO - Created new user: dupuser
2025-07-11 14:46:50,317 - auth - INFO - Created new user: cookieattruser
2025-07-11 14:46:50,832 - auth - INFO - Created new user: wrongtypeuser
2025-07-11 14:46:51,332 - auth - INFO - Created new user: refreshflowuser
2025-07-11 14:46:51,843 - auth - INFO - Created new user: schemachangepass
2025-07-11 14:46:52,363 - auth - INFO - Created new user: tamperuser
2025-07-11 14:46:52,860 - auth - INFO - Created new user: auduser
2025-07-11 14:46:53,230 - auth - INFO - Created new user: realannotator
2025-07-11 14:46:53,783 - auth - INFO - Created new user: annforaud
2025-07-11 14:46:54,163 - auth - INFO - Created new user: trueauditor
2025-07-11 14:46:58,810 - auth - INFO - Created new user: supuser
2025-07-11 14:47:03,439 - auth - INFO - Created new user: supuser
2025-07-11 14:47:08,159 - auth - INFO - Created new user: supuser
2025-07-11 14:47:12,807 - auth - INFO - Created new user: supuser
2025-07-11 14:47:17,425 - auth - INFO - Created new user: supuser
2025-07-11 14:47:22,091 - auth - INFO - Created new user: supuser
2025-07-11 14:47:26,745 - auth - INFO - Created new user: supuser
2025-07-11 14:47:31,468 - auth - INFO - Created new user: supuser
2025-07-11 14:47:36,128 - auth - INFO - Created new user: supuser
2025-07-11 14:47:40,818 - auth - INFO - Created new user: supuser
2025-07-11 14:47:45,434 - auth - INFO - Created new user: supuser
2025-07-11 14:47:50,050 - auth - INFO - Created new user: supuser
2025-07-11 14:47:54,648 - auth - INFO - Created new user: supuser
2025-07-11 14:47:59,239 - auth - INFO - Created new user: supuser
2025-07-11 14:48:03,845 - auth - INFO - Created new user: supuser
2025-07-11 14:48:08,536 - auth - INFO - Created new user: supuser
2025-07-11 14:48:13,170 - auth - INFO - Created new user: supuser
2025-07-11 14:48:17,795 - auth - INFO - Created new user: supuser
2025-07-11 14:48:26,659 - auth - INFO - Created new user: testadmin
2025-07-11 14:48:31,250 - auth - INFO - Created new user: testadmin
2025-07-11 14:48:35,834 - auth - INFO - Created new user: testadmin
2025-07-11 14:48:40,480 - auth - INFO - Created new user: testadmin
2025-07-11 14:48:49,377 - auth - INFO - Created new user: testannotator
2025-07-11 14:48:53,978 - auth - INFO - Created new user: testadmin
2025-07-11 14:48:58,540 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:03,169 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:07,766 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:12,374 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:16,939 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:21,534 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:26,148 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:30,744 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:35,358 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:39,948 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:44,556 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:49,248 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:53,828 - auth - INFO - Created new user: testadmin
2025-07-11 14:49:58,439 - auth - INFO - Created new user: testadmin
2025-07-11 14:50:03,003 - auth - INFO - Created new user: testadmin
2025-07-11 14:50:07,567 - auth - INFO - Created new user: testadmin
2025-07-11 14:50:12,134 - auth - INFO - Created new user: testadmin
2025-07-11 14:50:16,744 - auth - INFO - Created new user: testadmin
2025-07-11 14:50:21,324 - auth - INFO - Created new user: testadmin
2025-07-11 14:50:25,885 - auth - INFO - Created new user: testadmin
2025-07-11 14:50:30,471 - auth - INFO - Created new user: testadmin
2025-07-11 14:50:35,053 - auth - INFO - Created new user: testadmin
2025-07-11 14:50:39,666 - auth - INFO - Created new user: testadmin
2025-07-11 14:50:44,273 - auth - INFO - Created new user: testadmin
2025-07-11 14:53:29,099 - auth - INFO - Created new user: synthadmin
2025-07-11 14:53:30,116 - auth - INFO - Created new user: radio_user
2025-07-11 14:53:55,333 - auth - INFO - Created new user: tgadmin
2025-07-11 14:54:20,831 - auth - INFO - Created new user: supuser
2025-07-11 14:54:25,488 - auth - INFO - Created new user: supuser
2025-07-11 14:54:30,053 - auth - INFO - Created new user: supuser
2025-07-11 14:54:34,631 - auth - INFO - Created new user: supuser
2025-07-11 14:54:39,207 - auth - INFO - Created new user: supuser
2025-07-11 14:54:43,783 - auth - INFO - Created new user: supuser
2025-07-11 14:54:48,372 - auth - INFO - Created new user: supuser
2025-07-11 14:54:52,995 - auth - INFO - Created new user: supuser
2025-07-11 14:54:58,088 - auth - INFO - Created new user: supuser
2025-07-11 14:55:02,667 - auth - INFO - Created new user: anno_sup
2025-07-11 14:55:07,289 - auth - INFO - Created new user: supuser
2025-07-11 14:55:11,880 - auth - INFO - Created new user: supuser
2025-07-11 14:55:16,466 - auth - INFO - Created new user: supuser
2025-07-11 14:55:21,052 - auth - INFO - Created new user: supuser
2025-07-11 14:56:23,369 - auth - INFO - Created new user: kbadmin
2025-07-11 14:56:24,071 - auth - INFO - Created new user: kbaudit
2025-07-11 14:56:52,180 - auth - INFO - Created new user: concuradmin1752226011954
2025-07-11 14:56:52,615 - auth - INFO - Created new user: concuser17522260124080
2025-07-11 14:56:52,618 - auth - INFO - Created new user: concuser17522260124081
2025-07-11 14:56:52,624 - auth - INFO - Created new user: concuser17522260124083
2025-07-11 14:56:52,627 - auth - INFO - Created new user: concuser17522260124084
2025-07-11 14:56:52,637 - auth - INFO - Created new user: concuser17522260124082
2025-07-11 14:56:52,843 - auth - INFO - Created new user: duplicateuser1752226012637
2025-07-11 14:56:52,854 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752226012637', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$NPfGz2jaQ/OSaIi4P/KwlO64CW6hmY2DdcHPaTgDP6NOmYb97TfNO', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-11 14:56:52,869 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752226012637', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$6qkkQtQ.lq71nr2qxE4KvuodPytY0xNYCNKqjcxBpD/PNyKzp0yMK', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-11 14:56:53,062 - auth - INFO - Created new user: annotator_conc2
2025-07-11 14:56:53,873 - auth - INFO - Created new user: fileadmin2
2025-07-11 14:57:08,874 - auth - INFO - Created new user: wpuser
2025-07-11 14:57:09,433 - auth - INFO - Created new user: ehadmin
2025-07-11 14:57:09,992 - auth - INFO - Created new user: ehadmin
2025-07-11 14:57:10,522 - auth - INFO - Created new user: ehannotator
2025-07-11 14:57:11,039 - auth - INFO - Created new user: ehannotator
2025-07-11 14:57:11,588 - auth - INFO - Created new user: ehannotator
2025-07-11 14:57:12,002 - auth - INFO - Created new user: largeuser
2025-07-11 14:57:12,187 - auth - INFO - Created new user: concurrentuser
2025-07-11 14:57:44,774 - auth - INFO - Created new user: synthadmin
2025-07-11 14:57:45,808 - auth - INFO - Created new user: radio_user
2025-07-11 14:58:06,250 - auth - INFO - Created new user: perfuser
2025-07-11 14:58:22,570 - auth - INFO - Created new user: auser
2025-07-11 14:58:36,592 - auth - INFO - Created new user: secureuser
2025-07-11 14:58:37,420 - auth - INFO - Created new user: encuser
2025-07-11 14:58:37,758 - auth - INFO - Created new user: loginuser
2025-07-11 14:58:38,303 - auth - INFO - Created new user: meuser
2025-07-11 14:58:38,836 - auth - INFO - Created new user: refreshuser
2025-07-11 14:58:39,376 - auth - INFO - Created new user: changepassuser
2025-07-11 14:58:39,944 - auth - INFO - Updated password for user: changepassuser
2025-07-11 14:58:40,655 - auth - INFO - Created new user: changepassuser2
2025-07-11 14:58:41,376 - auth - INFO - Created new user: logoutuser
2025-07-11 14:58:41,892 - auth - INFO - Created new user: verifyuser
2025-07-11 14:58:42,488 - auth - INFO - Created new user: annuser
2025-07-11 14:58:42,891 - auth - INFO - Created new user: adminuser
2025-07-11 14:58:43,291 - auth - INFO - Created new user: dupuser
2025-07-11 14:58:43,638 - auth - INFO - Created new user: cookieattruser
2025-07-11 14:58:44,156 - auth - INFO - Created new user: wrongtypeuser
2025-07-11 14:58:44,694 - auth - INFO - Created new user: refreshflowuser
2025-07-11 14:58:45,227 - auth - INFO - Created new user: schemachangepass
2025-07-11 14:58:45,777 - auth - INFO - Created new user: tamperuser
2025-07-11 14:58:46,294 - auth - INFO - Created new user: auduser
2025-07-11 14:58:46,693 - auth - INFO - Created new user: realannotator
2025-07-11 14:58:47,238 - auth - INFO - Created new user: annforaud
2025-07-11 14:58:47,631 - auth - INFO - Created new user: trueauditor
2025-07-11 14:59:36,407 - auth - INFO - Created new user: concurrentuser
2025-07-11 15:00:20,241 - auth - INFO - Created new user: tgadmin
2025-07-11 15:00:47,348 - auth - INFO - Created new user: supuser
2025-07-11 15:00:52,013 - auth - INFO - Created new user: supuser
2025-07-11 15:00:56,597 - auth - INFO - Created new user: supuser
2025-07-11 15:01:01,194 - auth - INFO - Created new user: supuser
2025-07-11 15:01:05,789 - auth - INFO - Created new user: supuser
2025-07-11 15:01:10,376 - auth - INFO - Created new user: supuser
2025-07-11 15:01:14,975 - auth - INFO - Created new user: supuser
2025-07-11 15:01:19,576 - auth - INFO - Created new user: supuser
2025-07-11 15:01:24,577 - auth - INFO - Created new user: supuser
2025-07-11 15:01:29,164 - auth - INFO - Created new user: anno_sup
2025-07-11 15:01:33,776 - auth - INFO - Created new user: supuser
2025-07-11 15:01:38,380 - auth - INFO - Created new user: supuser
2025-07-11 15:01:42,983 - auth - INFO - Created new user: supuser
2025-07-11 15:01:47,560 - auth - INFO - Created new user: supuser
2025-07-11 15:08:09,737 - auth - INFO - Created new user: supuser
2025-07-11 15:08:14,556 - auth - INFO - Created new user: supuser
2025-07-11 15:08:19,160 - auth - INFO - Created new user: supuser
2025-07-11 15:08:24,342 - auth - INFO - Created new user: supuser
2025-07-11 15:08:28,934 - auth - INFO - Created new user: supuser
2025-07-11 15:08:33,556 - auth - INFO - Created new user: supuser
2025-07-11 15:08:38,144 - auth - INFO - Created new user: supuser
2025-07-11 15:08:42,749 - auth - INFO - Created new user: supuser
2025-07-11 15:08:47,377 - auth - INFO - Created new user: supuser
2025-07-11 15:08:52,164 - auth - INFO - Created new user: anno_sup
2025-07-11 15:08:56,831 - auth - INFO - Created new user: supuser
2025-07-11 15:09:01,431 - auth - INFO - Created new user: supuser
2025-07-11 15:09:06,057 - auth - INFO - Created new user: supuser
2025-07-11 15:09:10,664 - auth - INFO - Created new user: supuser
2025-07-11 15:10:35,669 - auth - INFO - Created new user: concurrentuser
2025-07-11 15:11:21,993 - auth - INFO - Created new user: synthadmin
2025-07-11 15:11:23,123 - auth - INFO - Created new user: radio_user
2025-07-11 15:13:23,620 - auth - INFO - Created new user: admin
2025-07-11 15:13:23,986 - auth - INFO - Created new user: supuser
2025-07-11 15:13:28,675 - auth - INFO - Created new user: admin
2025-07-11 15:13:29,044 - auth - INFO - Created new user: supuser
2025-07-11 15:13:33,624 - auth - INFO - Created new user: admin
2025-07-11 15:13:33,989 - auth - INFO - Created new user: supuser
2025-07-11 15:13:39,114 - auth - INFO - Created new user: admin
2025-07-11 15:13:39,504 - auth - INFO - Created new user: supuser
2025-07-11 15:13:44,123 - auth - INFO - Created new user: admin
2025-07-11 15:13:44,499 - auth - INFO - Created new user: supuser
2025-07-11 15:13:49,053 - auth - INFO - Created new user: admin
2025-07-11 15:13:49,418 - auth - INFO - Created new user: supuser
2025-07-11 15:13:54,020 - auth - INFO - Created new user: admin
2025-07-11 15:13:54,402 - auth - INFO - Created new user: supuser
2025-07-11 15:13:58,989 - auth - INFO - Created new user: admin
2025-07-11 15:13:59,352 - auth - INFO - Created new user: supuser
2025-07-11 15:14:03,963 - auth - INFO - Created new user: admin
2025-07-11 15:14:04,339 - auth - INFO - Created new user: supuser
2025-07-11 15:14:09,140 - auth - INFO - Created new user: anno_sup
2025-07-11 15:14:13,791 - auth - INFO - Created new user: admin
2025-07-11 15:14:14,190 - auth - INFO - Created new user: supuser
2025-07-11 15:14:18,809 - auth - INFO - Created new user: admin
2025-07-11 15:14:19,210 - auth - INFO - Created new user: supuser
2025-07-11 15:14:23,840 - auth - INFO - Created new user: admin
2025-07-11 15:14:24,240 - auth - INFO - Created new user: supuser
2025-07-11 15:14:28,840 - auth - INFO - Created new user: admin
2025-07-11 15:14:29,240 - auth - INFO - Created new user: supuser
2025-07-11 15:15:23,781 - auth - INFO - Created new user: synthadmin
2025-07-11 15:15:24,920 - auth - INFO - Created new user: radio_user
2025-07-11 15:17:27,848 - auth - INFO - Created new user: admin
2025-07-11 15:17:28,219 - auth - INFO - Created new user: supuser
2025-07-11 15:17:32,927 - auth - INFO - Created new user: admin
2025-07-11 15:17:33,295 - auth - INFO - Created new user: supuser
2025-07-11 15:17:37,876 - auth - INFO - Created new user: admin
2025-07-11 15:17:38,265 - auth - INFO - Created new user: supuser
2025-07-11 15:17:42,881 - auth - INFO - Created new user: admin
2025-07-11 15:17:43,238 - auth - INFO - Created new user: supuser
2025-07-11 15:17:47,822 - auth - INFO - Created new user: admin
2025-07-11 15:17:48,213 - auth - INFO - Created new user: supuser
2025-07-11 15:17:52,821 - auth - INFO - Created new user: admin
2025-07-11 15:17:53,192 - auth - INFO - Created new user: supuser
2025-07-11 15:17:57,817 - auth - INFO - Created new user: admin
2025-07-11 15:17:58,175 - auth - INFO - Created new user: supuser
2025-07-11 15:18:02,774 - auth - INFO - Created new user: admin
2025-07-11 15:18:03,191 - auth - INFO - Created new user: supuser
2025-07-11 15:18:07,814 - auth - INFO - Created new user: admin
2025-07-11 15:18:08,178 - auth - INFO - Created new user: supuser
2025-07-11 15:18:12,785 - auth - INFO - Created new user: anno_sup
2025-07-11 15:18:17,428 - auth - INFO - Created new user: admin
2025-07-11 15:18:17,810 - auth - INFO - Created new user: supuser
2025-07-11 15:18:22,391 - auth - INFO - Created new user: admin
2025-07-11 15:18:22,779 - auth - INFO - Created new user: supuser
2025-07-11 15:18:27,357 - auth - INFO - Created new user: admin
2025-07-11 15:18:27,757 - auth - INFO - Created new user: supuser
2025-07-11 15:18:32,395 - auth - INFO - Created new user: admin
2025-07-11 15:18:32,783 - auth - INFO - Created new user: supuser
2025-07-11 15:22:07,968 - auth - INFO - Created new user: synthadmin
2025-07-11 15:22:08,538 - auth - INFO - Created new user: radio_user
2025-07-11 16:14:55,038 - auth - INFO - Created new user: admin
2025-07-11 16:14:55,412 - auth - INFO - Created new user: supuser
2025-07-11 16:15:00,164 - auth - INFO - Created new user: admin
2025-07-11 16:15:00,537 - auth - INFO - Created new user: supuser
2025-07-11 16:15:05,134 - auth - INFO - Created new user: admin
2025-07-11 16:15:05,499 - auth - INFO - Created new user: supuser
2025-07-11 16:15:10,091 - auth - INFO - Created new user: admin
2025-07-11 16:15:10,470 - auth - INFO - Created new user: supuser
2025-07-11 16:15:15,048 - auth - INFO - Created new user: admin
2025-07-11 16:15:15,413 - auth - INFO - Created new user: supuser
2025-07-11 16:15:20,021 - auth - INFO - Created new user: admin
2025-07-11 16:15:20,389 - auth - INFO - Created new user: supuser
2025-07-11 16:15:24,980 - auth - INFO - Created new user: admin
2025-07-11 16:15:25,354 - auth - INFO - Created new user: supuser
2025-07-11 16:15:29,929 - auth - INFO - Created new user: admin
2025-07-11 16:15:30,309 - auth - INFO - Created new user: supuser
2025-07-11 16:15:34,858 - auth - INFO - Created new user: admin
2025-07-11 16:15:35,242 - auth - INFO - Created new user: supuser
2025-07-11 16:15:39,882 - auth - INFO - Created new user: anno_sup
2025-07-11 16:15:44,535 - auth - INFO - Created new user: admin
2025-07-11 16:15:44,908 - auth - INFO - Created new user: supuser
2025-07-11 16:15:49,486 - auth - INFO - Created new user: admin
2025-07-11 16:15:49,882 - auth - INFO - Created new user: supuser
2025-07-11 16:15:54,482 - auth - INFO - Created new user: admin
2025-07-11 16:15:54,858 - auth - INFO - Created new user: supuser
2025-07-11 16:15:59,477 - auth - INFO - Created new user: admin
2025-07-11 16:15:59,851 - auth - INFO - Created new user: supuser
2025-07-12 10:20:10,863 - auth - INFO - Created new user: adminuser
2025-07-12 10:20:15,889 - auth - INFO - Created new user: adminuser
2025-07-12 10:20:20,492 - auth - INFO - Created new user: adminuser
2025-07-12 10:20:20,876 - auth - INFO - Created new user: newuser
2025-07-12 10:20:25,325 - auth - INFO - Created new user: adminuser
2025-07-12 10:20:25,709 - auth - INFO - Created new user: a
2025-07-12 10:20:25,915 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 10:20:26,095 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 10:20:30,530 - auth - INFO - Created new user: adminuser
2025-07-12 10:20:35,158 - auth - INFO - Created new user: adminuser
2025-07-12 10:20:39,809 - auth - INFO - Created new user: adminuser
2025-07-12 10:20:48,509 - auth - INFO - Created new user: adminuser
2025-07-12 10:20:53,120 - auth - INFO - Created new user: adminuser
2025-07-12 10:20:57,733 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:02,359 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:06,963 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:11,640 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:16,230 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:20,860 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:25,507 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:30,142 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:34,731 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:39,468 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:44,328 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:49,208 - auth - INFO - Created new user: adminuser
2025-07-12 10:21:54,092 - auth - INFO - Created new user: adminuser
2025-07-12 10:22:03,158 - auth - INFO - Created new user: anno_user
2025-07-12 10:22:08,055 - auth - INFO - Created new user: anno_user
2025-07-12 10:22:12,948 - auth - INFO - Created new user: anno_user
2025-07-12 10:22:18,253 - auth - INFO - Created new user: adminuser
2025-07-12 10:22:23,052 - auth - INFO - Created new user: adminuser
2025-07-12 10:22:23,347 - auth - INFO - Updated user: adminuser
2025-07-12 10:22:23,372 - auth - INFO - Updated user: adminuser
2025-07-12 10:22:27,813 - auth - INFO - Created new user: adminuser
2025-07-12 10:22:32,431 - auth - INFO - Created new user: adminuser
2025-07-12 10:22:37,078 - auth - INFO - Created new user: adminuser
2025-07-12 10:22:41,710 - auth - INFO - Created new user: annouser
2025-07-12 10:22:46,372 - auth - INFO - Created new user: annouser
2025-07-12 10:22:51,365 - auth - INFO - Created new user: annouser
2025-07-12 10:22:56,225 - auth - INFO - Created new user: annouser
2025-07-12 10:23:00,797 - auth - INFO - Created new user: annouser
2025-07-12 10:23:05,432 - auth - INFO - Created new user: annouser
2025-07-12 10:23:10,078 - auth - INFO - Created new user: annouser
2025-07-12 10:23:14,895 - auth - INFO - Created new user: annouser
2025-07-12 10:23:19,499 - auth - INFO - Created new user: annouser
2025-07-12 10:23:24,332 - auth - INFO - Created new user: annouser
2025-07-12 10:23:29,097 - auth - INFO - Created new user: audituser
2025-07-12 10:23:33,855 - auth - INFO - Created new user: audituser
2025-07-12 10:23:38,607 - auth - INFO - Created new user: audituser
2025-07-12 10:23:43,374 - auth - INFO - Created new user: audituser
2025-07-12 10:23:48,224 - auth - INFO - Created new user: audituser
2025-07-12 10:23:53,211 - auth - INFO - Created new user: audituser
2025-07-12 10:23:58,095 - auth - INFO - Created new user: audituser
2025-07-12 10:24:02,911 - auth - INFO - Created new user: audituser
2025-07-12 10:24:07,715 - auth - INFO - Created new user: wrongaudit
2025-07-12 10:24:12,709 - auth - INFO - Created new user: testuser
2025-07-12 10:24:25,967 - auth - INFO - Created new user: changepw
2025-07-12 10:24:26,547 - auth - INFO - Updated password for user: changepw
2025-07-12 10:24:31,179 - auth - INFO - Created new user: a
2025-07-12 10:24:35,750 - auth - INFO - Created new user: minpw
2025-07-12 10:24:40,281 - auth - INFO - Created new user: logoutuser
2025-07-12 10:24:53,577 - auth - INFO - Created new user: cpuser
2025-07-12 10:24:58,372 - auth - INFO - Created new user: du
2025-07-12 10:24:58,774 - auth - INFO - Created new user: deluser
2025-07-12 10:25:03,378 - auth - INFO - Created new user: clientuser
2025-07-12 10:25:07,976 - auth - INFO - Created new user: clientuser
2025-07-12 10:25:12,507 - auth - INFO - Created new user: notclient
2025-07-12 10:25:12,901 - auth - INFO - Created new user: concuradmin1752296112711
2025-07-12 10:25:13,376 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot commit transaction - SQL statements in progress
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 10:25:13,376 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17522961131392', 'Concurrent User 17522961131392', '<EMAIL>', 'annotator', '$2b$12$XyPumlXxWzdvOwFf7hSwueA8MkKnD6ctMHAwL.bbZjBuSdlkPxONy', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 10:25:13,376 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17522961131390', 'Concurrent User 17522961131390', '<EMAIL>', 'annotator', '$2b$12$kFvFUqtX0WweACuClohjM./hCdHeBS9rnxgx8xoAkXDKdeYYO1uyS', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 10:25:13,376 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot start a transaction within a transaction
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17522961131393', 'Concurrent User 17522961131393', '<EMAIL>', 'annotator', '$2b$12$TeGFJ7ohhHBhbDOVuCOfN.Q7NWD2CIzaaW8Q86OzJ3M/76t4krr5a', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 10:25:13,376 - auth - INFO - Created new user: concuser17522961131391
2025-07-12 10:25:13,727 - auth - INFO - Created new user: duplicateuser1752296113392
2025-07-12 10:25:13,734 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752296113392', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$TsM8UYYf.LKyc7Fmyjy2YegMA3HDgAb5crGBua1QYeUqJnFNLd7Yq', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 10:25:13,734 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752296113392', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$aU14FNw2/JcYc2Fr.hBub.M4vwgyecR4egRsB/a0Cw7ZjxPo/v5Uu', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 10:25:13,982 - auth - INFO - Created new user: annotator_conc2
2025-07-12 10:25:15,011 - auth - INFO - Created new user: fileadmin2
2025-07-12 10:25:15,360 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 10:25:16,583 - auth - INFO - Created new user: wpuser
2025-07-12 10:25:17,343 - auth - INFO - Created new user: ehadmin
2025-07-12 10:25:18,154 - auth - INFO - Created new user: ehadmin
2025-07-12 10:25:18,834 - auth - INFO - Created new user: ehannotator
2025-07-12 10:25:19,548 - auth - INFO - Created new user: ehannotator
2025-07-12 10:25:20,358 - auth - INFO - Created new user: ehannotator
2025-07-12 10:25:20,995 - auth - INFO - Created new user: largeuser
2025-07-12 10:25:21,264 - auth - INFO - Created new user: concurrentuser
2025-07-12 10:25:23,730 - auth - INFO - Created new user: kbadmin
2025-07-12 10:25:24,686 - auth - INFO - Created new user: kbaudit
2025-07-12 10:25:26,397 - auth - INFO - Created new user: perfuser
2025-07-12 10:25:28,918 - auth - INFO - Created new user: auser
2025-07-12 10:25:29,878 - auth - INFO - Created new user: secureuser
2025-07-12 10:25:30,944 - auth - INFO - Created new user: encuser
2025-07-12 10:25:31,420 - auth - INFO - Created new user: loginuser
2025-07-12 10:25:32,165 - auth - INFO - Created new user: meuser
2025-07-12 10:25:32,912 - auth - INFO - Created new user: refreshuser
2025-07-12 10:25:33,758 - auth - INFO - Created new user: changepassuser
2025-07-12 10:25:34,503 - auth - INFO - Updated password for user: changepassuser
2025-07-12 10:25:35,615 - auth - INFO - Created new user: changepassuser2
2025-07-12 10:25:36,566 - auth - INFO - Created new user: logoutuser
2025-07-12 10:25:37,281 - auth - INFO - Created new user: verifyuser
2025-07-12 10:25:38,122 - auth - INFO - Created new user: annuser
2025-07-12 10:25:38,696 - auth - INFO - Created new user: adminuser
2025-07-12 10:25:39,220 - auth - INFO - Created new user: dupuser
2025-07-12 10:25:39,711 - auth - INFO - Created new user: cookieattruser
2025-07-12 10:25:40,396 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 10:25:41,094 - auth - INFO - Created new user: refreshflowuser
2025-07-12 10:25:41,794 - auth - INFO - Created new user: schemachangepass
2025-07-12 10:25:42,541 - auth - INFO - Created new user: tamperuser
2025-07-12 10:25:43,257 - auth - INFO - Created new user: auduser
2025-07-12 10:25:43,750 - auth - INFO - Created new user: realannotator
2025-07-12 10:25:44,472 - auth - INFO - Created new user: annforaud
2025-07-12 10:25:44,960 - auth - INFO - Created new user: trueauditor
2025-07-12 10:25:49,842 - auth - INFO - Created new user: admin
2025-07-12 10:25:50,335 - auth - INFO - Created new user: supuser
2025-07-12 10:25:55,170 - auth - INFO - Created new user: admin
2025-07-12 10:25:55,662 - auth - INFO - Created new user: supuser
2025-07-12 10:26:00,571 - auth - INFO - Created new user: admin
2025-07-12 10:26:01,080 - auth - INFO - Created new user: supuser
2025-07-12 10:26:05,928 - auth - INFO - Created new user: admin
2025-07-12 10:26:06,450 - auth - INFO - Created new user: supuser
2025-07-12 10:26:11,406 - auth - INFO - Created new user: admin
2025-07-12 10:26:11,884 - auth - INFO - Created new user: supuser
2025-07-12 10:26:16,812 - auth - INFO - Created new user: admin
2025-07-12 10:26:17,322 - auth - INFO - Created new user: supuser
2025-07-12 10:26:22,162 - auth - INFO - Created new user: admin
2025-07-12 10:26:22,654 - auth - INFO - Created new user: supuser
2025-07-12 10:26:27,472 - auth - INFO - Created new user: admin
2025-07-12 10:26:27,980 - auth - INFO - Created new user: supuser
2025-07-12 10:26:32,721 - auth - INFO - Created new user: admin
2025-07-12 10:26:33,114 - auth - INFO - Created new user: supuser
2025-07-12 10:26:37,745 - auth - INFO - Created new user: anno_sup
2025-07-12 10:26:42,684 - auth - INFO - Created new user: admin
2025-07-12 10:26:43,120 - auth - INFO - Created new user: supuser
2025-07-12 10:26:47,930 - auth - INFO - Created new user: admin
2025-07-12 10:26:48,397 - auth - INFO - Created new user: supuser
2025-07-12 10:26:53,215 - auth - INFO - Created new user: admin
2025-07-12 10:26:53,651 - auth - INFO - Created new user: supuser
2025-07-12 10:26:58,483 - auth - INFO - Created new user: admin
2025-07-12 10:26:58,902 - auth - INFO - Created new user: supuser
2025-07-12 10:26:59,408 - auth - INFO - Created new user: synthadmin
2025-07-12 10:27:00,077 - auth - INFO - Created new user: radio_user
2025-07-12 10:27:00,602 - auth - INFO - Created new user: tgadmin
2025-07-12 10:27:06,444 - auth - INFO - Created new user: authzanno
2025-07-12 10:27:07,130 - auth - INFO - Created new user: authzauditor
2025-07-12 10:27:07,582 - auth - INFO - Created new user: authzclient
2025-07-12 10:27:07,995 - auth - INFO - Created new user: authzadmin
2025-07-12 10:27:09,437 - auth - INFO - Created new user: smoketest
2025-07-12 10:59:26,597 - auth - INFO - Created new user: adminuser
2025-07-12 10:59:31,273 - auth - INFO - Created new user: adminuser
2025-07-12 10:59:35,841 - auth - INFO - Created new user: adminuser
2025-07-12 10:59:36,208 - auth - INFO - Created new user: newuser
2025-07-12 10:59:40,598 - auth - INFO - Created new user: adminuser
2025-07-12 10:59:40,979 - auth - INFO - Created new user: a
2025-07-12 10:59:41,177 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 10:59:41,365 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 10:59:45,746 - auth - INFO - Created new user: adminuser
2025-07-12 10:59:50,307 - auth - INFO - Created new user: adminuser
2025-07-12 10:59:54,879 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:03,604 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:08,283 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:12,996 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:17,612 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:22,183 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:26,758 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:31,314 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:35,909 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:40,473 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:45,032 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:49,618 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:54,211 - auth - INFO - Created new user: adminuser
2025-07-12 11:00:58,797 - auth - INFO - Created new user: adminuser
2025-07-12 11:01:03,414 - auth - INFO - Created new user: adminuser
2025-07-12 11:01:08,026 - auth - INFO - Created new user: adminuser
2025-07-12 11:01:16,822 - auth - INFO - Created new user: anno_user
2025-07-12 11:01:21,538 - auth - INFO - Created new user: anno_user
2025-07-12 11:01:26,243 - auth - INFO - Created new user: anno_user
2025-07-12 11:01:31,238 - auth - INFO - Created new user: adminuser
2025-07-12 11:01:35,946 - auth - INFO - Created new user: adminuser
2025-07-12 11:01:36,166 - auth - INFO - Updated user: adminuser
2025-07-12 11:01:36,173 - auth - INFO - Updated user: adminuser
2025-07-12 11:01:40,630 - auth - INFO - Created new user: adminuser
2025-07-12 11:01:45,370 - auth - INFO - Created new user: adminuser
2025-07-12 11:01:50,075 - auth - INFO - Created new user: adminuser
2025-07-12 11:01:54,703 - auth - INFO - Created new user: annouser
2025-07-12 11:01:59,330 - auth - INFO - Created new user: annouser
2025-07-12 11:02:03,942 - auth - INFO - Created new user: annouser
2025-07-12 11:02:08,548 - auth - INFO - Created new user: annouser
2025-07-12 11:02:13,168 - auth - INFO - Created new user: annouser
2025-07-12 11:02:17,768 - auth - INFO - Created new user: annouser
2025-07-12 11:02:22,364 - auth - INFO - Created new user: annouser
2025-07-12 11:02:27,005 - auth - INFO - Created new user: annouser
2025-07-12 11:02:31,644 - auth - INFO - Created new user: annouser
2025-07-12 11:02:36,264 - auth - INFO - Created new user: annouser
2025-07-12 11:02:40,906 - auth - INFO - Created new user: audituser
2025-07-12 11:02:45,515 - auth - INFO - Created new user: audituser
2025-07-12 11:02:50,147 - auth - INFO - Created new user: audituser
2025-07-12 11:02:54,748 - auth - INFO - Created new user: audituser
2025-07-12 11:02:59,364 - auth - INFO - Created new user: audituser
2025-07-12 11:03:04,033 - auth - INFO - Created new user: audituser
2025-07-12 11:03:08,616 - auth - INFO - Created new user: audituser
2025-07-12 11:03:13,216 - auth - INFO - Created new user: audituser
2025-07-12 11:03:17,816 - auth - INFO - Created new user: wrongaudit
2025-07-12 11:03:22,466 - auth - INFO - Created new user: testuser
2025-07-12 11:03:35,466 - auth - INFO - Created new user: changepw
2025-07-12 11:03:36,033 - auth - INFO - Updated password for user: changepw
2025-07-12 11:03:40,626 - auth - INFO - Created new user: a
2025-07-12 11:03:45,033 - auth - INFO - Created new user: minpw
2025-07-12 11:03:49,433 - auth - INFO - Created new user: logoutuser
2025-07-12 11:04:02,423 - auth - INFO - Created new user: cpuser
2025-07-12 11:04:07,208 - auth - INFO - Created new user: du
2025-07-12 11:04:07,583 - auth - INFO - Created new user: deluser
2025-07-12 11:04:12,216 - auth - INFO - Created new user: clientuser
2025-07-12 11:04:16,837 - auth - INFO - Created new user: clientuser
2025-07-12 11:04:21,348 - auth - INFO - Created new user: notclient
2025-07-12 11:04:21,748 - auth - INFO - Created new user: concuradmin1752298461549
2025-07-12 11:04:22,204 - auth - INFO - Created new user: concuser17522984619813
2025-07-12 11:04:22,204 - auth - ERROR - Database error in create_user: Could not refresh instance '<User at 0x186e1e530a0>'
2025-07-12 11:04:22,204 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot start a transaction within a transaction
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17522984619812', 'Concurrent User 17522984619812', '<EMAIL>', 'annotator', '$2b$12$GWBZ5U9qURE5m.gsKznT2e5yOEepSwqUdrfceCdJGnAyGHLHIN8CG', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 11:04:22,215 - auth - INFO - Created new user: concuser17522984619810
2025-07-12 11:04:22,215 - auth - INFO - Created new user: concuser17522984619811
2025-07-12 11:04:22,432 - auth - INFO - Created new user: duplicateuser1752298462215
2025-07-12 11:04:22,432 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752298462215', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$V7wKavPZ0WC0JjcP8CIhlejWBiwbDcZ8TymTQf/7W8drDGbUkTDFW', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 11:04:22,432 - auth - ERROR - Database error in create_user: This result object does not return rows. It has been closed automatically.
2025-07-12 11:04:22,632 - auth - INFO - Created new user: annotator_conc2
2025-07-12 11:04:23,514 - auth - INFO - Created new user: fileadmin2
2025-07-12 11:04:24,381 - auth - INFO - Created new user: wpuser
2025-07-12 11:04:24,934 - auth - INFO - Created new user: ehadmin
2025-07-12 11:04:25,473 - auth - INFO - Created new user: ehadmin
2025-07-12 11:04:26,009 - auth - INFO - Created new user: ehannotator
2025-07-12 11:04:26,531 - auth - INFO - Created new user: ehannotator
2025-07-12 11:04:27,115 - auth - INFO - Created new user: ehannotator
2025-07-12 11:04:27,553 - auth - INFO - Created new user: largeuser
2025-07-12 11:04:27,748 - auth - INFO - Created new user: concurrentuser
2025-07-12 11:04:29,315 - auth - INFO - Created new user: kbadmin
2025-07-12 11:04:29,855 - auth - INFO - Created new user: kbaudit
2025-07-12 11:04:30,981 - auth - INFO - Created new user: perfuser
2025-07-12 11:04:32,848 - auth - INFO - Created new user: auser
2025-07-12 11:04:33,465 - auth - INFO - Created new user: secureuser
2025-07-12 11:04:34,264 - auth - INFO - Created new user: encuser
2025-07-12 11:04:34,615 - auth - INFO - Created new user: loginuser
2025-07-12 11:04:35,163 - auth - INFO - Created new user: meuser
2025-07-12 11:04:35,731 - auth - INFO - Created new user: refreshuser
2025-07-12 11:04:36,280 - auth - INFO - Created new user: changepassuser
2025-07-12 11:04:36,847 - auth - INFO - Updated password for user: changepassuser
2025-07-12 11:04:37,565 - auth - INFO - Created new user: changepassuser2
2025-07-12 11:04:38,281 - auth - INFO - Created new user: logoutuser
2025-07-12 11:04:38,898 - auth - INFO - Created new user: verifyuser
2025-07-12 11:04:39,581 - auth - INFO - Created new user: annuser
2025-07-12 11:04:40,002 - auth - INFO - Created new user: adminuser
2025-07-12 11:04:40,431 - auth - INFO - Created new user: dupuser
2025-07-12 11:04:40,782 - auth - INFO - Created new user: cookieattruser
2025-07-12 11:04:41,331 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 11:04:41,880 - auth - INFO - Created new user: refreshflowuser
2025-07-12 11:04:42,431 - auth - INFO - Created new user: schemachangepass
2025-07-12 11:04:43,015 - auth - INFO - Created new user: tamperuser
2025-07-12 11:04:43,565 - auth - INFO - Created new user: auduser
2025-07-12 11:04:43,965 - auth - INFO - Created new user: realannotator
2025-07-12 11:04:44,514 - auth - INFO - Created new user: annforaud
2025-07-12 11:04:44,914 - auth - INFO - Created new user: trueauditor
2025-07-12 11:04:49,583 - auth - INFO - Created new user: admin
2025-07-12 11:04:49,971 - auth - INFO - Created new user: supuser
2025-07-12 11:04:54,565 - auth - INFO - Created new user: admin
2025-07-12 11:04:54,948 - auth - INFO - Created new user: supuser
2025-07-12 11:04:59,601 - auth - INFO - Created new user: admin
2025-07-12 11:04:59,965 - auth - INFO - Created new user: supuser
2025-07-12 11:05:04,582 - auth - INFO - Created new user: admin
2025-07-12 11:05:04,965 - auth - INFO - Created new user: supuser
2025-07-12 11:05:09,615 - auth - INFO - Created new user: admin
2025-07-12 11:05:10,001 - auth - INFO - Created new user: supuser
2025-07-12 11:05:14,581 - auth - INFO - Created new user: admin
2025-07-12 11:05:14,965 - auth - INFO - Created new user: supuser
2025-07-12 11:05:19,598 - auth - INFO - Created new user: admin
2025-07-12 11:05:19,982 - auth - INFO - Created new user: supuser
2025-07-12 11:05:24,565 - auth - INFO - Created new user: admin
2025-07-12 11:05:24,948 - auth - INFO - Created new user: supuser
2025-07-12 11:05:29,548 - auth - INFO - Created new user: admin
2025-07-12 11:05:29,931 - auth - INFO - Created new user: supuser
2025-07-12 11:05:34,548 - auth - INFO - Created new user: anno_sup
2025-07-12 11:05:39,231 - auth - INFO - Created new user: admin
2025-07-12 11:05:39,615 - auth - INFO - Created new user: supuser
2025-07-12 11:05:44,215 - auth - INFO - Created new user: admin
2025-07-12 11:05:44,615 - auth - INFO - Created new user: supuser
2025-07-12 11:05:49,239 - auth - INFO - Created new user: admin
2025-07-12 11:05:49,633 - auth - INFO - Created new user: supuser
2025-07-12 11:05:54,200 - auth - INFO - Created new user: admin
2025-07-12 11:05:54,584 - auth - INFO - Created new user: supuser
2025-07-12 11:05:55,033 - auth - INFO - Created new user: synthadmin
2025-07-12 11:05:55,535 - auth - INFO - Created new user: radio_user
2025-07-12 11:05:55,979 - auth - INFO - Created new user: tgadmin
2025-07-12 11:05:59,265 - auth - INFO - Created new user: concurrentuser
2025-07-12 11:14:59,101 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:03,783 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:08,353 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:08,725 - auth - INFO - Created new user: newuser
2025-07-12 11:15:13,096 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:13,467 - auth - INFO - Created new user: a
2025-07-12 11:15:13,664 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 11:15:13,851 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 11:15:18,213 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:22,769 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:27,358 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:35,957 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:40,534 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:45,131 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:49,749 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:54,365 - auth - INFO - Created new user: adminuser
2025-07-12 11:15:58,998 - auth - INFO - Created new user: adminuser
2025-07-12 11:16:03,566 - auth - INFO - Created new user: adminuser
2025-07-12 11:16:08,165 - auth - INFO - Created new user: adminuser
2025-07-12 11:16:12,793 - auth - INFO - Created new user: adminuser
2025-07-12 11:16:17,410 - auth - INFO - Created new user: adminuser
2025-07-12 11:16:21,991 - auth - INFO - Created new user: adminuser
2025-07-12 11:16:26,590 - auth - INFO - Created new user: adminuser
2025-07-12 11:16:31,202 - auth - INFO - Created new user: adminuser
2025-07-12 11:16:35,816 - auth - INFO - Created new user: adminuser
2025-07-12 11:16:40,435 - auth - INFO - Created new user: adminuser
2025-07-12 11:16:49,142 - auth - INFO - Created new user: anno_user
2025-07-12 11:16:53,790 - auth - INFO - Created new user: anno_user
2025-07-12 11:16:58,583 - auth - INFO - Created new user: anno_user
2025-07-12 11:17:03,941 - auth - INFO - Created new user: adminuser
2025-07-12 11:17:09,059 - auth - INFO - Created new user: adminuser
2025-07-12 11:17:09,272 - auth - INFO - Updated user: adminuser
2025-07-12 11:17:09,285 - auth - INFO - Updated user: adminuser
2025-07-12 11:17:13,995 - auth - INFO - Created new user: adminuser
2025-07-12 11:17:18,978 - auth - INFO - Created new user: adminuser
2025-07-12 11:17:23,940 - auth - INFO - Created new user: adminuser
2025-07-12 11:17:28,853 - auth - INFO - Created new user: annouser
2025-07-12 11:17:33,461 - auth - INFO - Created new user: annouser
2025-07-12 11:17:38,252 - auth - INFO - Created new user: annouser
2025-07-12 11:17:42,862 - auth - INFO - Created new user: annouser
2025-07-12 11:17:47,523 - auth - INFO - Created new user: annouser
2025-07-12 11:17:52,151 - auth - INFO - Created new user: annouser
2025-07-12 11:17:56,824 - auth - INFO - Created new user: annouser
2025-07-12 11:18:01,476 - auth - INFO - Created new user: annouser
2025-07-12 11:18:06,097 - auth - INFO - Created new user: annouser
2025-07-12 11:18:10,755 - auth - INFO - Created new user: annouser
2025-07-12 11:18:15,369 - auth - INFO - Created new user: audituser
2025-07-12 11:18:19,947 - auth - INFO - Created new user: audituser
2025-07-12 11:18:24,571 - auth - INFO - Created new user: audituser
2025-07-12 11:18:29,193 - auth - INFO - Created new user: audituser
2025-07-12 11:18:33,765 - auth - INFO - Created new user: audituser
2025-07-12 11:18:38,419 - auth - INFO - Created new user: audituser
2025-07-12 11:18:43,025 - auth - INFO - Created new user: audituser
2025-07-12 11:18:47,624 - auth - INFO - Created new user: audituser
2025-07-12 11:18:52,195 - auth - INFO - Created new user: wrongaudit
2025-07-12 11:18:56,851 - auth - INFO - Created new user: testuser
2025-07-12 11:19:09,832 - auth - INFO - Created new user: changepw
2025-07-12 11:19:10,392 - auth - INFO - Updated password for user: changepw
2025-07-12 11:19:14,957 - auth - INFO - Created new user: a
2025-07-12 11:19:19,346 - auth - INFO - Created new user: minpw
2025-07-12 11:19:23,722 - auth - INFO - Created new user: logoutuser
2025-07-12 11:19:36,691 - auth - INFO - Created new user: cpuser
2025-07-12 11:19:41,473 - auth - INFO - Created new user: du
2025-07-12 11:19:41,857 - auth - INFO - Created new user: deluser
2025-07-12 11:19:46,445 - auth - INFO - Created new user: clientuser
2025-07-12 11:19:51,054 - auth - INFO - Created new user: clientuser
2025-07-12 11:19:55,545 - auth - INFO - Created new user: notclient
2025-07-12 11:19:55,963 - auth - INFO - Created new user: concuradmin1752299395753
2025-07-12 11:19:56,404 - auth - INFO - Created new user: concuser17522993961961
2025-07-12 11:19:56,406 - auth - INFO - Created new user: concuser17522993961962
2025-07-12 11:19:56,409 - auth - INFO - Created new user: concuser17522993961960
2025-07-12 11:19:56,413 - auth - INFO - Created new user: concuser17522993961964
2025-07-12 11:19:56,415 - auth - INFO - Created new user: concuser17522993961963
2025-07-12 11:19:56,623 - auth - INFO - Created new user: duplicateuser1752299396420
2025-07-12 11:19:56,625 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752299396420', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$R9npTpKh6YrTI6BVeaIYy.bVAGvmwVuwCVPSUbcrndYXAw7hNyDi.', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 11:19:56,626 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752299396420', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$qtsHyL8/ukpMXcE4q4qk7.4apK2X726kzTi/m/.j3cD/u11pWPsdy', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 11:19:56,822 - auth - INFO - Created new user: annotator_conc2
2025-07-12 11:19:57,637 - auth - INFO - Created new user: fileadmin2
2025-07-12 11:19:57,864 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 11:19:57,865 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 11:19:57,867 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 11:19:58,685 - auth - INFO - Created new user: wpuser
2025-07-12 11:19:59,218 - auth - INFO - Created new user: ehadmin
2025-07-12 11:19:59,744 - auth - INFO - Created new user: ehadmin
2025-07-12 11:20:00,308 - auth - INFO - Created new user: ehannotator
2025-07-12 11:20:00,814 - auth - INFO - Created new user: ehannotator
2025-07-12 11:20:01,369 - auth - INFO - Created new user: ehannotator
2025-07-12 11:20:01,788 - auth - INFO - Created new user: largeuser
2025-07-12 11:20:01,983 - auth - INFO - Created new user: concurrentuser
2025-07-12 11:20:03,464 - auth - INFO - Created new user: kbadmin
2025-07-12 11:20:03,984 - auth - INFO - Created new user: kbaudit
2025-07-12 11:20:05,007 - auth - INFO - Created new user: perfuser
2025-07-12 11:20:06,738 - auth - INFO - Created new user: auser
2025-07-12 11:20:07,301 - auth - INFO - Created new user: secureuser
2025-07-12 11:20:08,095 - auth - INFO - Created new user: encuser
2025-07-12 11:20:08,459 - auth - INFO - Created new user: loginuser
2025-07-12 11:20:09,015 - auth - INFO - Created new user: meuser
2025-07-12 11:20:09,534 - auth - INFO - Created new user: refreshuser
2025-07-12 11:20:10,077 - auth - INFO - Created new user: changepassuser
2025-07-12 11:20:10,646 - auth - INFO - Updated password for user: changepassuser
2025-07-12 11:20:11,339 - auth - INFO - Created new user: changepassuser2
2025-07-12 11:20:12,057 - auth - INFO - Created new user: logoutuser
2025-07-12 11:20:12,558 - auth - INFO - Created new user: verifyuser
2025-07-12 11:20:13,107 - auth - INFO - Created new user: annuser
2025-07-12 11:20:13,489 - auth - INFO - Created new user: adminuser
2025-07-12 11:20:13,877 - auth - INFO - Created new user: dupuser
2025-07-12 11:20:14,238 - auth - INFO - Created new user: cookieattruser
2025-07-12 11:20:14,747 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 11:20:15,261 - auth - INFO - Created new user: refreshflowuser
2025-07-12 11:20:15,781 - auth - INFO - Created new user: schemachangepass
2025-07-12 11:20:16,316 - auth - INFO - Created new user: tamperuser
2025-07-12 11:20:16,832 - auth - INFO - Created new user: auduser
2025-07-12 11:20:17,218 - auth - INFO - Created new user: realannotator
2025-07-12 11:20:17,731 - auth - INFO - Created new user: annforaud
2025-07-12 11:20:18,115 - auth - INFO - Created new user: trueauditor
2025-07-12 11:20:22,731 - auth - INFO - Created new user: admin
2025-07-12 11:20:23,105 - auth - INFO - Created new user: supuser
2025-07-12 11:20:27,669 - auth - INFO - Created new user: admin
2025-07-12 11:20:28,043 - auth - INFO - Created new user: supuser
2025-07-12 11:20:32,594 - auth - INFO - Created new user: admin
2025-07-12 11:20:32,964 - auth - INFO - Created new user: supuser
2025-07-12 11:20:37,552 - auth - INFO - Created new user: admin
2025-07-12 11:20:37,926 - auth - INFO - Created new user: supuser
2025-07-12 11:20:42,516 - auth - INFO - Created new user: admin
2025-07-12 11:20:42,895 - auth - INFO - Created new user: supuser
2025-07-12 11:20:47,501 - auth - INFO - Created new user: admin
2025-07-12 11:20:47,872 - auth - INFO - Created new user: supuser
2025-07-12 11:20:52,487 - auth - INFO - Created new user: admin
2025-07-12 11:20:52,859 - auth - INFO - Created new user: supuser
2025-07-12 11:20:57,447 - auth - INFO - Created new user: admin
2025-07-12 11:20:57,820 - auth - INFO - Created new user: supuser
2025-07-12 11:21:02,430 - auth - INFO - Created new user: admin
2025-07-12 11:21:02,814 - auth - INFO - Created new user: supuser
2025-07-12 11:21:07,389 - auth - INFO - Created new user: anno_sup
2025-07-12 11:21:12,043 - auth - INFO - Created new user: admin
2025-07-12 11:21:12,413 - auth - INFO - Created new user: supuser
2025-07-12 11:21:16,972 - auth - INFO - Created new user: admin
2025-07-12 11:21:17,341 - auth - INFO - Created new user: supuser
2025-07-12 11:21:21,991 - auth - INFO - Created new user: admin
2025-07-12 11:21:22,367 - auth - INFO - Created new user: supuser
2025-07-12 11:21:26,938 - auth - INFO - Created new user: admin
2025-07-12 11:21:27,313 - auth - INFO - Created new user: supuser
2025-07-12 11:21:27,738 - auth - INFO - Created new user: synthadmin
2025-07-12 11:21:28,224 - auth - INFO - Created new user: radio_user
2025-07-12 11:21:28,639 - auth - INFO - Created new user: tgadmin
2025-07-12 11:21:31,885 - auth - INFO - Created new user: concurrentuser
2025-07-12 11:21:32,842 - auth - INFO - Created new user: authzanno
2025-07-12 11:21:33,228 - auth - INFO - Created new user: authzauditor
2025-07-12 11:21:33,604 - auth - INFO - Created new user: authzclient
2025-07-12 11:21:33,992 - auth - INFO - Created new user: authzadmin
2025-07-12 11:21:34,944 - auth - INFO - Created new user: smoketest
2025-07-12 12:51:46,249 - auth - INFO - Created new user: adminuser
2025-07-12 12:51:51,001 - auth - INFO - Created new user: adminuser
2025-07-12 12:51:55,570 - auth - INFO - Created new user: adminuser
2025-07-12 12:51:55,948 - auth - INFO - Created new user: newuser
2025-07-12 12:52:00,361 - auth - INFO - Created new user: adminuser
2025-07-12 12:52:00,736 - auth - INFO - Created new user: a
2025-07-12 12:52:00,921 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 12:52:01,107 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 12:52:05,482 - auth - INFO - Created new user: adminuser
2025-07-12 12:52:10,059 - auth - INFO - Created new user: adminuser
2025-07-12 12:52:14,686 - auth - INFO - Created new user: adminuser
2025-07-12 12:52:23,387 - auth - INFO - Created new user: adminuser
2025-07-12 12:52:28,011 - auth - INFO - Created new user: adminuser
2025-07-12 12:52:32,615 - auth - INFO - Created new user: adminuser
2025-07-12 12:52:37,206 - auth - INFO - Created new user: adminuser
2025-07-12 12:52:41,771 - auth - INFO - Created new user: adminuser
2025-07-12 12:52:46,374 - auth - INFO - Created new user: adminuser
2025-07-12 12:52:50,965 - auth - INFO - Created new user: adminuser
2025-07-12 12:52:55,536 - auth - INFO - Created new user: adminuser
2025-07-12 12:53:00,109 - auth - INFO - Created new user: adminuser
2025-07-12 12:53:04,702 - auth - INFO - Created new user: adminuser
2025-07-12 12:53:09,277 - auth - INFO - Created new user: adminuser
2025-07-12 12:53:13,899 - auth - INFO - Created new user: adminuser
2025-07-12 12:53:18,452 - auth - INFO - Created new user: adminuser
2025-07-12 12:53:23,040 - auth - INFO - Created new user: adminuser
2025-07-12 12:53:27,616 - auth - INFO - Created new user: adminuser
2025-07-12 12:53:36,368 - auth - INFO - Created new user: anno_user
2025-07-12 12:53:40,936 - auth - INFO - Created new user: anno_user
2025-07-12 12:53:45,535 - auth - INFO - Created new user: anno_user
2025-07-12 12:53:50,257 - auth - INFO - Created new user: adminuser
2025-07-12 12:53:54,886 - auth - INFO - Created new user: adminuser
2025-07-12 12:53:55,086 - auth - INFO - Updated user: adminuser
2025-07-12 12:53:55,092 - auth - INFO - Updated user: adminuser
2025-07-12 12:53:59,473 - auth - INFO - Created new user: adminuser
2025-07-12 12:54:04,054 - auth - INFO - Created new user: adminuser
2025-07-12 12:54:08,635 - auth - INFO - Created new user: adminuser
2025-07-12 12:54:13,237 - auth - INFO - Created new user: annouser
2025-07-12 12:54:17,821 - auth - INFO - Created new user: annouser
2025-07-12 12:54:22,396 - auth - INFO - Created new user: annouser
2025-07-12 12:54:26,991 - auth - INFO - Created new user: annouser
2025-07-12 12:54:31,536 - auth - INFO - Created new user: annouser
2025-07-12 12:54:36,121 - auth - INFO - Created new user: annouser
2025-07-12 12:54:40,701 - auth - INFO - Created new user: annouser
2025-07-12 12:54:45,310 - auth - INFO - Created new user: annouser
2025-07-12 12:54:49,891 - auth - INFO - Created new user: annouser
2025-07-12 12:54:54,483 - auth - INFO - Created new user: annouser
2025-07-12 12:54:59,065 - auth - INFO - Created new user: audituser
2025-07-12 12:55:03,647 - auth - INFO - Created new user: audituser
2025-07-12 12:55:08,223 - auth - INFO - Created new user: audituser
2025-07-12 12:55:12,798 - auth - INFO - Created new user: audituser
2025-07-12 12:55:17,399 - auth - INFO - Created new user: audituser
2025-07-12 12:55:22,110 - auth - INFO - Created new user: audituser
2025-07-12 12:55:26,707 - auth - INFO - Created new user: audituser
2025-07-12 12:55:31,312 - auth - INFO - Created new user: audituser
2025-07-12 12:55:35,933 - auth - INFO - Created new user: wrongaudit
2025-07-12 12:55:40,571 - auth - INFO - Created new user: testuser
2025-07-12 12:55:53,583 - auth - INFO - Created new user: changepw
2025-07-12 12:55:54,136 - auth - INFO - Updated password for user: changepw
2025-07-12 12:55:58,697 - auth - INFO - Created new user: a
2025-07-12 12:56:03,088 - auth - INFO - Created new user: minpw
2025-07-12 12:56:07,488 - auth - INFO - Created new user: logoutuser
2025-07-12 12:56:20,504 - auth - INFO - Created new user: cpuser
2025-07-12 12:56:25,260 - auth - INFO - Created new user: du
2025-07-12 12:56:25,643 - auth - INFO - Created new user: deluser
2025-07-12 12:56:30,208 - auth - INFO - Created new user: clientuser
2025-07-12 12:56:34,796 - auth - INFO - Created new user: clientuser
2025-07-12 12:56:39,285 - auth - INFO - Created new user: notclient
2025-07-12 12:56:39,677 - auth - INFO - Created new user: concuradmin1752305199483
2025-07-12 12:56:40,110 - auth - INFO - Created new user: concuser17523051999011
2025-07-12 12:56:40,110 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523051999013', 'Concurrent User 17523051999013', '<EMAIL>', 'annotator', '$2b$12$1skVYd1TechNpTHklW9/7OU/ioQVyGLgaxzk.ELI2PJoZ6SAHxGNq', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 12:56:40,110 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523051999010', 'Concurrent User 17523051999010', '<EMAIL>', 'annotator', '$2b$12$lGIrxIYjNF8bJY0zT7Xt9e/qgRvi/ZaGvEwu6EHyt4EWQbxbsnEqO', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 12:56:40,110 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 12:56:40,110 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523051999012', 'Concurrent User 17523051999012', '<EMAIL>', 'annotator', '$2b$12$CnudrqKH6U07ahl5hDhjc.dYwQM7UoknnzpPua6..x8gtGD2hJfjW', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 12:56:40,826 - auth - INFO - Created new user: duplicateuser1752305200534
2025-07-12 12:56:40,827 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.username
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752305200534', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$OEomdmlw1MWOiItELZfOv./gvCnioSJT3heCSSHkZJVY93/H7eXlS', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 12:56:40,834 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.username
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752305200534', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$C//wDcqyIpsKGknxXET67uPSz3Xf8YgA7sfrJhlpRjT8k0DINEzpy', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 12:56:41,026 - auth - INFO - Created new user: annotator_conc2
2025-07-12 12:56:41,841 - auth - INFO - Created new user: fileadmin2
2025-07-12 12:56:42,062 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 12:56:42,064 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 12:56:42,273 - auth - INFO - Created new user: wpuser
2025-07-12 12:56:42,790 - auth - INFO - Created new user: ehadmin
2025-07-12 12:56:43,313 - auth - INFO - Created new user: ehadmin
2025-07-12 12:56:43,848 - auth - INFO - Created new user: ehannotator
2025-07-12 12:56:44,359 - auth - INFO - Created new user: ehannotator
2025-07-12 12:56:44,901 - auth - INFO - Created new user: ehannotator
2025-07-12 12:56:45,320 - auth - INFO - Created new user: largeuser
2025-07-12 12:56:45,510 - auth - INFO - Created new user: concurrentuser
2025-07-12 12:56:47,003 - auth - INFO - Created new user: kbadmin
2025-07-12 12:56:47,531 - auth - INFO - Created new user: kbaudit
2025-07-12 12:56:48,603 - auth - INFO - Created new user: perfuser
2025-07-12 12:56:50,305 - auth - INFO - Created new user: auser
2025-07-12 12:56:50,864 - auth - INFO - Created new user: secureuser
2025-07-12 12:56:51,628 - auth - INFO - Created new user: encuser
2025-07-12 12:56:51,961 - auth - INFO - Created new user: loginuser
2025-07-12 12:56:52,489 - auth - INFO - Created new user: meuser
2025-07-12 12:56:52,996 - auth - INFO - Created new user: refreshuser
2025-07-12 12:56:53,538 - auth - INFO - Created new user: changepassuser
2025-07-12 12:56:54,095 - auth - INFO - Updated password for user: changepassuser
2025-07-12 12:56:54,788 - auth - INFO - Created new user: changepassuser2
2025-07-12 12:56:55,494 - auth - INFO - Created new user: logoutuser
2025-07-12 12:56:55,995 - auth - INFO - Created new user: verifyuser
2025-07-12 12:56:56,619 - auth - INFO - Created new user: annuser
2025-07-12 12:56:57,001 - auth - INFO - Created new user: adminuser
2025-07-12 12:56:57,391 - auth - INFO - Created new user: dupuser
2025-07-12 12:56:57,713 - auth - INFO - Created new user: cookieattruser
2025-07-12 12:56:58,226 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 12:56:58,734 - auth - INFO - Created new user: refreshflowuser
2025-07-12 12:56:59,253 - auth - INFO - Created new user: schemachangepass
2025-07-12 12:56:59,773 - auth - INFO - Created new user: tamperuser
2025-07-12 12:57:00,285 - auth - INFO - Created new user: auduser
2025-07-12 12:57:00,668 - auth - INFO - Created new user: realannotator
2025-07-12 12:57:01,182 - auth - INFO - Created new user: annforaud
2025-07-12 12:57:01,568 - auth - INFO - Created new user: trueauditor
2025-07-12 12:57:06,187 - auth - INFO - Created new user: admin
2025-07-12 12:57:06,565 - auth - INFO - Created new user: supuser
2025-07-12 12:57:11,185 - auth - INFO - Created new user: admin
2025-07-12 12:57:11,563 - auth - INFO - Created new user: supuser
2025-07-12 12:57:16,167 - auth - INFO - Created new user: admin
2025-07-12 12:57:16,538 - auth - INFO - Created new user: supuser
2025-07-12 12:57:21,138 - auth - INFO - Created new user: admin
2025-07-12 12:57:21,522 - auth - INFO - Created new user: supuser
2025-07-12 12:57:26,119 - auth - INFO - Created new user: admin
2025-07-12 12:57:26,493 - auth - INFO - Created new user: supuser
2025-07-12 12:57:31,123 - auth - INFO - Created new user: admin
2025-07-12 12:57:31,497 - auth - INFO - Created new user: supuser
2025-07-12 12:57:36,098 - auth - INFO - Created new user: admin
2025-07-12 12:57:36,470 - auth - INFO - Created new user: supuser
2025-07-12 12:57:41,032 - auth - INFO - Created new user: admin
2025-07-12 12:57:41,405 - auth - INFO - Created new user: supuser
2025-07-12 12:57:45,994 - auth - INFO - Created new user: admin
2025-07-12 12:57:46,368 - auth - INFO - Created new user: supuser
2025-07-12 12:57:50,949 - auth - INFO - Created new user: anno_sup
2025-07-12 12:57:55,633 - auth - INFO - Created new user: admin
2025-07-12 12:57:56,006 - auth - INFO - Created new user: supuser
2025-07-12 12:58:00,593 - auth - INFO - Created new user: admin
2025-07-12 12:58:00,967 - auth - INFO - Created new user: supuser
2025-07-12 12:58:05,548 - auth - INFO - Created new user: admin
2025-07-12 12:58:05,923 - auth - INFO - Created new user: supuser
2025-07-12 12:58:10,511 - auth - INFO - Created new user: admin
2025-07-12 12:58:10,883 - auth - INFO - Created new user: supuser
2025-07-12 12:58:11,299 - auth - INFO - Created new user: synthadmin
2025-07-12 12:58:11,778 - auth - INFO - Created new user: radio_user
2025-07-12 12:58:12,198 - auth - INFO - Created new user: tgadmin
2025-07-12 12:58:15,607 - auth - INFO - Created new user: concurrentuser
2025-07-12 12:58:16,590 - auth - INFO - Created new user: authzanno
2025-07-12 12:58:16,979 - auth - INFO - Created new user: authzauditor
2025-07-12 12:58:17,361 - auth - INFO - Created new user: authzclient
2025-07-12 12:58:17,759 - auth - INFO - Created new user: authzadmin
2025-07-12 12:58:18,756 - auth - INFO - Created new user: smoketest
2025-07-12 13:01:25,562 - auth - INFO - Created new user: adminuser
2025-07-12 13:01:30,286 - auth - INFO - Created new user: adminuser
2025-07-12 13:01:34,866 - auth - INFO - Created new user: adminuser
2025-07-12 13:01:35,259 - auth - INFO - Created new user: newuser
2025-07-12 13:05:11,197 - auth - INFO - Created new user: adminuser
2025-07-12 13:05:15,893 - auth - INFO - Created new user: adminuser
2025-07-12 13:05:20,511 - auth - INFO - Created new user: adminuser
2025-07-12 13:05:20,882 - auth - INFO - Created new user: newuser
2025-07-12 13:05:25,331 - auth - INFO - Created new user: adminuser
2025-07-12 13:05:25,692 - auth - INFO - Created new user: a
2025-07-12 13:05:25,890 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 13:05:26,081 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 13:05:30,479 - auth - INFO - Created new user: adminuser
2025-07-12 13:05:35,091 - auth - INFO - Created new user: adminuser
2025-07-12 13:05:39,712 - auth - INFO - Created new user: adminuser
2025-07-12 13:05:48,388 - auth - INFO - Created new user: adminuser
2025-07-12 13:05:52,966 - auth - INFO - Created new user: adminuser
2025-07-12 13:05:57,550 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:02,176 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:06,778 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:11,367 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:15,977 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:20,545 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:25,139 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:29,737 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:34,345 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:38,921 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:43,508 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:48,103 - auth - INFO - Created new user: adminuser
2025-07-12 13:06:52,701 - auth - INFO - Created new user: adminuser
2025-07-12 13:07:01,360 - auth - INFO - Created new user: anno_user
2025-07-12 13:07:06,004 - auth - INFO - Created new user: anno_user
2025-07-12 13:07:10,577 - auth - INFO - Created new user: anno_user
2025-07-12 13:07:15,302 - auth - INFO - Created new user: adminuser
2025-07-12 13:07:19,894 - auth - INFO - Created new user: adminuser
2025-07-12 13:07:20,085 - auth - INFO - Updated user: adminuser
2025-07-12 13:07:20,085 - auth - INFO - Updated user: adminuser
2025-07-12 13:07:24,486 - auth - INFO - Created new user: adminuser
2025-07-12 13:07:29,097 - auth - INFO - Created new user: adminuser
2025-07-12 13:07:33,694 - auth - INFO - Created new user: adminuser
2025-07-12 13:07:38,280 - auth - INFO - Created new user: annouser
2025-07-12 13:07:42,884 - auth - INFO - Created new user: annouser
2025-07-12 13:07:47,492 - auth - INFO - Created new user: annouser
2025-07-12 13:07:52,087 - auth - INFO - Created new user: annouser
2025-07-12 13:07:56,707 - auth - INFO - Created new user: annouser
2025-07-12 13:08:01,279 - auth - INFO - Created new user: annouser
2025-07-12 13:08:05,873 - auth - INFO - Created new user: annouser
2025-07-12 13:08:10,489 - auth - INFO - Created new user: annouser
2025-07-12 13:08:15,065 - auth - INFO - Created new user: annouser
2025-07-12 13:08:19,669 - auth - INFO - Created new user: annouser
2025-07-12 13:08:24,251 - auth - INFO - Created new user: audituser
2025-07-12 13:08:28,860 - auth - INFO - Created new user: audituser
2025-07-12 13:08:33,465 - auth - INFO - Created new user: audituser
2025-07-12 13:08:38,074 - auth - INFO - Created new user: audituser
2025-07-12 13:08:42,676 - auth - INFO - Created new user: audituser
2025-07-12 13:08:47,317 - auth - INFO - Created new user: audituser
2025-07-12 13:08:51,897 - auth - INFO - Created new user: audituser
2025-07-12 13:08:56,496 - auth - INFO - Created new user: audituser
2025-07-12 13:09:01,109 - auth - INFO - Created new user: wrongaudit
2025-07-12 13:09:05,779 - auth - INFO - Created new user: testuser
2025-07-12 13:09:18,865 - auth - INFO - Created new user: changepw
2025-07-12 13:09:19,413 - auth - INFO - Updated password for user: changepw
2025-07-12 13:09:23,986 - auth - INFO - Created new user: a
2025-07-12 13:09:28,413 - auth - INFO - Created new user: minpw
2025-07-12 13:09:32,832 - auth - INFO - Created new user: logoutuser
2025-07-12 13:09:45,864 - auth - INFO - Created new user: cpuser
2025-07-12 13:09:50,623 - auth - INFO - Created new user: du
2025-07-12 13:09:50,989 - auth - INFO - Created new user: deluser
2025-07-12 13:09:55,586 - auth - INFO - Created new user: clientuser
2025-07-12 13:10:00,164 - auth - INFO - Created new user: clientuser
2025-07-12 13:10:04,654 - auth - INFO - Created new user: notclient
2025-07-12 13:10:05,050 - auth - INFO - Created new user: concuradmin1752306004848
2025-07-12 13:10:05,480 - auth - INFO - Created new user: concuser17523060052714
2025-07-12 13:10:05,481 - auth - INFO - Created new user: concuser17523060052710
2025-07-12 13:10:05,481 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) not an error
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523060052713', 'Concurrent User 17523060052713', '<EMAIL>', 'annotator', '$2b$12$5bcAXBzieAHmgQD6zG6hj.UlFYPAYgD.2MxXVYY1xFXskNzTEIje6', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:10:05,481 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523060052712', 'Concurrent User 17523060052712', '<EMAIL>', 'annotator', '$2b$12$He.LQZt/mla60oXj5L0HtuH1KToCIXgb9SXd3j6UzGN1YXp3SW922', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:10:05,481 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523060052711', 'Concurrent User 17523060052711', '<EMAIL>', 'annotator', '$2b$12$UY5sIHCKi6TgOkEXyCcKTezbbTMj43j73SZXDYriSIij8mBHKSuA6', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:10:06,140 - auth - INFO - Created new user: duplicateuser1752306005848
2025-07-12 13:10:06,140 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752306005848', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$WDaq0m6i2qQLGjHdIMyk2uSv.9dtGvPu094iiE8XtlcwxyFhFcoF6', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 13:10:06,140 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752306005848', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$GqqnGJdM5eg07xNEBNjqn.3yYI9iGS4rtSFf8P61s0hjzTSP0mtbK', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 13:10:06,336 - auth - INFO - Created new user: annotator_conc2
2025-07-12 13:10:07,116 - auth - INFO - Created new user: fileadmin2
2025-07-12 13:10:07,337 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:10:07,593 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot commit transaction - SQL statements in progress
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:10:08,003 - auth - INFO - Created new user: ehadmin
2025-07-12 13:10:08,561 - auth - INFO - Created new user: ehadmin
2025-07-12 13:10:09,124 - auth - INFO - Created new user: ehannotator
2025-07-12 13:10:09,672 - auth - INFO - Created new user: ehannotator
2025-07-12 13:10:10,242 - auth - INFO - Created new user: ehannotator
2025-07-12 13:10:10,688 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('largeuser', 'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA ... (9702 characters truncated) ... AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA', '<EMAIL>', 'annotator', '$2b$12$Jm3T0xNT6XWZQ9igcyajMeiKKu3fODIGa79fgC11v0d64QgMFEIxm', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:10:10,879 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concurrentuser', 'Concurrent User', '<EMAIL>', 'annotator', '$2b$12$krIDoaDjSpJzBxY0N0wLmeib51Rl8UooMnjtmeFROuBbmYcU/AM5.', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:10:12,146 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('kbadmin', 'KB Admin', '<EMAIL>', 'admin', '$2b$12$7D2IlxwhoDULZq09Q6mrmOoLvAYjdsITOc/U9m1fRZaC6CkypmI9S', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:10:12,413 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('kbaudit', 'KB Audit', '<EMAIL>', 'annotator', '$2b$12$70vIirt2Tt26Fj2Z5TCZ3e.bLlpdrK3sbKbFOqU5cK6o2Dvxl4asu', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:10:13,112 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('perfuser', 'Performance User', '<EMAIL>', 'annotator', '$2b$12$0WV42AFih5lVeaa4HE9llugEuBI2BcznmRimP5Kql.JhxKW0aj0Bq', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:10:14,740 - auth - INFO - Created new user: auser
2025-07-12 13:10:15,347 - auth - INFO - Created new user: secureuser
2025-07-12 13:10:16,122 - auth - INFO - Created new user: encuser
2025-07-12 13:10:16,463 - auth - INFO - Created new user: loginuser
2025-07-12 13:10:16,979 - auth - INFO - Created new user: meuser
2025-07-12 13:10:17,494 - auth - INFO - Created new user: refreshuser
2025-07-12 13:10:18,022 - auth - INFO - Created new user: changepassuser
2025-07-12 13:10:18,564 - auth - INFO - Updated password for user: changepassuser
2025-07-12 13:10:19,260 - auth - INFO - Created new user: changepassuser2
2025-07-12 13:10:19,958 - auth - INFO - Created new user: logoutuser
2025-07-12 13:10:20,488 - auth - INFO - Created new user: verifyuser
2025-07-12 13:10:21,103 - auth - INFO - Created new user: annuser
2025-07-12 13:10:21,474 - auth - INFO - Created new user: adminuser
2025-07-12 13:10:21,860 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('dupuser', 'Dup User', '<EMAIL>', 'annotator', '$2b$12$b6JzQ/xRu8Td0aZ4r9cNYuOFh9zjO9.IJ0kDrrImCCd49Pajga3Si', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:10:22,198 - auth - INFO - Created new user: cookieattruser
2025-07-12 13:10:22,714 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 13:10:23,233 - auth - INFO - Created new user: refreshflowuser
2025-07-12 13:10:23,740 - auth - INFO - Created new user: schemachangepass
2025-07-12 13:10:24,287 - auth - INFO - Created new user: tamperuser
2025-07-12 13:10:24,804 - auth - INFO - Created new user: auduser
2025-07-12 13:10:25,176 - auth - INFO - Created new user: realannotator
2025-07-12 13:10:25,705 - auth - INFO - Created new user: annforaud
2025-07-12 13:10:26,077 - auth - INFO - Created new user: trueauditor
2025-07-12 13:10:30,697 - auth - INFO - Created new user: admin
2025-07-12 13:10:31,064 - auth - INFO - Created new user: supuser
2025-07-12 13:10:35,680 - auth - INFO - Created new user: admin
2025-07-12 13:10:36,067 - auth - INFO - Created new user: supuser
2025-07-12 13:10:40,649 - auth - INFO - Created new user: admin
2025-07-12 13:10:41,028 - auth - INFO - Created new user: supuser
2025-07-12 13:10:45,638 - auth - INFO - Created new user: admin
2025-07-12 13:10:46,012 - auth - INFO - Created new user: supuser
2025-07-12 13:10:50,597 - auth - INFO - Created new user: admin
2025-07-12 13:10:50,987 - auth - INFO - Created new user: supuser
2025-07-12 13:10:55,611 - auth - INFO - Created new user: admin
2025-07-12 13:10:55,966 - auth - INFO - Created new user: supuser
2025-07-12 13:11:00,565 - auth - INFO - Created new user: admin
2025-07-12 13:11:00,930 - auth - INFO - Created new user: supuser
2025-07-12 13:11:05,518 - auth - INFO - Created new user: admin
2025-07-12 13:11:05,902 - auth - INFO - Created new user: supuser
2025-07-12 13:11:10,518 - auth - INFO - Created new user: admin
2025-07-12 13:11:10,894 - auth - INFO - Created new user: supuser
2025-07-12 13:11:15,482 - auth - INFO - Created new user: anno_sup
2025-07-12 13:11:20,162 - auth - INFO - Created new user: admin
2025-07-12 13:11:20,545 - auth - INFO - Created new user: supuser
2025-07-12 13:11:25,137 - auth - INFO - Created new user: admin
2025-07-12 13:11:25,503 - auth - INFO - Created new user: supuser
2025-07-12 13:11:30,112 - auth - INFO - Created new user: admin
2025-07-12 13:11:30,476 - auth - INFO - Created new user: supuser
2025-07-12 13:11:35,077 - auth - INFO - Created new user: admin
2025-07-12 13:11:35,450 - auth - INFO - Created new user: supuser
2025-07-12 13:11:35,857 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('synthadmin', 'Synth Admin', '<EMAIL>', 'admin', '$2b$12$ysJA2nPl/zgYaoOebQjl/.rzqnFhV7X.RbOGNQYAnP0EvA13givxa', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:11:36,094 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('radio_user', 'Radio User', '<EMAIL>', 'annotator', '$2b$12$WCbPwaQhPq12ZY8TEBVsiu50ukh9oURT5ASOLDI/goXDCVJ2G.JFG', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:11:36,327 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('tgadmin', 'TG Admin', '<EMAIL>', 'admin', '$2b$12$AUOMHxtKUNXLy7GgF53WlePH6ULwjOTKQAciaqdEhPMxXJDvAfecS', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:11:36,783 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concurrentuser', 'Concurrent User', '<EMAIL>', 'annotator', '$2b$12$sI2I664uliOgnTXJspC.VecrtgJ4vyFxXyju/Bz3E2DGLovb9hcdG', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:11:37,646 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('authzanno', 'Anno User', '<EMAIL>', 'annotator', '$2b$12$Qyi8WHb8d66.TL9qG7Dix./bQRcXHh3vOGknxlSdjuOIYaFpkcxyu', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:11:37,833 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('authzauditor', 'Auditor User', '<EMAIL>', 'auditor', '$2b$12$AM8rMAVvmejjBt9XIbNIXesGv7ZTWlfZqJTzIwM.v/no2CNcwP8mq', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:11:38,022 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('authzclient', 'Client User', '<EMAIL>', 'client', '$2b$12$PYqJBRSpyMMajMYntQtwLeieARAKRw3fgTjNUpvBsgSsx1zt0fscO', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:11:38,215 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('authzadmin', 'Admin User', '<EMAIL>', 'admin', '$2b$12$VvjAOCA4vneLszX3XNLKSOxoko8Iackrj73KsVlvCZQfQXwyb4xiC', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:11:38,964 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('smoketest', 'Smoke Test User', '<EMAIL>', 'annotator', '$2b$12$K3IgEOsB1URlUjIaLgA12u2padWhQsiTlxoGJkqXIksGn1yKveHQ2', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:12:21,761 - auth - INFO - Created new user: adminuser
2025-07-12 13:12:26,504 - auth - INFO - Created new user: adminuser
2025-07-12 13:12:31,113 - auth - INFO - Created new user: adminuser
2025-07-12 13:12:31,519 - auth - INFO - Created new user: newuser
2025-07-12 13:12:35,957 - auth - INFO - Created new user: adminuser
2025-07-12 13:12:36,339 - auth - INFO - Created new user: a
2025-07-12 13:12:36,524 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 13:12:36,710 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 13:12:41,109 - auth - INFO - Created new user: adminuser
2025-07-12 13:12:45,699 - auth - INFO - Created new user: adminuser
2025-07-12 13:12:50,276 - auth - INFO - Created new user: adminuser
2025-07-12 13:12:58,920 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:03,506 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:08,105 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:12,710 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:17,344 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:21,940 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:26,523 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:31,109 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:35,689 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:40,307 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:44,915 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:49,501 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:54,059 - auth - INFO - Created new user: adminuser
2025-07-12 13:13:58,638 - auth - INFO - Created new user: adminuser
2025-07-12 13:14:03,208 - auth - INFO - Created new user: adminuser
2025-07-12 13:14:11,891 - auth - INFO - Created new user: anno_user
2025-07-12 13:14:16,548 - auth - INFO - Created new user: anno_user
2025-07-12 13:14:21,121 - auth - INFO - Created new user: anno_user
2025-07-12 13:14:25,830 - auth - INFO - Created new user: adminuser
2025-07-12 13:14:30,406 - auth - INFO - Created new user: adminuser
2025-07-12 13:14:30,604 - auth - INFO - Updated user: adminuser
2025-07-12 13:14:30,609 - auth - INFO - Updated user: adminuser
2025-07-12 13:14:35,027 - auth - INFO - Created new user: adminuser
2025-07-12 13:14:39,668 - auth - INFO - Created new user: adminuser
2025-07-12 13:14:44,251 - auth - INFO - Created new user: adminuser
2025-07-12 13:14:48,830 - auth - INFO - Created new user: annouser
2025-07-12 13:14:53,438 - auth - INFO - Created new user: annouser
2025-07-12 13:14:58,031 - auth - INFO - Created new user: annouser
2025-07-12 13:15:02,626 - auth - INFO - Created new user: annouser
2025-07-12 13:15:07,208 - auth - INFO - Created new user: annouser
2025-07-12 13:15:11,809 - auth - INFO - Created new user: annouser
2025-07-12 13:15:16,407 - auth - INFO - Created new user: annouser
2025-07-12 13:15:21,049 - auth - INFO - Created new user: annouser
2025-07-12 13:15:25,610 - auth - INFO - Created new user: annouser
2025-07-12 13:15:30,206 - auth - INFO - Created new user: annouser
2025-07-12 13:15:34,798 - auth - INFO - Created new user: audituser
2025-07-12 13:15:39,406 - auth - INFO - Created new user: audituser
2025-07-12 13:15:44,054 - auth - INFO - Created new user: audituser
2025-07-12 13:15:48,625 - auth - INFO - Created new user: audituser
2025-07-12 13:15:53,210 - auth - INFO - Created new user: audituser
2025-07-12 13:15:57,825 - auth - INFO - Created new user: audituser
2025-07-12 13:16:02,407 - auth - INFO - Created new user: audituser
2025-07-12 13:16:07,022 - auth - INFO - Created new user: audituser
2025-07-12 13:16:11,659 - auth - INFO - Created new user: wrongaudit
2025-07-12 13:16:16,347 - auth - INFO - Created new user: testuser
2025-07-12 13:16:29,414 - auth - INFO - Created new user: changepw
2025-07-12 13:16:29,987 - auth - INFO - Updated password for user: changepw
2025-07-12 13:16:34,590 - auth - INFO - Created new user: a
2025-07-12 13:16:38,995 - auth - INFO - Created new user: minpw
2025-07-12 13:16:43,456 - auth - INFO - Created new user: logoutuser
2025-07-12 13:16:56,496 - auth - INFO - Created new user: cpuser
2025-07-12 13:17:01,286 - auth - INFO - Created new user: du
2025-07-12 13:17:01,656 - auth - INFO - Created new user: deluser
2025-07-12 13:17:06,229 - auth - INFO - Created new user: clientuser
2025-07-12 13:17:10,809 - auth - INFO - Created new user: clientuser
2025-07-12 13:17:15,303 - auth - INFO - Created new user: notclient
2025-07-12 13:17:15,708 - auth - INFO - Created new user: concuradmin1752306435511
2025-07-12 13:17:16,152 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot start a transaction within a transaction
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523064359380', 'Concurrent User 17523064359380', '<EMAIL>', 'annotator', '$2b$12$mJqIdbtWqFuh6EHJTvRz8eGYRC74SPtCo0BnLrJgqt8FRAp7IGOXC', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:16,152 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523064359384', 'Concurrent User 17523064359384', '<EMAIL>', 'annotator', '$2b$12$34u4arH2HsAH0rSD39QSmeGfw9IITD1JVOKlzdBeOHUL1l5EuUFtu', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:17:16,152 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523064359383', 'Concurrent User 17523064359383', '<EMAIL>', 'annotator', '$2b$12$a8KpNUWL1I7zp5RGOvkBq.Gx367HXCyKKZnivrxePDzF8uclt5OnK', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:17:16,152 - auth - INFO - Created new user: concuser17523064359382
2025-07-12 13:17:16,153 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523064359381', 'Concurrent User 17523064359381', '<EMAIL>', 'annotator', '$2b$12$uzDkRRXWUTSUucU7eqGeeeQBbJ33KQSmRLRGrXJTaG/9VJV3jzv/G', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:17:16,363 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752306436161', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$RJRqoqf2OWpVKnwjyP304OFh4VadxGU8QTw75.kjXTEC3C8f60vCu', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 13:17:16,363 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot start a transaction within a transaction
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752306436161', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$.dWJDTsMvu56D0TA3Z52c.q43j3RQStbsBjMGUbphASDxrbDF13Om', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:16,365 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:17:17,056 - auth - INFO - Created new user: annotator_conc2
2025-07-12 13:17:17,871 - auth - INFO - Created new user: fileadmin2
2025-07-12 13:17:18,093 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.full_name AS users_full_name, users.email AS users_email, users.role AS users_role, users.password_hash AS users_password_hash, users.created_at AS users_created_at, users.last_login AS users_last_login, users.is_active AS users_is_active, users.annotator_mode AS users_annotator_mode 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('fileadmin2', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:17:18,094 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:17:18,306 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot commit transaction - SQL statements in progress
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:18,647 - auth - INFO - Created new user: ehadmin
2025-07-12 13:17:19,174 - auth - INFO - Created new user: ehadmin
2025-07-12 13:17:19,700 - auth - INFO - Created new user: ehannotator
2025-07-12 13:17:20,225 - auth - INFO - Created new user: ehannotator
2025-07-12 13:17:20,773 - auth - INFO - Created new user: ehannotator
2025-07-12 13:17:21,245 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('largeuser', 'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA ... (9702 characters truncated) ... AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA', '<EMAIL>', 'annotator', '$2b$12$5QvecPkdJSTPUU2yq6zTV.OAdZRscP1A.Ju5dZkaSHSYSCjhf5Dgq', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:21,439 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concurrentuser', 'Concurrent User', '<EMAIL>', 'annotator', '$2b$12$AmnL3hirXsrqUkzNSamMA.F41xjLfZw8G98ieChFaiP1NJB88.SGG', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:22,620 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('kbadmin', 'KB Admin', '<EMAIL>', 'admin', '$2b$12$yBaJxK96a6K87fiMIl3ov.lNtuvwNNArUcK8R2xC.AHCtgWSychAy', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:22,903 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('kbaudit', 'KB Audit', '<EMAIL>', 'annotator', '$2b$12$UPfwAc/YJkIz5DoPs1XF.OYGOYg3xOl7Ta8.Bs7XK.ODVZw7WF1MG', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:23,658 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('perfuser', 'Performance User', '<EMAIL>', 'annotator', '$2b$12$EBWkuJ7nrmngm9Lr2eFS9e0LgSEmiDT51krATZGvgUVQI2VilM18.', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:25,232 - auth - INFO - Created new user: auser
2025-07-12 13:17:25,838 - auth - INFO - Created new user: secureuser
2025-07-12 13:17:26,611 - auth - INFO - Created new user: encuser
2025-07-12 13:17:26,960 - auth - INFO - Created new user: loginuser
2025-07-12 13:17:27,517 - auth - INFO - Created new user: meuser
2025-07-12 13:17:28,047 - auth - INFO - Created new user: refreshuser
2025-07-12 13:17:28,575 - auth - INFO - Created new user: changepassuser
2025-07-12 13:17:29,138 - auth - INFO - Updated password for user: changepassuser
2025-07-12 13:17:29,861 - auth - INFO - Created new user: changepassuser2
2025-07-12 13:17:30,580 - auth - INFO - Created new user: logoutuser
2025-07-12 13:17:31,115 - auth - INFO - Created new user: verifyuser
2025-07-12 13:17:31,688 - auth - INFO - Created new user: annuser
2025-07-12 13:17:32,077 - auth - INFO - Created new user: adminuser
2025-07-12 13:17:32,475 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('dupuser', 'Dup User', '<EMAIL>', 'annotator', '$2b$12$2SsqRp.KTmREFS1fMD9eN.R16l7Z54fHgzhvycW4lbSjCpvLqQJce', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:32,826 - auth - INFO - Created new user: cookieattruser
2025-07-12 13:17:33,344 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 13:17:33,865 - auth - INFO - Created new user: refreshflowuser
2025-07-12 13:17:34,390 - auth - INFO - Created new user: schemachangepass
2025-07-12 13:17:34,922 - auth - INFO - Created new user: tamperuser
2025-07-12 13:17:35,451 - auth - INFO - Created new user: auduser
2025-07-12 13:17:35,836 - auth - INFO - Created new user: realannotator
2025-07-12 13:17:36,405 - auth - INFO - Created new user: annforaud
2025-07-12 13:17:36,792 - auth - INFO - Created new user: trueauditor
2025-07-12 13:17:41,442 - auth - INFO - Created new user: admin
2025-07-12 13:17:41,815 - auth - INFO - Created new user: supuser
2025-07-12 13:17:46,435 - auth - INFO - Created new user: admin
2025-07-12 13:17:46,807 - auth - INFO - Created new user: supuser
2025-07-12 13:17:51,414 - auth - INFO - Created new user: admin
2025-07-12 13:17:51,815 - auth - INFO - Created new user: supuser
2025-07-12 13:17:56,492 - auth - INFO - Created new user: admin
2025-07-12 13:17:56,881 - auth - INFO - Created new user: supuser
2025-07-12 13:18:01,495 - auth - INFO - Created new user: admin
2025-07-12 13:18:01,868 - auth - INFO - Created new user: supuser
2025-07-12 13:18:06,516 - auth - INFO - Created new user: admin
2025-07-12 13:18:06,886 - auth - INFO - Created new user: supuser
2025-07-12 13:18:11,537 - auth - INFO - Created new user: admin
2025-07-12 13:18:11,915 - auth - INFO - Created new user: supuser
2025-07-12 13:18:16,558 - auth - INFO - Created new user: admin
2025-07-12 13:18:16,926 - auth - INFO - Created new user: supuser
2025-07-12 13:18:21,540 - auth - INFO - Created new user: admin
2025-07-12 13:18:21,919 - auth - INFO - Created new user: supuser
2025-07-12 13:18:26,559 - auth - INFO - Created new user: anno_sup
2025-07-12 13:18:31,212 - auth - INFO - Created new user: admin
2025-07-12 13:18:31,586 - auth - INFO - Created new user: supuser
2025-07-12 13:18:36,202 - auth - INFO - Created new user: admin
2025-07-12 13:18:36,564 - auth - INFO - Created new user: supuser
2025-07-12 13:18:41,158 - auth - INFO - Created new user: admin
2025-07-12 13:18:41,536 - auth - INFO - Created new user: supuser
2025-07-12 13:18:46,124 - auth - INFO - Created new user: admin
2025-07-12 13:18:46,494 - auth - INFO - Created new user: supuser
2025-07-12 13:18:46,901 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('synthadmin', 'Synth Admin', '<EMAIL>', 'admin', '$2b$12$eQTbeJ9h2FSvfbh2lSWu7OrJRoopQVGUx1BMoCIs4gbLwqNLoJ/ze', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:18:47,134 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('radio_user', 'Radio User', '<EMAIL>', 'annotator', '$2b$12$g44.Q/tlomP6g7UmMh5Fwe9LnNAJO9MZgXfH5rONpwa9TxAYOc7Zm', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:18:47,369 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('tgadmin', 'TG Admin', '<EMAIL>', 'admin', '$2b$12$uRySbJIzNkJ8RpKJcqtFLeLyRbthYWGSUKwfQSVUIGPkPVN/GIaVi', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:18:47,815 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concurrentuser', 'Concurrent User', '<EMAIL>', 'annotator', '$2b$12$1V9TIu6zaohqP1XnxCRA3uEey2Og.qd7WllwnOyRkVeXSUpCfe5mS', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:18:48,735 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('authzanno', 'Anno User', '<EMAIL>', 'annotator', '$2b$12$DDBrOs/l58U1Nn5VpLawPeCr6LwABCU4aJV2HpJ9sGgUM35DtG24i', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:18:48,922 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('authzauditor', 'Auditor User', '<EMAIL>', 'auditor', '$2b$12$COF1jJbs6V/uwO1Nrfwriu4UoM2V0QkxhAyRdbDY7tzZJo4rrzTCe', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:18:49,119 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('authzclient', 'Client User', '<EMAIL>', 'client', '$2b$12$kpaHpQrP.iJ2IexD6lWk5.80ZEUX56d84Z6znB8wrOr7lS.QWHfuu', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:18:49,302 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('authzadmin', 'Admin User', '<EMAIL>', 'admin', '$2b$12$sl4ynVJflEEue8S5p5D.H.6aEv7swxlvQGtQYJWJK6ZC3suOXoA.W', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:18:50,015 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('smoketest', 'Smoke Test User', '<EMAIL>', 'annotator', '$2b$12$3I9M9OMNTM2pL3i3TLDgb.jcyWgXkfCr3PtdxylaZQVI4BqI/rPpS', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:19:39,782 - auth - INFO - Created new user: concuradmin1752306579573
2025-07-12 13:19:40,220 - auth - INFO - Created new user: concuser17523065800161
2025-07-12 13:19:40,229 - auth - INFO - Created new user: concuser17523065800162
2025-07-12 13:19:40,231 - auth - INFO - Created new user: concuser17523065800163
2025-07-12 13:19:40,233 - auth - INFO - Created new user: concuser17523065800164
2025-07-12 13:19:40,248 - auth - INFO - Created new user: concuser17523065800160
2025-07-12 13:19:40,452 - auth - INFO - Created new user: duplicateuser1752306580250
2025-07-12 13:19:40,455 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752306580250', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$5jwByw2tWp7sfpE9BaqyIuOYGSTwH8X5QkVBkA5/BciGOVEn7jKbi', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 13:19:40,468 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752306580250', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$q/OdofEBrWTDNtlPj1bWAOG5U8EcQOXzY4dqE9Jj3wwmgreHQwV.6', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 13:19:40,661 - auth - INFO - Created new user: annotator_conc2
2025-07-12 13:19:41,486 - auth - INFO - Created new user: fileadmin2
2025-07-12 13:20:34,324 - auth - INFO - Created new user: wpuser
2025-07-12 13:20:34,860 - auth - INFO - Created new user: ehadmin
2025-07-12 13:20:35,405 - auth - INFO - Created new user: ehadmin
2025-07-12 13:20:35,922 - auth - INFO - Created new user: ehannotator
2025-07-12 13:20:36,437 - auth - INFO - Created new user: ehannotator
2025-07-12 13:20:36,983 - auth - INFO - Created new user: ehannotator
2025-07-12 13:20:37,405 - auth - INFO - Created new user: largeuser
2025-07-12 13:20:37,619 - auth - INFO - Created new user: concurrentuser
2025-07-12 13:21:02,695 - auth - INFO - Created new user: kbadmin
2025-07-12 13:21:03,371 - auth - INFO - Created new user: kbaudit
2025-07-12 13:21:25,142 - auth - INFO - Created new user: secureuser
2025-07-12 13:21:25,882 - auth - INFO - Created new user: encuser
2025-07-12 13:21:26,205 - auth - INFO - Created new user: loginuser
2025-07-12 13:21:26,700 - auth - INFO - Created new user: meuser
2025-07-12 13:21:27,217 - auth - INFO - Created new user: refreshuser
2025-07-12 13:21:27,724 - auth - INFO - Created new user: changepassuser
2025-07-12 13:21:28,266 - auth - INFO - Updated password for user: changepassuser
2025-07-12 13:21:28,936 - auth - INFO - Created new user: changepassuser2
2025-07-12 13:21:29,617 - auth - INFO - Created new user: logoutuser
2025-07-12 13:21:30,109 - auth - INFO - Created new user: verifyuser
2025-07-12 13:21:30,638 - auth - INFO - Created new user: annuser
2025-07-12 13:21:31,008 - auth - INFO - Created new user: adminuser
2025-07-12 13:21:31,392 - auth - INFO - Created new user: dupuser
2025-07-12 13:21:31,686 - auth - INFO - Created new user: cookieattruser
2025-07-12 13:21:32,167 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 13:21:32,656 - auth - INFO - Created new user: refreshflowuser
2025-07-12 13:21:33,151 - auth - INFO - Created new user: schemachangepass
2025-07-12 13:21:33,688 - auth - INFO - Created new user: tamperuser
2025-07-12 13:21:34,178 - auth - INFO - Created new user: auduser
2025-07-12 13:21:34,549 - auth - INFO - Created new user: realannotator
2025-07-12 13:21:35,045 - auth - INFO - Created new user: annforaud
2025-07-12 13:21:35,421 - auth - INFO - Created new user: trueauditor
2025-07-12 13:21:58,893 - auth - INFO - Created new user: synthadmin
2025-07-12 13:21:59,540 - auth - INFO - Created new user: radio_user
2025-07-12 13:22:20,798 - auth - INFO - Created new user: tgadmin
2025-07-12 13:22:39,847 - auth - INFO - Created new user: perfuser
2025-07-12 13:36:34,560 - auth - INFO - Created new user: adminuser
2025-07-12 13:36:39,300 - auth - INFO - Created new user: adminuser
2025-07-12 13:36:43,881 - auth - INFO - Created new user: adminuser
2025-07-12 13:36:44,260 - auth - INFO - Created new user: newuser
2025-07-12 13:36:48,672 - auth - INFO - Created new user: adminuser
2025-07-12 13:36:49,042 - auth - INFO - Created new user: a
2025-07-12 13:36:49,233 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 13:36:49,419 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 13:36:53,818 - auth - INFO - Created new user: adminuser
2025-07-12 13:36:58,394 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:03,005 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:11,692 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:16,276 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:20,852 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:25,425 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:29,988 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:34,593 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:39,170 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:43,728 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:48,297 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:52,876 - auth - INFO - Created new user: adminuser
2025-07-12 13:37:57,478 - auth - INFO - Created new user: adminuser
2025-07-12 13:38:02,063 - auth - INFO - Created new user: adminuser
2025-07-12 13:38:06,626 - auth - INFO - Created new user: adminuser
2025-07-12 13:38:11,210 - auth - INFO - Created new user: adminuser
2025-07-12 13:38:15,788 - auth - INFO - Created new user: adminuser
2025-07-12 13:38:24,444 - auth - INFO - Created new user: anno_user
2025-07-12 13:38:29,038 - auth - INFO - Created new user: anno_user
2025-07-12 13:38:33,658 - auth - INFO - Created new user: anno_user
2025-07-12 13:38:38,453 - auth - INFO - Created new user: adminuser
2025-07-12 13:38:43,120 - auth - INFO - Created new user: adminuser
2025-07-12 13:38:43,321 - auth - INFO - Updated user: adminuser
2025-07-12 13:38:43,329 - auth - INFO - Updated user: adminuser
2025-07-12 13:38:47,736 - auth - INFO - Created new user: adminuser
2025-07-12 13:38:52,395 - auth - INFO - Created new user: adminuser
2025-07-12 13:38:57,016 - auth - INFO - Created new user: adminuser
2025-07-12 13:39:01,587 - auth - INFO - Created new user: annouser
2025-07-12 13:39:06,157 - auth - INFO - Created new user: annouser
2025-07-12 13:39:10,739 - auth - INFO - Created new user: annouser
2025-07-12 13:39:15,318 - auth - INFO - Created new user: annouser
2025-07-12 13:39:19,887 - auth - INFO - Created new user: annouser
2025-07-12 13:39:24,501 - auth - INFO - Created new user: annouser
2025-07-12 13:39:29,095 - auth - INFO - Created new user: annouser
2025-07-12 13:39:33,711 - auth - INFO - Created new user: annouser
2025-07-12 13:39:38,275 - auth - INFO - Created new user: annouser
2025-07-12 13:39:42,875 - auth - INFO - Created new user: annouser
2025-07-12 13:39:47,518 - auth - INFO - Created new user: audituser
2025-07-12 13:39:52,087 - auth - INFO - Created new user: audituser
2025-07-12 13:39:56,677 - auth - INFO - Created new user: audituser
2025-07-12 13:40:01,273 - auth - INFO - Created new user: audituser
2025-07-12 13:40:05,856 - auth - INFO - Created new user: audituser
2025-07-12 13:40:10,470 - auth - INFO - Created new user: audituser
2025-07-12 13:40:15,060 - auth - INFO - Created new user: audituser
2025-07-12 13:40:19,647 - auth - INFO - Created new user: audituser
2025-07-12 13:40:24,238 - auth - INFO - Created new user: wrongaudit
2025-07-12 13:40:28,928 - auth - INFO - Created new user: testuser
2025-07-12 13:40:41,936 - auth - INFO - Created new user: changepw
2025-07-12 13:40:42,486 - auth - INFO - Updated password for user: changepw
2025-07-12 13:40:47,045 - auth - INFO - Created new user: a
2025-07-12 13:40:51,446 - auth - INFO - Created new user: minpw
2025-07-12 13:40:55,841 - auth - INFO - Created new user: logoutuser
2025-07-12 13:41:08,826 - auth - INFO - Created new user: cpuser
2025-07-12 13:41:13,610 - auth - INFO - Created new user: du
2025-07-12 13:41:13,980 - auth - INFO - Created new user: deluser
2025-07-12 13:41:18,568 - auth - INFO - Created new user: clientuser
2025-07-12 13:41:23,161 - auth - INFO - Created new user: clientuser
2025-07-12 13:41:27,621 - auth - INFO - Created new user: notclient
2025-07-12 13:41:28,026 - auth - INFO - Created new user: concuradmin1752307887825
2025-07-12 13:41:29,386 - auth - INFO - Created new user: concuser17523078891771
2025-07-12 13:41:29,390 - auth - INFO - Created new user: concuser17523078891772
2025-07-12 13:41:29,392 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot start a transaction within a transaction
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523078891770', 'Concurrent User 17523078891770', '<EMAIL>', 'annotator', '$2b$12$CshmH4Cq6h48rl6Ipg86ieWngJOk7/IfN5hXHdq8gDvFVgiw6JHzq', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:41:29,392 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523078891774', 'Concurrent User 17523078891774', '<EMAIL>', 'annotator', '$2b$12$dRlDsD5S7vn9bgUYDTyp5OGh7wy7mMqYww8aJO074YeIeip1voEnO', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:41:29,394 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Error binding parameter 0 - probably unsupported type.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523078891773', 'Concurrent User 17523078891773', '<EMAIL>', 'annotator', '$2b$12$jAqr75mt.hkKYVnxbg7eVOqviBJMaekkwHvWpBaRw4XV3Zv6nkelC', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:41:29,606 - auth - INFO - Created new user: duplicateuser1752307889403
2025-07-12 13:41:29,606 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752307889403', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$5BESqnkDlFl0hwZlDrWhguSOmXvM0uJDq6Sa9GkKUXuO5ZnmIqp1y', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 13:41:29,607 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752307889403', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$BUpnw0xFAY4ULS4rgxgVuud8vmD.c7sTCBvtEtCDv9Gc7mf4sEXP2', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 13:41:29,803 - auth - INFO - Created new user: annotator_conc2
2025-07-12 13:41:30,609 - auth - INFO - Created new user: fileadmin2
2025-07-12 13:41:30,833 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.full_name AS users_full_name, users.email AS users_email, users.role AS users_role, users.password_hash AS users_password_hash, users.created_at AS users_created_at, users.last_login AS users_last_login, users.is_active AS users_is_active, users.annotator_mode AS users_annotator_mode 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('fileadmin2', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:41:30,834 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:41:30,860 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Error binding parameter 0 - probably unsupported type.
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.full_name AS users_full_name, users.email AS users_email, users.role AS users_role, users.password_hash AS users_password_hash, users.created_at AS users_created_at, users.last_login AS users_last_login, users.is_active AS users_is_active, users.annotator_mode AS users_annotator_mode 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('wpuser', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:41:31,199 - auth - INFO - Created new user: ehadmin
2025-07-12 13:41:31,725 - auth - INFO - Created new user: ehadmin
2025-07-12 13:41:32,238 - auth - INFO - Created new user: ehannotator
2025-07-12 13:41:32,759 - auth - INFO - Created new user: ehannotator
2025-07-12 13:41:33,306 - auth - INFO - Created new user: ehannotator
2025-07-12 13:41:33,733 - auth - INFO - Created new user: largeuser
2025-07-12 13:41:33,930 - auth - INFO - Created new user: concurrentuser
2025-07-12 13:41:35,448 - auth - INFO - Created new user: kbadmin
2025-07-12 13:41:35,973 - auth - INFO - Created new user: kbaudit
2025-07-12 13:41:37,073 - auth - INFO - Created new user: perfuser
2025-07-12 13:41:38,761 - auth - INFO - Created new user: auser
2025-07-12 13:41:39,326 - auth - INFO - Created new user: secureuser
2025-07-12 13:41:40,082 - auth - INFO - Created new user: encuser
2025-07-12 13:41:40,420 - auth - INFO - Created new user: loginuser
2025-07-12 13:41:40,940 - auth - INFO - Created new user: meuser
2025-07-12 13:41:41,458 - auth - INFO - Created new user: refreshuser
2025-07-12 13:41:41,973 - auth - INFO - Created new user: changepassuser
2025-07-12 13:41:42,535 - auth - INFO - Updated password for user: changepassuser
2025-07-12 13:41:43,236 - auth - INFO - Created new user: changepassuser2
2025-07-12 13:41:43,928 - auth - INFO - Created new user: logoutuser
2025-07-12 13:41:44,436 - auth - INFO - Created new user: verifyuser
2025-07-12 13:41:45,045 - auth - INFO - Created new user: annuser
2025-07-12 13:41:45,449 - auth - INFO - Created new user: adminuser
2025-07-12 13:41:45,909 - auth - INFO - Created new user: dupuser
2025-07-12 13:41:46,270 - auth - INFO - Created new user: cookieattruser
2025-07-12 13:41:46,807 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 13:41:47,327 - auth - INFO - Created new user: refreshflowuser
2025-07-12 13:41:47,864 - auth - INFO - Created new user: schemachangepass
2025-07-12 13:41:48,423 - auth - INFO - Created new user: tamperuser
2025-07-12 13:41:49,002 - auth - INFO - Created new user: auduser
2025-07-12 13:41:49,410 - auth - INFO - Created new user: realannotator
2025-07-12 13:41:49,982 - auth - INFO - Created new user: annforaud
2025-07-12 13:41:50,361 - auth - INFO - Created new user: trueauditor
2025-07-12 13:41:54,971 - auth - INFO - Created new user: admin
2025-07-12 13:41:55,341 - auth - INFO - Created new user: supuser
2025-07-12 13:41:59,917 - auth - INFO - Created new user: admin
2025-07-12 13:42:00,284 - auth - INFO - Created new user: supuser
2025-07-12 13:42:04,843 - auth - INFO - Created new user: admin
2025-07-12 13:42:05,226 - auth - INFO - Created new user: supuser
2025-07-12 13:42:09,833 - auth - INFO - Created new user: admin
2025-07-12 13:42:10,207 - auth - INFO - Created new user: supuser
2025-07-12 13:42:14,794 - auth - INFO - Created new user: admin
2025-07-12 13:42:15,176 - auth - INFO - Created new user: supuser
2025-07-12 13:42:19,783 - auth - INFO - Created new user: admin
2025-07-12 13:42:20,156 - auth - INFO - Created new user: supuser
2025-07-12 13:42:24,781 - auth - INFO - Created new user: admin
2025-07-12 13:42:25,161 - auth - INFO - Created new user: supuser
2025-07-12 13:42:29,748 - auth - INFO - Created new user: admin
2025-07-12 13:42:30,126 - auth - INFO - Created new user: supuser
2025-07-12 13:42:34,740 - auth - INFO - Created new user: admin
2025-07-12 13:42:35,118 - auth - INFO - Created new user: supuser
2025-07-12 13:42:39,726 - auth - INFO - Created new user: anno_sup
2025-07-12 13:42:44,360 - auth - INFO - Created new user: admin
2025-07-12 13:42:44,740 - auth - INFO - Created new user: supuser
2025-07-12 13:42:49,345 - auth - INFO - Created new user: admin
2025-07-12 13:42:49,701 - auth - INFO - Created new user: supuser
2025-07-12 13:42:54,306 - auth - INFO - Created new user: admin
2025-07-12 13:42:54,675 - auth - INFO - Created new user: supuser
2025-07-12 13:42:59,233 - auth - INFO - Created new user: admin
2025-07-12 13:42:59,611 - auth - INFO - Created new user: supuser
2025-07-12 13:43:00,042 - auth - INFO - Created new user: synthadmin
2025-07-12 13:43:00,529 - auth - INFO - Created new user: radio_user
2025-07-12 13:43:00,954 - auth - INFO - Created new user: tgadmin
2025-07-12 13:43:04,250 - auth - INFO - Created new user: concurrentuser
2025-07-12 13:43:05,201 - auth - INFO - Created new user: authzanno
2025-07-12 13:43:05,585 - auth - INFO - Created new user: authzauditor
2025-07-12 13:43:05,958 - auth - INFO - Created new user: authzclient
2025-07-12 13:43:06,340 - auth - INFO - Created new user: authzadmin
2025-07-12 13:43:07,384 - auth - INFO - Created new user: smoketest
2025-07-12 13:51:49,663 - auth - INFO - Created new user: adminuser
2025-07-12 13:51:54,354 - auth - INFO - Created new user: adminuser
2025-07-12 13:51:58,930 - auth - INFO - Created new user: adminuser
2025-07-12 13:51:59,307 - auth - INFO - Created new user: newuser
2025-07-12 13:52:03,693 - auth - INFO - Created new user: adminuser
2025-07-12 13:52:04,067 - auth - INFO - Created new user: a
2025-07-12 13:52:04,256 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 13:52:04,445 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 13:52:08,832 - auth - INFO - Created new user: adminuser
2025-07-12 13:52:13,429 - auth - INFO - Created new user: adminuser
2025-07-12 13:52:17,998 - auth - INFO - Created new user: adminuser
2025-07-12 13:52:26,660 - auth - INFO - Created new user: adminuser
2025-07-12 13:52:31,262 - auth - INFO - Created new user: adminuser
2025-07-12 13:52:35,913 - auth - INFO - Created new user: adminuser
2025-07-12 13:52:40,523 - auth - INFO - Created new user: adminuser
2025-07-12 13:52:45,119 - auth - INFO - Created new user: adminuser
2025-07-12 13:52:49,744 - auth - INFO - Created new user: adminuser
2025-07-12 13:52:54,327 - auth - INFO - Created new user: adminuser
2025-07-12 13:52:58,942 - auth - INFO - Created new user: adminuser
2025-07-12 13:53:03,544 - auth - INFO - Created new user: adminuser
2025-07-12 13:53:08,112 - auth - INFO - Created new user: adminuser
2025-07-12 13:53:12,719 - auth - INFO - Created new user: adminuser
2025-07-12 13:53:17,284 - auth - INFO - Created new user: adminuser
2025-07-12 13:53:21,902 - auth - INFO - Created new user: adminuser
2025-07-12 13:53:26,479 - auth - INFO - Created new user: adminuser
2025-07-12 13:53:31,062 - auth - INFO - Created new user: adminuser
2025-07-12 13:53:39,739 - auth - INFO - Created new user: anno_user
2025-07-12 13:53:44,348 - auth - INFO - Created new user: anno_user
2025-07-12 13:53:49,019 - auth - INFO - Created new user: anno_user
2025-07-12 13:53:53,758 - auth - INFO - Created new user: adminuser
2025-07-12 13:53:58,332 - auth - INFO - Created new user: adminuser
2025-07-12 13:53:58,545 - auth - INFO - Updated user: adminuser
2025-07-12 13:53:58,552 - auth - INFO - Updated user: adminuser
2025-07-12 13:54:02,946 - auth - INFO - Created new user: adminuser
2025-07-12 13:54:07,571 - auth - INFO - Created new user: adminuser
2025-07-12 13:54:12,162 - auth - INFO - Created new user: adminuser
2025-07-12 13:54:16,745 - auth - INFO - Created new user: annouser
2025-07-12 13:54:21,337 - auth - INFO - Created new user: annouser
2025-07-12 13:54:25,927 - auth - INFO - Created new user: annouser
2025-07-12 13:54:30,522 - auth - INFO - Created new user: annouser
2025-07-12 13:54:35,094 - auth - INFO - Created new user: annouser
2025-07-12 13:54:39,676 - auth - INFO - Created new user: annouser
2025-07-12 13:54:44,283 - auth - INFO - Created new user: annouser
2025-07-12 13:54:48,926 - auth - INFO - Created new user: annouser
2025-07-12 13:54:53,494 - auth - INFO - Created new user: annouser
2025-07-12 13:54:58,061 - auth - INFO - Created new user: annouser
2025-07-12 13:55:02,663 - auth - INFO - Created new user: audituser
2025-07-12 13:55:07,247 - auth - INFO - Created new user: audituser
2025-07-12 13:55:11,848 - auth - INFO - Created new user: audituser
2025-07-12 13:55:16,460 - auth - INFO - Created new user: audituser
2025-07-12 13:55:21,059 - auth - INFO - Created new user: audituser
2025-07-12 13:55:25,717 - auth - INFO - Created new user: audituser
2025-07-12 13:55:30,315 - auth - INFO - Created new user: audituser
2025-07-12 13:55:34,910 - auth - INFO - Created new user: audituser
2025-07-12 13:55:39,522 - auth - INFO - Created new user: wrongaudit
2025-07-12 13:55:44,153 - auth - INFO - Created new user: testuser
2025-07-12 13:55:57,155 - auth - INFO - Created new user: changepw
2025-07-12 13:55:57,728 - auth - INFO - Updated password for user: changepw
2025-07-12 13:56:02,302 - auth - INFO - Created new user: a
2025-07-12 13:56:06,688 - auth - INFO - Created new user: minpw
2025-07-12 13:56:11,098 - auth - INFO - Created new user: logoutuser
2025-07-12 13:56:24,098 - auth - INFO - Created new user: cpuser
2025-07-12 13:56:28,872 - auth - INFO - Created new user: du
2025-07-12 13:56:29,258 - auth - INFO - Created new user: deluser
2025-07-12 13:56:33,864 - auth - INFO - Created new user: clientuser
2025-07-12 13:56:38,481 - auth - INFO - Created new user: clientuser
2025-07-12 13:56:42,963 - auth - INFO - Created new user: notclient
2025-07-12 13:56:43,357 - auth - INFO - Created new user: concuradmin1752308803161
2025-07-12 13:56:43,791 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot start a transaction within a transaction
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523088035813', 'Concurrent User 17523088035813', '<EMAIL>', 'annotator', '$2b$12$UX4rrfcHfimU31rtWOypouNbxtKUk6MCAEgXuMGZGNG0cOQyUZIx6', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:56:43,792 - auth - INFO - Created new user: concuser17523088035811
2025-07-12 13:56:43,793 - auth - INFO - Created new user: concuser17523088035810
2025-07-12 13:56:43,793 - auth - INFO - Created new user: concuser17523088035812
2025-07-12 13:56:43,794 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523088035814', 'Concurrent User 17523088035814', '<EMAIL>', 'annotator', '$2b$12$SXwNZCZkwoFeDzrbYSmybeShTvthe84jQAw3XnWXWJ/NkQl22ZxgG', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:56:44,509 - auth - ERROR - Database error in create_user: Could not refresh instance '<User at 0x2469f0770d0>'
2025-07-12 13:56:44,509 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752308804212', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$bGmwf18FB99cYpTal9bnl.tGiZfN0YiM7o6Sq/b2fEEcjHXtFxqv2', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 13:56:44,517 - auth - INFO - Created new user: duplicateuser1752308804212
2025-07-12 13:56:44,714 - auth - INFO - Created new user: annotator_conc2
2025-07-12 13:56:45,487 - auth - INFO - Created new user: fileadmin2
2025-07-12 13:56:45,701 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:56:45,703 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:56:45,705 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:56:46,037 - auth - INFO - Created new user: wpuser
2025-07-12 13:56:46,565 - auth - INFO - Created new user: ehadmin
2025-07-12 13:56:46,775 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Error binding parameter 0 - probably unsupported type.
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.full_name AS users_full_name, users.email AS users_email, users.role AS users_role, users.password_hash AS users_password_hash, users.created_at AS users_created_at, users.last_login AS users_last_login, users.is_active AS users_is_active, users.annotator_mode AS users_annotator_mode 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('wpuser', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 13:56:47,086 - auth - INFO - Created new user: ehadmin
2025-07-12 13:56:47,601 - auth - INFO - Created new user: ehannotator
2025-07-12 13:56:48,117 - auth - INFO - Created new user: ehannotator
2025-07-12 13:56:48,703 - auth - INFO - Created new user: ehannotator
2025-07-12 13:56:49,123 - auth - INFO - Created new user: largeuser
2025-07-12 13:56:49,319 - auth - INFO - Created new user: concurrentuser
2025-07-12 13:56:50,865 - auth - INFO - Created new user: kbadmin
2025-07-12 13:56:51,441 - auth - INFO - Created new user: kbaudit
2025-07-12 13:56:52,668 - auth - INFO - Created new user: perfuser
2025-07-12 13:56:54,475 - auth - INFO - Created new user: auser
2025-07-12 13:56:55,137 - auth - INFO - Created new user: secureuser
2025-07-12 13:56:55,900 - auth - INFO - Created new user: encuser
2025-07-12 13:56:56,237 - auth - INFO - Created new user: loginuser
2025-07-12 13:56:56,763 - auth - INFO - Created new user: meuser
2025-07-12 13:56:57,262 - auth - INFO - Created new user: refreshuser
2025-07-12 13:56:57,765 - auth - INFO - Created new user: changepassuser
2025-07-12 13:56:58,324 - auth - INFO - Updated password for user: changepassuser
2025-07-12 13:56:59,009 - auth - INFO - Created new user: changepassuser2
2025-07-12 13:56:59,712 - auth - INFO - Created new user: logoutuser
2025-07-12 13:57:00,227 - auth - INFO - Created new user: verifyuser
2025-07-12 13:57:00,762 - auth - INFO - Created new user: annuser
2025-07-12 13:57:01,130 - auth - INFO - Created new user: adminuser
2025-07-12 13:57:01,512 - auth - INFO - Created new user: dupuser
2025-07-12 13:57:01,831 - auth - INFO - Created new user: cookieattruser
2025-07-12 13:57:02,348 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 13:57:02,871 - auth - INFO - Created new user: refreshflowuser
2025-07-12 13:57:03,378 - auth - INFO - Created new user: schemachangepass
2025-07-12 13:57:03,921 - auth - INFO - Created new user: tamperuser
2025-07-12 13:57:04,440 - auth - INFO - Created new user: auduser
2025-07-12 13:57:04,825 - auth - INFO - Created new user: realannotator
2025-07-12 13:57:05,375 - auth - INFO - Created new user: annforaud
2025-07-12 13:57:05,750 - auth - INFO - Created new user: trueauditor
2025-07-12 13:57:10,338 - auth - INFO - Created new user: admin
2025-07-12 13:57:10,725 - auth - INFO - Created new user: supuser
2025-07-12 13:57:15,277 - auth - INFO - Created new user: admin
2025-07-12 13:57:15,643 - auth - INFO - Created new user: supuser
2025-07-12 13:57:20,230 - auth - INFO - Created new user: admin
2025-07-12 13:57:20,588 - auth - INFO - Created new user: supuser
2025-07-12 13:57:25,157 - auth - INFO - Created new user: admin
2025-07-12 13:57:25,529 - auth - INFO - Created new user: supuser
2025-07-12 13:57:30,114 - auth - INFO - Created new user: admin
2025-07-12 13:57:30,488 - auth - INFO - Created new user: supuser
2025-07-12 13:57:35,057 - auth - INFO - Created new user: admin
2025-07-12 13:57:35,453 - auth - INFO - Created new user: supuser
2025-07-12 13:57:40,073 - auth - INFO - Created new user: admin
2025-07-12 13:57:40,442 - auth - INFO - Created new user: supuser
2025-07-12 13:57:45,003 - auth - INFO - Created new user: admin
2025-07-12 13:57:45,369 - auth - INFO - Created new user: supuser
2025-07-12 13:57:49,914 - auth - INFO - Created new user: admin
2025-07-12 13:57:50,271 - auth - INFO - Created new user: supuser
2025-07-12 13:57:54,835 - auth - INFO - Created new user: anno_sup
2025-07-12 13:57:59,464 - auth - INFO - Created new user: admin
2025-07-12 13:57:59,821 - auth - INFO - Created new user: supuser
2025-07-12 13:58:04,406 - auth - INFO - Created new user: admin
2025-07-12 13:58:04,777 - auth - INFO - Created new user: supuser
2025-07-12 13:58:09,325 - auth - INFO - Created new user: admin
2025-07-12 13:58:09,694 - auth - INFO - Created new user: supuser
2025-07-12 13:58:14,255 - auth - INFO - Created new user: admin
2025-07-12 13:58:14,614 - auth - INFO - Created new user: supuser
2025-07-12 13:58:15,017 - auth - INFO - Created new user: synthadmin
2025-07-12 13:58:15,486 - auth - INFO - Created new user: radio_user
2025-07-12 13:58:15,901 - auth - INFO - Created new user: tgadmin
2025-07-12 13:58:19,285 - auth - INFO - Created new user: concurrentuser
2025-07-12 13:58:20,252 - auth - INFO - Created new user: authzanno
2025-07-12 13:58:20,616 - auth - INFO - Created new user: authzauditor
2025-07-12 13:58:21,002 - auth - INFO - Created new user: authzclient
2025-07-12 13:58:21,368 - auth - INFO - Created new user: authzadmin
2025-07-12 13:58:22,336 - auth - INFO - Created new user: smoketest
2025-07-12 14:01:38,190 - auth - INFO - Created new user: adminuser
2025-07-12 14:01:42,875 - auth - INFO - Created new user: adminuser
2025-07-12 14:01:47,472 - auth - INFO - Created new user: adminuser
2025-07-12 14:01:47,837 - auth - INFO - Created new user: newuser
2025-07-12 14:01:52,248 - auth - INFO - Created new user: adminuser
2025-07-12 14:01:52,632 - auth - INFO - Created new user: a
2025-07-12 14:01:52,821 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 14:01:53,011 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 14:01:57,387 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:01,982 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:06,539 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:15,195 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:19,785 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:24,356 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:28,939 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:33,507 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:38,118 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:42,691 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:47,272 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:51,870 - auth - INFO - Created new user: adminuser
2025-07-12 14:02:56,477 - auth - INFO - Created new user: adminuser
2025-07-12 14:03:01,066 - auth - INFO - Created new user: adminuser
2025-07-12 14:03:05,678 - auth - INFO - Created new user: adminuser
2025-07-12 14:03:10,267 - auth - INFO - Created new user: adminuser
2025-07-12 14:03:14,860 - auth - INFO - Created new user: adminuser
2025-07-12 14:03:19,420 - auth - INFO - Created new user: adminuser
2025-07-12 14:03:28,030 - auth - INFO - Created new user: anno_user
2025-07-12 14:03:32,628 - auth - INFO - Created new user: anno_user
2025-07-12 14:03:37,215 - auth - INFO - Created new user: anno_user
2025-07-12 14:03:41,947 - auth - INFO - Created new user: adminuser
2025-07-12 14:03:46,519 - auth - INFO - Created new user: adminuser
2025-07-12 14:03:46,721 - auth - INFO - Updated user: adminuser
2025-07-12 14:03:46,727 - auth - INFO - Updated user: adminuser
2025-07-12 14:03:51,124 - auth - INFO - Created new user: adminuser
2025-07-12 14:03:55,745 - auth - INFO - Created new user: adminuser
2025-07-12 14:04:00,362 - auth - INFO - Created new user: adminuser
2025-07-12 14:04:04,953 - auth - INFO - Created new user: annouser
2025-07-12 14:04:09,517 - auth - INFO - Created new user: annouser
2025-07-12 14:04:14,122 - auth - INFO - Created new user: annouser
2025-07-12 14:04:18,786 - auth - INFO - Created new user: annouser
2025-07-12 14:04:23,378 - auth - INFO - Created new user: annouser
2025-07-12 14:04:27,971 - auth - INFO - Created new user: annouser
2025-07-12 14:04:32,560 - auth - INFO - Created new user: annouser
2025-07-12 14:04:37,197 - auth - INFO - Created new user: annouser
2025-07-12 14:04:41,770 - auth - INFO - Created new user: annouser
2025-07-12 14:04:46,355 - auth - INFO - Created new user: annouser
2025-07-12 14:04:50,955 - auth - INFO - Created new user: audituser
2025-07-12 14:04:55,545 - auth - INFO - Created new user: audituser
2025-07-12 14:05:00,125 - auth - INFO - Created new user: audituser
2025-07-12 14:05:04,775 - auth - INFO - Created new user: audituser
2025-07-12 14:05:09,369 - auth - INFO - Created new user: audituser
2025-07-12 14:05:14,028 - auth - INFO - Created new user: audituser
2025-07-12 14:05:18,638 - auth - INFO - Created new user: audituser
2025-07-12 14:05:23,269 - auth - INFO - Created new user: audituser
2025-07-12 14:05:27,887 - auth - INFO - Created new user: wrongaudit
2025-07-12 14:05:32,542 - auth - INFO - Created new user: testuser
2025-07-12 14:05:45,593 - auth - INFO - Created new user: changepw
2025-07-12 14:05:46,150 - auth - INFO - Updated password for user: changepw
2025-07-12 14:05:50,733 - auth - INFO - Created new user: a
2025-07-12 14:05:55,155 - auth - INFO - Created new user: minpw
2025-07-12 14:05:59,553 - auth - INFO - Created new user: logoutuser
2025-07-12 14:06:12,606 - auth - INFO - Created new user: cpuser
2025-07-12 14:06:17,388 - auth - INFO - Created new user: du
2025-07-12 14:06:17,752 - auth - INFO - Created new user: deluser
2025-07-12 14:06:22,341 - auth - INFO - Created new user: clientuser
2025-07-12 14:06:26,943 - auth - INFO - Created new user: clientuser
2025-07-12 14:06:31,402 - auth - INFO - Created new user: notclient
2025-07-12 14:06:31,806 - auth - INFO - Created new user: concuradmin1752309391607
2025-07-12 14:06:32,230 - auth - INFO - Created new user: concuser17523093920134
2025-07-12 14:06:32,230 - auth - ERROR - Database error in create_user: (sqlite3.DatabaseError) not an error
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-12 14:06:32,230 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523093920130', 'Concurrent User 17523093920130', '<EMAIL>', 'annotator', '$2b$12$iFHpKe3TK8erEPEa6Bqcju4W9DyDsxif/PMPEdXmzGQ8Xao3UzOHK', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:06:32,230 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Error binding parameter 0 - probably unsupported type.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523093920131', 'Concurrent User 17523093920131', '<EMAIL>', 'annotator', '$2b$12$kjNS/s5E2l.ZibguAora.ODf3Pt0Zfsg9tSsjbgLwDbRmt10Q.9vW', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:06:32,240 - auth - INFO - Created new user: concuser17523093920133
2025-07-12 14:06:32,439 - auth - INFO - Created new user: duplicateuser1752309392240
2025-07-12 14:06:32,439 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752309392240', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$lotO95Oanm53wSg0GwRMFec2bAnNsPOP1/aIIxouFBlCDfBAnyNYW', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 14:06:32,439 - auth - ERROR - Database error in create_user: This result object does not return rows. It has been closed automatically.
2025-07-12 14:06:32,640 - auth - INFO - Created new user: annotator_conc2
2025-07-12 14:06:33,786 - auth - INFO - Created new user: fileadmin2
2025-07-12 14:06:34,072 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:06:34,072 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Error binding parameter 0 - probably unsupported type.
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.full_name AS users_full_name, users.email AS users_email, users.role AS users_role, users.password_hash AS users_password_hash, users.created_at AS users_created_at, users.last_login AS users_last_login, users.is_active AS users_is_active, users.annotator_mode AS users_annotator_mode 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('fileadmin2', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:06:34,090 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:06:34,397 - auth - INFO - Created new user: wpuser
2025-07-12 14:06:34,912 - auth - INFO - Created new user: ehadmin
2025-07-12 14:06:35,414 - auth - INFO - Created new user: ehadmin
2025-07-12 14:06:35,929 - auth - INFO - Created new user: ehannotator
2025-07-12 14:06:36,422 - auth - INFO - Created new user: ehannotator
2025-07-12 14:06:36,964 - auth - INFO - Created new user: ehannotator
2025-07-12 14:06:37,390 - auth - INFO - Created new user: largeuser
2025-07-12 14:06:37,581 - auth - INFO - Created new user: concurrentuser
2025-07-12 14:06:39,052 - auth - INFO - Created new user: kbadmin
2025-07-12 14:06:39,619 - auth - INFO - Created new user: kbaudit
2025-07-12 14:06:40,668 - auth - INFO - Created new user: perfuser
2025-07-12 14:06:42,423 - auth - INFO - Created new user: auser
2025-07-12 14:06:42,981 - auth - INFO - Created new user: secureuser
2025-07-12 14:06:43,726 - auth - INFO - Created new user: encuser
2025-07-12 14:06:44,049 - auth - INFO - Created new user: loginuser
2025-07-12 14:06:44,575 - auth - INFO - Created new user: meuser
2025-07-12 14:06:45,072 - auth - INFO - Created new user: refreshuser
2025-07-12 14:06:45,573 - auth - INFO - Created new user: changepassuser
2025-07-12 14:06:46,114 - auth - INFO - Updated password for user: changepassuser
2025-07-12 14:06:46,782 - auth - INFO - Created new user: changepassuser2
2025-07-12 14:06:47,481 - auth - INFO - Created new user: logoutuser
2025-07-12 14:06:47,980 - auth - INFO - Created new user: verifyuser
2025-07-12 14:06:48,497 - auth - INFO - Created new user: annuser
2025-07-12 14:06:48,883 - auth - INFO - Created new user: adminuser
2025-07-12 14:06:49,286 - auth - INFO - Created new user: dupuser
2025-07-12 14:06:49,629 - auth - INFO - Created new user: cookieattruser
2025-07-12 14:06:50,192 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 14:06:50,776 - auth - INFO - Created new user: refreshflowuser
2025-07-12 14:06:51,339 - auth - INFO - Created new user: schemachangepass
2025-07-12 14:06:51,892 - auth - INFO - Created new user: tamperuser
2025-07-12 14:06:52,414 - auth - INFO - Created new user: auduser
2025-07-12 14:06:52,806 - auth - INFO - Created new user: realannotator
2025-07-12 14:06:53,387 - auth - INFO - Created new user: annforaud
2025-07-12 14:06:53,791 - auth - INFO - Created new user: trueauditor
2025-07-12 14:06:58,428 - auth - INFO - Created new user: admin
2025-07-12 14:06:58,798 - auth - INFO - Created new user: supuser
2025-07-12 14:07:03,389 - auth - INFO - Created new user: admin
2025-07-12 14:07:03,753 - auth - INFO - Created new user: supuser
2025-07-12 14:07:08,349 - auth - INFO - Created new user: admin
2025-07-12 14:07:08,711 - auth - INFO - Created new user: supuser
2025-07-12 14:07:13,308 - auth - INFO - Created new user: admin
2025-07-12 14:07:13,678 - auth - INFO - Created new user: supuser
2025-07-12 14:07:18,258 - auth - INFO - Created new user: admin
2025-07-12 14:07:18,638 - auth - INFO - Created new user: supuser
2025-07-12 14:07:23,245 - auth - INFO - Created new user: admin
2025-07-12 14:07:23,606 - auth - INFO - Created new user: supuser
2025-07-12 14:07:28,174 - auth - INFO - Created new user: admin
2025-07-12 14:07:28,529 - auth - INFO - Created new user: supuser
2025-07-12 14:07:33,106 - auth - INFO - Created new user: admin
2025-07-12 14:07:33,479 - auth - INFO - Created new user: supuser
2025-07-12 14:07:38,051 - auth - INFO - Created new user: admin
2025-07-12 14:07:38,404 - auth - INFO - Created new user: supuser
2025-07-12 14:07:42,988 - auth - INFO - Created new user: anno_sup
2025-07-12 14:07:47,549 - auth - INFO - Created new user: admin
2025-07-12 14:07:47,919 - auth - INFO - Created new user: supuser
2025-07-12 14:07:52,493 - auth - INFO - Created new user: admin
2025-07-12 14:07:52,857 - auth - INFO - Created new user: supuser
2025-07-12 14:07:57,456 - auth - INFO - Created new user: admin
2025-07-12 14:07:57,822 - auth - INFO - Created new user: supuser
2025-07-12 14:08:02,381 - auth - INFO - Created new user: admin
2025-07-12 14:08:02,751 - auth - INFO - Created new user: supuser
2025-07-12 14:08:03,137 - auth - INFO - Created new user: synthadmin
2025-07-12 14:08:03,620 - auth - INFO - Created new user: radio_user
2025-07-12 14:08:04,019 - auth - INFO - Created new user: tgadmin
2025-07-12 14:08:07,898 - auth - INFO - Created new user: concurrentuser
2025-07-12 14:08:08,837 - auth - INFO - Created new user: authzanno
2025-07-12 14:08:09,202 - auth - INFO - Created new user: authzauditor
2025-07-12 14:08:09,585 - auth - INFO - Created new user: authzclient
2025-07-12 14:08:09,953 - auth - INFO - Created new user: authzadmin
2025-07-12 14:08:10,935 - auth - INFO - Created new user: smoketest
2025-07-12 14:09:43,409 - auth - INFO - Created new user: adminuser
2025-07-12 14:09:48,080 - auth - INFO - Created new user: adminuser
2025-07-12 14:09:52,606 - auth - INFO - Created new user: adminuser
2025-07-12 14:09:52,972 - auth - INFO - Created new user: newuser
2025-07-12 14:09:57,390 - auth - INFO - Created new user: adminuser
2025-07-12 14:09:57,754 - auth - INFO - Created new user: a
2025-07-12 14:09:57,938 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 14:09:58,112 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 14:10:02,476 - auth - INFO - Created new user: adminuser
2025-07-12 14:10:07,036 - auth - INFO - Created new user: adminuser
2025-07-12 14:10:11,605 - auth - INFO - Created new user: adminuser
2025-07-12 14:10:20,287 - auth - INFO - Created new user: adminuser
2025-07-12 14:10:24,870 - auth - INFO - Created new user: adminuser
2025-07-12 14:10:29,443 - auth - INFO - Created new user: adminuser
2025-07-12 14:10:34,035 - auth - INFO - Created new user: adminuser
2025-07-12 14:10:38,587 - auth - INFO - Created new user: adminuser
2025-07-12 14:10:43,164 - auth - INFO - Created new user: adminuser
2025-07-12 14:10:47,739 - auth - INFO - Created new user: adminuser
2025-07-12 14:10:52,303 - auth - INFO - Created new user: adminuser
2025-07-12 14:10:56,851 - auth - INFO - Created new user: adminuser
2025-07-12 14:11:01,420 - auth - INFO - Created new user: adminuser
2025-07-12 14:11:05,978 - auth - INFO - Created new user: adminuser
2025-07-12 14:11:10,554 - auth - INFO - Created new user: adminuser
2025-07-12 14:11:15,139 - auth - INFO - Created new user: adminuser
2025-07-12 14:11:19,718 - auth - INFO - Created new user: adminuser
2025-07-12 14:11:24,290 - auth - INFO - Created new user: adminuser
2025-07-12 14:11:32,943 - auth - INFO - Created new user: anno_user
2025-07-12 14:11:37,540 - auth - INFO - Created new user: anno_user
2025-07-12 14:11:42,090 - auth - INFO - Created new user: anno_user
2025-07-12 14:11:46,810 - auth - INFO - Created new user: adminuser
2025-07-12 14:11:51,403 - auth - INFO - Created new user: adminuser
2025-07-12 14:11:51,595 - auth - INFO - Updated user: adminuser
2025-07-12 14:11:51,601 - auth - INFO - Updated user: adminuser
2025-07-12 14:11:55,986 - auth - INFO - Created new user: adminuser
2025-07-12 14:12:00,565 - auth - INFO - Created new user: adminuser
2025-07-12 14:12:05,156 - auth - INFO - Created new user: adminuser
2025-07-12 14:12:09,734 - auth - INFO - Created new user: annouser
2025-07-12 14:12:14,307 - auth - INFO - Created new user: annouser
2025-07-12 14:12:18,885 - auth - INFO - Created new user: annouser
2025-07-12 14:12:23,444 - auth - INFO - Created new user: annouser
2025-07-12 14:12:28,002 - auth - INFO - Created new user: annouser
2025-07-12 14:12:32,606 - auth - INFO - Created new user: annouser
2025-07-12 14:12:37,179 - auth - INFO - Created new user: annouser
2025-07-12 14:12:41,787 - auth - INFO - Created new user: annouser
2025-07-12 14:12:46,343 - auth - INFO - Created new user: annouser
2025-07-12 14:12:50,933 - auth - INFO - Created new user: annouser
2025-07-12 14:12:55,505 - auth - INFO - Created new user: audituser
2025-07-12 14:13:00,072 - auth - INFO - Created new user: audituser
2025-07-12 14:13:04,647 - auth - INFO - Created new user: audituser
2025-07-12 14:13:09,220 - auth - INFO - Created new user: audituser
2025-07-12 14:13:13,777 - auth - INFO - Created new user: audituser
2025-07-12 14:13:18,369 - auth - INFO - Created new user: audituser
2025-07-12 14:13:22,947 - auth - INFO - Created new user: audituser
2025-07-12 14:13:27,480 - auth - INFO - Created new user: audituser
2025-07-12 14:13:32,083 - auth - INFO - Created new user: wrongaudit
2025-07-12 14:13:36,724 - auth - INFO - Created new user: testuser
2025-07-12 14:13:49,712 - auth - INFO - Created new user: changepw
2025-07-12 14:13:50,264 - auth - INFO - Updated password for user: changepw
2025-07-12 14:13:54,821 - auth - INFO - Created new user: a
2025-07-12 14:13:59,197 - auth - INFO - Created new user: minpw
2025-07-12 14:14:03,563 - auth - INFO - Created new user: logoutuser
2025-07-12 14:14:16,523 - auth - INFO - Created new user: cpuser
2025-07-12 14:14:21,304 - auth - INFO - Created new user: du
2025-07-12 14:14:21,686 - auth - INFO - Created new user: deluser
2025-07-12 14:14:26,216 - auth - INFO - Created new user: clientuser
2025-07-12 14:14:30,775 - auth - INFO - Created new user: clientuser
2025-07-12 14:14:35,215 - auth - INFO - Created new user: notclient
2025-07-12 14:14:35,605 - auth - INFO - Created new user: concuradmin1752309875417
2025-07-12 14:14:36,043 - auth - INFO - Created new user: concuser17523098758290
2025-07-12 14:14:36,043 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot commit transaction - SQL statements in progress
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 14:14:36,043 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523098758292', 'Concurrent User 17523098758292', '<EMAIL>', 'annotator', '$2b$12$UAbg0VtH9nNRmaED7rpFPuWsYwP2nd903.VC537yUsCIAYDj1Oi66', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:14:36,043 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot start a transaction within a transaction
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523098758291', 'Concurrent User 17523098758291', '<EMAIL>', 'annotator', '$2b$12$AsudXP/.Xe24UZOqjgGz1emI/c/n/Kw7PJ.Ug7zCCx5MPeENz6gl.', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 14:14:36,043 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523098758293', 'Concurrent User 17523098758293', '<EMAIL>', 'annotator', '$2b$12$p2eVJUN/RH6pKpW7ZNw9LOjQj3WlCS6KPufCLRAegYt7HHfSRT3t2', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:14:36,252 - auth - INFO - Created new user: duplicateuser1752309876051
2025-07-12 14:14:36,252 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752309876051', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$ulaharTsERoP9zwiPNwyhOu3HaYkl/11eBIYMaaoWeZWZZOJ8nT8y', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 14:14:36,252 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752309876051', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$4f6ZBii48bJWFukl.5TXPeAeDsBr8CV6IkR3iz4ZOVcUkKSuhT4W6', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 14:14:36,437 - auth - INFO - Created new user: annotator_conc2
2025-07-12 14:14:37,353 - auth - INFO - Created new user: fileadmin2
2025-07-12 14:14:37,572 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:14:38,414 - auth - INFO - Created new user: wpuser
2025-07-12 14:14:38,953 - auth - INFO - Created new user: ehadmin
2025-07-12 14:14:39,467 - auth - INFO - Created new user: ehadmin
2025-07-12 14:14:39,998 - auth - INFO - Created new user: ehannotator
2025-07-12 14:14:40,531 - auth - INFO - Created new user: ehannotator
2025-07-12 14:14:41,085 - auth - INFO - Created new user: ehannotator
2025-07-12 14:14:41,502 - auth - INFO - Created new user: largeuser
2025-07-12 14:14:41,698 - auth - INFO - Created new user: concurrentuser
2025-07-12 14:14:43,270 - auth - INFO - Created new user: kbadmin
2025-07-12 14:14:43,814 - auth - INFO - Created new user: kbaudit
2025-07-12 14:14:44,955 - auth - INFO - Created new user: perfuser
2025-07-12 14:14:46,755 - auth - INFO - Created new user: auser
2025-07-12 14:14:47,366 - auth - INFO - Created new user: secureuser
2025-07-12 14:14:48,163 - auth - INFO - Created new user: encuser
2025-07-12 14:14:48,513 - auth - INFO - Created new user: loginuser
2025-07-12 14:14:49,044 - auth - INFO - Created new user: meuser
2025-07-12 14:14:49,574 - auth - INFO - Created new user: refreshuser
2025-07-12 14:14:50,098 - auth - INFO - Created new user: changepassuser
2025-07-12 14:14:50,645 - auth - INFO - Updated password for user: changepassuser
2025-07-12 14:14:51,331 - auth - INFO - Created new user: changepassuser2
2025-07-12 14:14:52,039 - auth - INFO - Created new user: logoutuser
2025-07-12 14:14:52,557 - auth - INFO - Created new user: verifyuser
2025-07-12 14:14:53,121 - auth - INFO - Created new user: annuser
2025-07-12 14:14:53,498 - auth - INFO - Created new user: adminuser
2025-07-12 14:14:53,887 - auth - INFO - Created new user: dupuser
2025-07-12 14:14:54,220 - auth - INFO - Created new user: cookieattruser
2025-07-12 14:14:54,736 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 14:14:55,253 - auth - INFO - Created new user: refreshflowuser
2025-07-12 14:14:55,771 - auth - INFO - Created new user: schemachangepass
2025-07-12 14:14:56,304 - auth - INFO - Created new user: tamperuser
2025-07-12 14:14:56,821 - auth - INFO - Created new user: auduser
2025-07-12 14:14:57,197 - auth - INFO - Created new user: realannotator
2025-07-12 14:14:57,735 - auth - INFO - Created new user: annforaud
2025-07-12 14:14:58,113 - auth - INFO - Created new user: trueauditor
2025-07-12 14:15:02,701 - auth - INFO - Created new user: admin
2025-07-12 14:15:03,069 - auth - INFO - Created new user: supuser
2025-07-12 14:15:07,594 - auth - INFO - Created new user: admin
2025-07-12 14:15:07,955 - auth - INFO - Created new user: supuser
2025-07-12 14:15:12,546 - auth - INFO - Created new user: admin
2025-07-12 14:15:12,897 - auth - INFO - Created new user: supuser
2025-07-12 14:15:17,504 - auth - INFO - Created new user: admin
2025-07-12 14:15:17,855 - auth - INFO - Created new user: supuser
2025-07-12 14:15:22,399 - auth - INFO - Created new user: admin
2025-07-12 14:15:22,764 - auth - INFO - Created new user: supuser
2025-07-12 14:15:27,293 - auth - INFO - Created new user: admin
2025-07-12 14:15:27,650 - auth - INFO - Created new user: supuser
2025-07-12 14:15:32,279 - auth - INFO - Created new user: admin
2025-07-12 14:15:32,641 - auth - INFO - Created new user: supuser
2025-07-12 14:15:37,204 - auth - INFO - Created new user: admin
2025-07-12 14:15:37,576 - auth - INFO - Created new user: supuser
2025-07-12 14:15:42,147 - auth - INFO - Created new user: admin
2025-07-12 14:15:42,501 - auth - INFO - Created new user: supuser
2025-07-12 14:15:47,070 - auth - INFO - Created new user: anno_sup
2025-07-12 14:15:51,707 - auth - INFO - Created new user: admin
2025-07-12 14:15:52,065 - auth - INFO - Created new user: supuser
2025-07-12 14:15:56,622 - auth - INFO - Created new user: admin
2025-07-12 14:15:56,975 - auth - INFO - Created new user: supuser
2025-07-12 14:16:01,551 - auth - INFO - Created new user: admin
2025-07-12 14:16:01,946 - auth - INFO - Created new user: supuser
2025-07-12 14:16:06,495 - auth - INFO - Created new user: admin
2025-07-12 14:16:06,867 - auth - INFO - Created new user: supuser
2025-07-12 14:16:07,274 - auth - INFO - Created new user: synthadmin
2025-07-12 14:16:07,737 - auth - INFO - Created new user: radio_user
2025-07-12 14:16:08,154 - auth - INFO - Created new user: tgadmin
2025-07-12 14:16:11,172 - auth - INFO - Created new user: concurrentuser
2025-07-12 14:16:12,119 - auth - INFO - Created new user: authzanno
2025-07-12 14:16:12,500 - auth - INFO - Created new user: authzauditor
2025-07-12 14:16:12,876 - auth - INFO - Created new user: authzclient
2025-07-12 14:16:13,256 - auth - INFO - Created new user: authzadmin
2025-07-12 14:16:14,190 - auth - INFO - Created new user: smoketest
2025-07-12 14:18:32,266 - auth - INFO - Created new user: adminuser
2025-07-12 14:18:36,954 - auth - INFO - Created new user: adminuser
2025-07-12 14:18:41,524 - auth - INFO - Created new user: adminuser
2025-07-12 14:18:41,894 - auth - INFO - Created new user: newuser
2025-07-12 14:18:46,269 - auth - INFO - Created new user: adminuser
2025-07-12 14:18:46,629 - auth - INFO - Created new user: a
2025-07-12 14:18:46,803 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 14:18:46,996 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 14:18:51,366 - auth - INFO - Created new user: adminuser
2025-07-12 14:18:55,957 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:00,553 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:09,166 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:13,729 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:18,363 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:22,939 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:27,514 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:32,086 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:36,657 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:41,237 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:45,828 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:50,409 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:54,963 - auth - INFO - Created new user: adminuser
2025-07-12 14:19:59,507 - auth - INFO - Created new user: adminuser
2025-07-12 14:20:04,062 - auth - INFO - Created new user: adminuser
2025-07-12 14:20:08,637 - auth - INFO - Created new user: adminuser
2025-07-12 14:20:13,209 - auth - INFO - Created new user: adminuser
2025-07-12 14:20:21,857 - auth - INFO - Created new user: anno_user
2025-07-12 14:20:26,454 - auth - INFO - Created new user: anno_user
2025-07-12 14:20:31,040 - auth - INFO - Created new user: anno_user
2025-07-12 14:20:35,744 - auth - INFO - Created new user: adminuser
2025-07-12 14:20:40,334 - auth - INFO - Created new user: adminuser
2025-07-12 14:20:40,535 - auth - INFO - Updated user: adminuser
2025-07-12 14:20:40,541 - auth - INFO - Updated user: adminuser
2025-07-12 14:20:44,921 - auth - INFO - Created new user: adminuser
2025-07-12 14:20:49,478 - auth - INFO - Created new user: adminuser
2025-07-12 14:20:54,039 - auth - INFO - Created new user: adminuser
2025-07-12 14:20:58,633 - auth - INFO - Created new user: annouser
2025-07-12 14:21:03,198 - auth - INFO - Created new user: annouser
2025-07-12 14:21:07,772 - auth - INFO - Created new user: annouser
2025-07-12 14:21:12,347 - auth - INFO - Created new user: annouser
2025-07-12 14:21:16,883 - auth - INFO - Created new user: annouser
2025-07-12 14:21:21,489 - auth - INFO - Created new user: annouser
2025-07-12 14:21:26,091 - auth - INFO - Created new user: annouser
2025-07-12 14:21:30,682 - auth - INFO - Created new user: annouser
2025-07-12 14:21:35,261 - auth - INFO - Created new user: annouser
2025-07-12 14:21:39,813 - auth - INFO - Created new user: annouser
2025-07-12 14:21:44,379 - auth - INFO - Created new user: audituser
2025-07-12 14:21:48,944 - auth - INFO - Created new user: audituser
2025-07-12 14:21:53,522 - auth - INFO - Created new user: audituser
2025-07-12 14:21:58,107 - auth - INFO - Created new user: audituser
2025-07-12 14:22:02,679 - auth - INFO - Created new user: audituser
2025-07-12 14:22:07,266 - auth - INFO - Created new user: audituser
2025-07-12 14:22:11,842 - auth - INFO - Created new user: audituser
2025-07-12 14:22:16,426 - auth - INFO - Created new user: audituser
2025-07-12 14:22:21,002 - auth - INFO - Created new user: wrongaudit
2025-07-12 14:22:25,641 - auth - INFO - Created new user: testuser
2025-07-12 14:22:38,624 - auth - INFO - Created new user: changepw
2025-07-12 14:22:39,151 - auth - INFO - Updated password for user: changepw
2025-07-12 14:22:43,732 - auth - INFO - Created new user: a
2025-07-12 14:22:48,140 - auth - INFO - Created new user: minpw
2025-07-12 14:22:52,508 - auth - INFO - Created new user: logoutuser
2025-07-12 14:23:05,415 - auth - INFO - Created new user: cpuser
2025-07-12 14:23:10,152 - auth - INFO - Created new user: du
2025-07-12 14:23:10,520 - auth - INFO - Created new user: deluser
2025-07-12 14:23:15,083 - auth - INFO - Created new user: clientuser
2025-07-12 14:23:19,653 - auth - INFO - Created new user: clientuser
2025-07-12 14:23:24,110 - auth - INFO - Created new user: notclient
2025-07-12 14:23:24,487 - auth - INFO - Created new user: concuradmin1752310404302
2025-07-12 14:23:24,921 - auth - INFO - Created new user: concuser17523104047140
2025-07-12 14:23:24,921 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot start a transaction within a transaction
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523104047141', 'Concurrent User 17523104047141', '<EMAIL>', 'annotator', '$2b$12$FZeadSGqn3sdo47gGQPem.6rtkBK1Lx.bWkOmoH2OOk/D3VRgVZBu', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 14:23:24,921 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523104047143', 'Concurrent User 17523104047143', '<EMAIL>', 'annotator', '$2b$12$lGIZ1sNfJc5DvHtM3MMTN.gRgCVmS0XRYQNGUoOZY9T0ANqG3.pFW', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:23:24,921 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523104047142', 'Concurrent User 17523104047142', '<EMAIL>', 'annotator', '$2b$12$rJrsA0z2ff3wT81ZqBA3..Y1p7wDCMF.pTy/HK4fJzaaLt3g8aDLm', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:23:24,921 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523104047144', 'Concurrent User 17523104047144', '<EMAIL>', 'annotator', '$2b$12$4ViJYY52Mo/cF7ELUCpTPugtxyLtD8De/w/7tFhZ66lzyptQlv9nO', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:23:25,125 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.username
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752310404925', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$HuU62KvGmVU8ordlD57v/ugELQJ/QR2Pe9S//9Y/G5LHcXwZAt2W2', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 14:23:25,125 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752310404925', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$LL.Yli3yVlIAJCn1FNC3x.A/ErzVRy0akHQRy/kJIKfMAuGcU0e1O', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:23:25,125 - auth - INFO - Created new user: duplicateuser1752310404925
2025-07-12 14:23:25,322 - auth - INFO - Created new user: annotator_conc2
2025-07-12 14:23:26,172 - auth - INFO - Created new user: fileadmin2
2025-07-12 14:23:26,379 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:23:26,379 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:23:26,379 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:23:26,379 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:23:27,165 - auth - INFO - Created new user: wpuser
2025-07-12 14:23:27,682 - auth - INFO - Created new user: ehadmin
2025-07-12 14:23:28,224 - auth - INFO - Created new user: ehadmin
2025-07-12 14:23:28,748 - auth - INFO - Created new user: ehannotator
2025-07-12 14:23:29,289 - auth - INFO - Created new user: ehannotator
2025-07-12 14:23:29,834 - auth - INFO - Created new user: ehannotator
2025-07-12 14:23:30,272 - auth - INFO - Created new user: largeuser
2025-07-12 14:23:30,471 - auth - INFO - Created new user: concurrentuser
2025-07-12 14:23:31,989 - auth - INFO - Created new user: kbadmin
2025-07-12 14:23:32,521 - auth - INFO - Created new user: kbaudit
2025-07-12 14:23:33,537 - auth - INFO - Created new user: perfuser
2025-07-12 14:23:35,298 - auth - INFO - Created new user: auser
2025-07-12 14:23:35,890 - auth - INFO - Created new user: secureuser
2025-07-12 14:23:36,636 - auth - INFO - Created new user: encuser
2025-07-12 14:23:36,966 - auth - INFO - Created new user: loginuser
2025-07-12 14:23:37,499 - auth - INFO - Created new user: meuser
2025-07-12 14:23:38,028 - auth - INFO - Created new user: refreshuser
2025-07-12 14:23:38,536 - auth - INFO - Created new user: changepassuser
2025-07-12 14:23:39,083 - auth - INFO - Updated password for user: changepassuser
2025-07-12 14:23:39,763 - auth - INFO - Created new user: changepassuser2
2025-07-12 14:23:40,435 - auth - INFO - Created new user: logoutuser
2025-07-12 14:23:40,939 - auth - INFO - Created new user: verifyuser
2025-07-12 14:23:41,481 - auth - INFO - Created new user: annuser
2025-07-12 14:23:41,859 - auth - INFO - Created new user: adminuser
2025-07-12 14:23:42,236 - auth - INFO - Created new user: dupuser
2025-07-12 14:23:42,547 - auth - INFO - Created new user: cookieattruser
2025-07-12 14:23:43,042 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 14:23:43,550 - auth - INFO - Created new user: refreshflowuser
2025-07-12 14:23:44,067 - auth - INFO - Created new user: schemachangepass
2025-07-12 14:23:44,589 - auth - INFO - Created new user: tamperuser
2025-07-12 14:23:45,093 - auth - INFO - Created new user: auduser
2025-07-12 14:23:45,466 - auth - INFO - Created new user: realannotator
2025-07-12 14:23:45,981 - auth - INFO - Created new user: annforaud
2025-07-12 14:23:46,345 - auth - INFO - Created new user: trueauditor
2025-07-12 14:23:50,951 - auth - INFO - Created new user: admin
2025-07-12 14:23:51,328 - auth - INFO - Created new user: supuser
2025-07-12 14:23:55,880 - auth - INFO - Created new user: admin
2025-07-12 14:23:56,237 - auth - INFO - Created new user: supuser
2025-07-12 14:24:00,836 - auth - INFO - Created new user: admin
2025-07-12 14:24:01,220 - auth - INFO - Created new user: supuser
2025-07-12 14:24:05,782 - auth - INFO - Created new user: admin
2025-07-12 14:24:06,140 - auth - INFO - Created new user: supuser
2025-07-12 14:24:10,711 - auth - INFO - Created new user: admin
2025-07-12 14:24:11,070 - auth - INFO - Created new user: supuser
2025-07-12 14:24:15,671 - auth - INFO - Created new user: admin
2025-07-12 14:24:16,041 - auth - INFO - Created new user: supuser
2025-07-12 14:24:20,640 - auth - INFO - Created new user: admin
2025-07-12 14:24:21,010 - auth - INFO - Created new user: supuser
2025-07-12 14:24:25,580 - auth - INFO - Created new user: admin
2025-07-12 14:24:25,949 - auth - INFO - Created new user: supuser
2025-07-12 14:24:30,564 - auth - INFO - Created new user: admin
2025-07-12 14:24:30,937 - auth - INFO - Created new user: supuser
2025-07-12 14:24:35,530 - auth - INFO - Created new user: anno_sup
2025-07-12 14:24:40,140 - auth - INFO - Created new user: admin
2025-07-12 14:24:40,500 - auth - INFO - Created new user: supuser
2025-07-12 14:24:45,079 - auth - INFO - Created new user: admin
2025-07-12 14:24:45,439 - auth - INFO - Created new user: supuser
2025-07-12 14:24:49,994 - auth - INFO - Created new user: admin
2025-07-12 14:24:50,350 - auth - INFO - Created new user: supuser
2025-07-12 14:24:54,943 - auth - INFO - Created new user: admin
2025-07-12 14:24:55,309 - auth - INFO - Created new user: supuser
2025-07-12 14:24:55,705 - auth - INFO - Created new user: synthadmin
2025-07-12 14:24:56,171 - auth - INFO - Created new user: radio_user
2025-07-12 14:24:56,588 - auth - INFO - Created new user: tgadmin
2025-07-12 14:25:00,522 - auth - INFO - Created new user: concurrentuser
2025-07-12 14:25:01,488 - auth - INFO - Created new user: authzanno
2025-07-12 14:25:01,856 - auth - INFO - Created new user: authzauditor
2025-07-12 14:25:02,239 - auth - INFO - Created new user: authzclient
2025-07-12 14:25:02,605 - auth - INFO - Created new user: authzadmin
2025-07-12 14:25:03,572 - auth - INFO - Created new user: smoketest
2025-07-12 14:39:58,517 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:03,211 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:07,768 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:08,141 - auth - INFO - Created new user: newuser
2025-07-12 14:40:12,529 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:12,891 - auth - INFO - Created new user: a
2025-07-12 14:40:13,065 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 14:40:13,251 - auth - INFO - Created new user: uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu
2025-07-12 14:40:17,642 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:22,224 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:26,785 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:35,382 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:39,957 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:44,490 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:49,098 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:53,677 - auth - INFO - Created new user: adminuser
2025-07-12 14:40:58,244 - auth - INFO - Created new user: adminuser
2025-07-12 14:41:02,803 - auth - INFO - Created new user: adminuser
2025-07-12 14:41:07,385 - auth - INFO - Created new user: adminuser
2025-07-12 14:41:11,930 - auth - INFO - Created new user: adminuser
2025-07-12 14:41:16,497 - auth - INFO - Created new user: adminuser
2025-07-12 14:41:21,045 - auth - INFO - Created new user: adminuser
2025-07-12 14:41:25,623 - auth - INFO - Created new user: adminuser
2025-07-12 14:41:30,197 - auth - INFO - Created new user: adminuser
2025-07-12 14:41:34,750 - auth - INFO - Created new user: adminuser
2025-07-12 14:41:39,344 - auth - INFO - Created new user: adminuser
2025-07-12 14:41:47,988 - auth - INFO - Created new user: anno_user
2025-07-12 14:41:52,572 - auth - INFO - Created new user: anno_user
2025-07-12 14:41:57,127 - auth - INFO - Created new user: anno_user
2025-07-12 14:42:01,859 - auth - INFO - Created new user: adminuser
2025-07-12 14:42:06,436 - auth - INFO - Created new user: adminuser
2025-07-12 14:42:06,626 - auth - INFO - Updated user: adminuser
2025-07-12 14:42:06,626 - auth - INFO - Updated user: adminuser
2025-07-12 14:42:11,029 - auth - INFO - Created new user: adminuser
2025-07-12 14:42:15,597 - auth - INFO - Created new user: adminuser
2025-07-12 14:42:20,157 - auth - INFO - Created new user: adminuser
2025-07-12 14:42:24,755 - auth - INFO - Created new user: annouser
2025-07-12 14:42:29,331 - auth - INFO - Created new user: annouser
2025-07-12 14:42:33,922 - auth - INFO - Created new user: annouser
2025-07-12 14:42:38,478 - auth - INFO - Created new user: annouser
2025-07-12 14:42:43,034 - auth - INFO - Created new user: annouser
2025-07-12 14:42:47,637 - auth - INFO - Created new user: annouser
2025-07-12 14:42:52,198 - auth - INFO - Created new user: annouser
2025-07-12 14:42:56,797 - auth - INFO - Created new user: annouser
2025-07-12 14:43:01,392 - auth - INFO - Created new user: annouser
2025-07-12 14:43:05,946 - auth - INFO - Created new user: annouser
2025-07-12 14:43:10,519 - auth - INFO - Created new user: audituser
2025-07-12 14:43:15,106 - auth - INFO - Created new user: audituser
2025-07-12 14:43:19,690 - auth - INFO - Created new user: audituser
2025-07-12 14:43:24,240 - auth - INFO - Created new user: audituser
2025-07-12 14:43:28,841 - auth - INFO - Created new user: audituser
2025-07-12 14:43:33,453 - auth - INFO - Created new user: audituser
2025-07-12 14:43:37,993 - auth - INFO - Created new user: audituser
2025-07-12 14:43:42,689 - auth - INFO - Created new user: audituser
2025-07-12 14:43:47,279 - auth - INFO - Created new user: wrongaudit
2025-07-12 14:43:52,003 - auth - INFO - Created new user: testuser
2025-07-12 14:44:05,113 - auth - INFO - Created new user: changepw
2025-07-12 14:44:05,671 - auth - INFO - Updated password for user: changepw
2025-07-12 14:44:10,308 - auth - INFO - Created new user: a
2025-07-12 14:44:14,746 - auth - INFO - Created new user: minpw
2025-07-12 14:44:19,139 - auth - INFO - Created new user: logoutuser
2025-07-12 14:44:32,114 - auth - INFO - Created new user: cpuser
2025-07-12 14:44:36,944 - auth - INFO - Created new user: du
2025-07-12 14:44:37,316 - auth - INFO - Created new user: deluser
2025-07-12 14:44:41,894 - auth - INFO - Created new user: clientuser
2025-07-12 14:44:46,481 - auth - INFO - Created new user: clientuser
2025-07-12 14:44:50,948 - auth - INFO - Created new user: notclient
2025-07-12 14:44:51,343 - auth - INFO - Created new user: concuradmin1752311691138
2025-07-12 14:44:51,802 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:44:51,802 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot start a transaction within a transaction
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523116915834', 'Concurrent User 17523116915834', '<EMAIL>', 'annotator', '$2b$12$2eT3s92jUWtEaLE63PtZ..KjTKYsX2KDapQS5i2r3LKPXMk9UY1Ya', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 14:44:51,804 - auth - ERROR - Database error in create_user: (sqlite3.OperationalError) cannot rollback - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 14:44:51,804 - auth - ERROR - Database error in create_user: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('concuser17523116915832', 'Concurrent User 17523116915832', '<EMAIL>', 'annotator', '$2b$12$TBC3rgGnYZw64TMiJ6XXPe.hUgPGSsJglTSpXgfw9elGjHqb30VYi', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:44:51,804 - auth - INFO - Created new user: concuser17523116915831
2025-07-12 14:44:52,012 - auth - ERROR - Database error in create_user: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752311691809', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$cjxpvBcHMWw9iNDWAjs/u.haLBKEGGktoHAxvDkKtZPvTFmgNK3Si', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-12 14:44:52,012 - auth - ERROR - Database error in create_user: (sqlite3.DatabaseError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, full_name, email, role, password_hash, created_at, last_login, is_active, annotator_mode) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?) RETURNING id, created_at]
[parameters: ('duplicateuser1752311691809', 'Duplicate User', '<EMAIL>', 'annotator', '$2b$12$0wngGJX9zvnDNyGHSEwUSOFtW74q57MPcW80U97BWDUGScaNPvuYO', None, 1, 'annotation')]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-12 14:44:52,012 - auth - INFO - Created new user: duplicateuser1752311691809
2025-07-12 14:44:52,206 - auth - INFO - Created new user: annotator_conc2
2025-07-12 14:44:53,074 - auth - INFO - Created new user: fileadmin2
2025-07-12 14:44:53,285 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:44:53,285 - auth - ERROR - Database error in get_user_by_username: (sqlite3.InterfaceError) Cursor needed to be reset because of commit/rollback and can no longer be fetched from.
(Background on this error at: https://sqlalche.me/e/20/rvf5)
2025-07-12 14:44:53,602 - auth - INFO - Created new user: wpuser
2025-07-12 14:44:54,221 - auth - INFO - Created new user: ehadmin
2025-07-12 14:44:54,722 - auth - INFO - Created new user: ehadmin
2025-07-12 14:44:55,229 - auth - INFO - Created new user: ehannotator
2025-07-12 14:44:55,735 - auth - INFO - Created new user: ehannotator
2025-07-12 14:44:56,288 - auth - INFO - Created new user: ehannotator
2025-07-12 14:44:56,703 - auth - INFO - Created new user: largeuser
2025-07-12 14:44:56,892 - auth - INFO - Created new user: concurrentuser
2025-07-12 14:44:58,449 - auth - INFO - Created new user: kbadmin
2025-07-12 14:44:58,980 - auth - INFO - Created new user: kbaudit
2025-07-12 14:45:00,199 - auth - INFO - Created new user: perfuser
2025-07-12 14:45:01,983 - auth - INFO - Created new user: auser
2025-07-12 14:45:02,547 - auth - INFO - Created new user: secureuser
2025-07-12 14:45:03,282 - auth - INFO - Created new user: encuser
2025-07-12 14:45:03,605 - auth - INFO - Created new user: loginuser
2025-07-12 14:45:04,161 - auth - INFO - Created new user: meuser
2025-07-12 14:45:04,773 - auth - INFO - Created new user: refreshuser
2025-07-12 14:45:05,330 - auth - INFO - Created new user: changepassuser
2025-07-12 14:45:05,910 - auth - INFO - Updated password for user: changepassuser
2025-07-12 14:45:06,612 - auth - INFO - Created new user: changepassuser2
2025-07-12 14:45:07,354 - auth - INFO - Created new user: logoutuser
2025-07-12 14:45:07,878 - auth - INFO - Created new user: verifyuser
2025-07-12 14:45:08,459 - auth - INFO - Created new user: annuser
2025-07-12 14:45:08,841 - auth - INFO - Created new user: adminuser
2025-07-12 14:45:09,243 - auth - INFO - Created new user: dupuser
2025-07-12 14:45:09,579 - auth - INFO - Created new user: cookieattruser
2025-07-12 14:45:10,112 - auth - INFO - Created new user: wrongtypeuser
2025-07-12 14:45:10,650 - auth - INFO - Created new user: refreshflowuser
2025-07-12 14:45:11,176 - auth - INFO - Created new user: schemachangepass
2025-07-12 14:45:11,722 - auth - INFO - Created new user: tamperuser
2025-07-12 14:45:12,237 - auth - INFO - Created new user: auduser
2025-07-12 14:45:12,622 - auth - INFO - Created new user: realannotator
2025-07-12 14:45:13,154 - auth - INFO - Created new user: annforaud
2025-07-12 14:45:13,527 - auth - INFO - Created new user: trueauditor
2025-07-12 14:45:18,128 - auth - INFO - Created new user: admin
2025-07-12 14:45:18,490 - auth - INFO - Created new user: supuser
2025-07-12 14:45:23,081 - auth - INFO - Created new user: admin
2025-07-12 14:45:23,456 - auth - INFO - Created new user: supuser
2025-07-12 14:45:28,077 - auth - INFO - Created new user: admin
2025-07-12 14:45:28,450 - auth - INFO - Created new user: supuser
2025-07-12 14:45:33,016 - auth - INFO - Created new user: admin
2025-07-12 14:45:33,397 - auth - INFO - Created new user: supuser
2025-07-12 14:45:38,023 - auth - INFO - Created new user: admin
2025-07-12 14:45:38,400 - auth - INFO - Created new user: supuser
2025-07-12 14:45:43,002 - auth - INFO - Created new user: admin
2025-07-12 14:45:43,375 - auth - INFO - Created new user: supuser
2025-07-12 14:45:47,974 - auth - INFO - Created new user: admin
2025-07-12 14:45:48,367 - auth - INFO - Created new user: supuser
2025-07-12 14:45:53,019 - auth - INFO - Created new user: admin
2025-07-12 14:45:53,412 - auth - INFO - Created new user: supuser
2025-07-12 14:45:58,033 - auth - INFO - Created new user: admin
2025-07-12 14:45:58,426 - auth - INFO - Created new user: supuser
2025-07-12 14:46:03,061 - auth - INFO - Created new user: anno_sup
2025-07-12 14:46:07,685 - auth - INFO - Created new user: admin
2025-07-12 14:46:08,060 - auth - INFO - Created new user: supuser
2025-07-12 14:46:12,676 - auth - INFO - Created new user: admin
2025-07-12 14:46:13,053 - auth - INFO - Created new user: supuser
2025-07-12 14:46:17,693 - auth - INFO - Created new user: admin
2025-07-12 14:46:18,064 - auth - INFO - Created new user: supuser
2025-07-12 14:46:22,642 - auth - INFO - Created new user: admin
2025-07-12 14:46:23,004 - auth - INFO - Created new user: supuser
2025-07-12 14:46:23,427 - auth - INFO - Created new user: synthadmin
2025-07-12 14:46:23,943 - auth - INFO - Created new user: radio_user
2025-07-12 14:46:24,357 - auth - INFO - Created new user: tgadmin
2025-07-12 14:46:28,091 - auth - INFO - Created new user: concurrentuser
2025-07-12 14:46:29,023 - auth - INFO - Created new user: authzanno
2025-07-12 14:46:29,415 - auth - INFO - Created new user: authzauditor
2025-07-12 14:46:29,790 - auth - INFO - Created new user: authzclient
2025-07-12 14:46:30,177 - auth - INFO - Created new user: authzadmin
2025-07-12 14:46:31,142 - auth - INFO - Created new user: smoketest
2025-07-12 19:06:23,799 - auth - INFO - Created new user: testuser_1752327383575
2025-07-12 19:18:18,150 - auth - INFO - Created new user: testuser_1752328097952
2025-07-12 19:21:12,818 - auth - INFO - Created new user: testuser_1752328272632
2025-07-14 10:46:17,927 - auth - INFO - Created new user: testuser_1752470177730
2025-07-14 10:46:19,130 - auth - INFO - Updated password for user: admin
2025-07-14 10:51:04,439 - auth - INFO - Created new user: testuser_1752470464253
2025-07-14 10:51:05,626 - auth - INFO - Updated password for user: admin
2025-07-14 10:51:09,136 - auth - INFO - Created new user: testuser_1752470468945
2025-07-14 11:04:46,090 - auth - INFO - Created new user: testuser_1752471285899
2025-07-14 11:04:47,326 - auth - INFO - Updated password for user: admin
2025-07-14 11:04:50,621 - auth - INFO - Created new user: testuser_1752471290437
2025-07-14 11:41:06,443 - auth - INFO - Created new user: testuser_1752473466257
2025-07-14 11:41:07,631 - auth - INFO - Updated password for user: admin
2025-07-14 11:41:10,856 - auth - INFO - Created new user: testuser_1752473470663
2025-07-14 11:43:20,090 - auth - INFO - Created new user: testuser_1752473599708
2025-07-14 11:43:22,060 - auth - INFO - Updated password for user: admin
2025-07-14 11:43:25,591 - auth - INFO - Created new user: testuser_1752473605375
2025-07-14 12:07:15,318 - auth - INFO - Created new user: testuser_1752475034924
2025-07-14 12:07:16,873 - auth - INFO - Updated password for user: admin
2025-07-14 12:07:20,314 - auth - INFO - Created new user: testuser_1752475040110
2025-07-14 12:10:58,263 - auth - INFO - Created new user: testuser_1752475257875
2025-07-14 12:10:59,633 - auth - INFO - Updated password for user: admin
2025-07-14 12:11:02,932 - auth - INFO - Created new user: testuser_1752475262746
2025-07-14 12:15:00,719 - auth - INFO - Created new user: testuser_1752475500316
2025-07-14 12:15:02,115 - auth - INFO - Updated password for user: admin
2025-07-14 12:15:05,429 - auth - INFO - Created new user: testuser_1752475505229
2025-07-14 12:21:46,132 - auth - INFO - Created new user: testuser_1752475905938
2025-07-14 12:21:48,085 - auth - INFO - Updated password for user: admin
2025-07-14 12:21:48,308 - auth - ERROR - Database error in get_user_by_username: Object <User at 0x137f780b7f0> cannot be converted to 'persistent' state, as this identity map is no longer valid.  Has the owning Session been closed? (Background on this error at: https://sqlalche.me/e/20/lkrp)
2025-07-14 12:21:51,399 - auth - INFO - Created new user: testuser_1752475911207
2025-07-14 12:24:17,283 - auth - INFO - Created new user: testuser_1752476056880
2025-07-14 12:24:18,595 - auth - INFO - Updated password for user: admin
2025-07-14 12:24:21,923 - auth - INFO - Created new user: testuser_1752476061719
2025-07-14 12:26:51,782 - auth - INFO - Created new user: testuser_1752476211588
2025-07-14 12:26:53,275 - auth - INFO - Updated password for user: admin
2025-07-14 12:26:53,877 - auth - ERROR - Database error in get_user_by_username: Object <User at 0x137f79d2230> cannot be converted to 'persistent' state, as this identity map is no longer valid.  Has the owning Session been closed? (Background on this error at: https://sqlalche.me/e/20/lkrp)
2025-07-14 12:26:55,211 - auth - ERROR - Database error in update_last_login: Instance '<User at 0x137f79fe650>' is not persistent within this Session
2025-07-14 12:26:56,504 - auth - INFO - Created new user: testuser_1752476216311
2025-07-14 12:29:52,868 - auth - INFO - Created new user: testuser_1752476392670
2025-07-14 12:29:54,102 - auth - INFO - Updated password for user: admin
2025-07-14 12:29:57,308 - auth - INFO - Created new user: testuser_1752476397119
2025-07-14 12:38:07,630 - auth - INFO - Created new user: testuser_1752476887248
2025-07-14 12:38:07,814 - auth - INFO - Created new user: testuser_1752476887434
2025-07-14 12:38:09,101 - auth - INFO - Created new user: testuser_1752476888920
2025-07-14 12:38:09,296 - auth - INFO - Created new user: testuser_1752476888921
2025-07-14 12:38:09,478 - auth - INFO - Created new user: testuser_1752476888907
2025-07-14 12:38:09,668 - auth - INFO - Created new user: testuser_1752476888925
2025-07-14 12:38:09,845 - auth - INFO - Created new user: testuser_1752476888923
2025-07-14 12:38:10,035 - auth - INFO - Created new user: testuser_1752476888924
2025-07-14 12:38:10,063 - auth - ERROR - Database error in get_user_by_username: Object <User at 0x137f7a66380> cannot be converted to 'persistent' state, as this identity map is no longer valid.  Has the owning Session been closed? (Background on this error at: https://sqlalche.me/e/20/lkrp)
2025-07-14 12:38:12,918 - auth - ERROR - Database error in update_last_login: Instance '<User at 0x137f7aa25f0>' is not persistent within this Session
2025-07-14 12:38:15,486 - auth - INFO - Updated password for user: admin
2025-07-14 12:38:16,230 - auth - INFO - Updated password for user: admin
2025-07-14 12:38:17,154 - auth - INFO - Updated password for user: admin
2025-07-14 12:38:17,927 - auth - INFO - Updated password for user: admin
2025-07-14 12:38:18,509 - auth - INFO - Updated password for user: admin
2025-07-14 12:38:18,875 - auth - INFO - Updated password for user: admin
2025-07-14 12:38:19,243 - auth - INFO - Updated password for user: admin
2025-07-14 12:38:19,611 - auth - INFO - Updated password for user: admin
2025-07-14 12:38:23,936 - auth - ERROR - Database error in update_last_login: Instance '<User at 0x137f7a9ece0>' is not persistent within this Session
2025-07-14 12:38:31,089 - auth - INFO - Created new user: testuser_1752476910334
2025-07-14 12:38:32,235 - auth - INFO - Created new user: testuser_1752476910898
2025-07-14 12:38:42,413 - auth - INFO - Created new user: testuser_1752476915031
2025-07-14 12:38:42,635 - auth - INFO - Created new user: testuser_1752476916666
2025-07-14 12:38:42,822 - auth - INFO - Created new user: testuser_1752476917250
2025-07-14 12:38:43,013 - auth - INFO - Created new user: testuser_1752476917078
2025-07-14 12:38:43,202 - auth - INFO - Created new user: testuser_1752476917263
2025-07-14 12:38:44,907 - auth - INFO - Created new user: testuser_1752476921543
2025-07-14 12:50:08,964 - auth - INFO - Created new user: testuser_1752477608549
2025-07-14 12:50:10,330 - auth - INFO - Created new user: testuser_1752477608765
2025-07-14 12:50:10,546 - auth - INFO - Created new user: testuser_1752477610336
2025-07-14 12:50:10,731 - auth - INFO - Created new user: testuser_1752477610327
2025-07-14 12:50:10,912 - auth - INFO - Created new user: testuser_1752477610334
2025-07-14 12:50:11,118 - auth - INFO - Created new user: testuser_1752477610333
2025-07-14 12:50:11,303 - auth - INFO - Created new user: testuser_1752477610341
2025-07-14 12:50:11,353 - auth - ERROR - Database error in get_user_by_username: Object <User at 0x137f7b3e950> cannot be converted to 'persistent' state, as this identity map is no longer valid.  Has the owning Session been closed? (Background on this error at: https://sqlalche.me/e/20/lkrp)
2025-07-14 12:50:16,879 - auth - INFO - Updated password for user: admin
2025-07-14 12:50:18,007 - auth - INFO - Updated password for user: admin
2025-07-14 12:50:18,552 - auth - INFO - Updated password for user: admin
2025-07-14 12:50:19,103 - auth - INFO - Updated password for user: admin
2025-07-14 12:50:19,693 - auth - INFO - Updated password for user: admin
2025-07-14 12:50:20,263 - auth - INFO - Updated password for user: admin
2025-07-14 12:50:20,645 - auth - INFO - Updated password for user: admin
2025-07-14 12:50:21,411 - auth - INFO - Updated password for user: admin
2025-07-14 12:50:25,338 - auth - ERROR - Database error in get_user_by_username: Object <User at 0x137f7ad3c40> cannot be converted to 'persistent' state, as this identity map is no longer valid.  Has the owning Session been closed? (Background on this error at: https://sqlalche.me/e/20/lkrp)
2025-07-14 12:50:31,987 - auth - INFO - Created new user: testuser_1752477631029
2025-07-14 12:50:38,158 - auth - INFO - Created new user: testuser_1752477635661
2025-07-14 12:50:38,352 - auth - INFO - Created new user: testuser_1752477635663
2025-07-14 12:50:43,597 - auth - INFO - Created new user: testuser_1752477636030
2025-07-14 12:50:43,780 - auth - INFO - Created new user: testuser_1752477636438
2025-07-14 12:50:43,971 - auth - INFO - Created new user: testuser_1752477636422
2025-07-14 12:50:44,161 - auth - INFO - Created new user: testuser_1752477636421
2025-07-14 12:50:45,080 - auth - INFO - Created new user: testuser_1752477640727
2025-07-14 13:01:46,707 - auth - INFO - Created new user: testuser_1752478306498
2025-07-14 13:01:48,203 - auth - INFO - Created new user: testuser_1752478308010
2025-07-14 13:01:48,384 - auth - INFO - Created new user: testuser_1752478308011
2025-07-14 13:01:48,572 - auth - INFO - Created new user: testuser_1752478308014
2025-07-14 13:01:48,759 - auth - INFO - Created new user: testuser_1752478308015
2025-07-14 13:01:48,930 - auth - INFO - Created new user: testuser_1752478308013
2025-07-14 13:01:49,138 - auth - INFO - Created new user: testuser_1752478308003
2025-07-14 13:01:49,321 - auth - INFO - Created new user: testuser_1752478308019
2025-07-14 13:01:54,567 - auth - INFO - Updated password for user: admin
2025-07-14 13:01:56,071 - auth - INFO - Updated password for user: admin
2025-07-14 13:01:56,427 - auth - INFO - Updated password for user: admin
2025-07-14 13:01:56,796 - auth - INFO - Updated password for user: admin
2025-07-14 13:01:57,171 - auth - INFO - Updated password for user: admin
2025-07-14 13:01:57,930 - auth - INFO - Updated password for user: admin
2025-07-14 13:01:59,042 - auth - ERROR - Database error in update_user_password: Instance '<User at 0x137f7a65b10>' is not persistent within this Session
2025-07-14 13:01:59,399 - auth - INFO - Updated password for user: admin
2025-07-14 13:02:03,413 - auth - ERROR - Database error in update_last_login: Instance '<User at 0x137f7ca7f10>' is not persistent within this Session
2025-07-14 13:02:03,599 - auth - ERROR - Database error in update_last_login: Instance '<User at 0x137f7ca70d0>' is not persistent within this Session
2025-07-14 13:02:05,312 - auth - ERROR - Database error in update_last_login: Instance '<User at 0x137f7c2ee90>' is not persistent within this Session
2025-07-14 13:04:16,538 - auth - INFO - Created new user: testuser_1752478456152
2025-07-14 13:04:16,735 - auth - INFO - Created new user: testuser_1752478456166
2025-07-14 13:04:16,934 - auth - INFO - Created new user: testuser_1752478456353
2025-07-14 13:04:17,905 - auth - INFO - Created new user: testuser_1752478457703
2025-07-14 13:04:18,088 - auth - INFO - Created new user: testuser_1752478457692
2025-07-14 13:04:18,288 - auth - INFO - Created new user: testuser_1752478457701
2025-07-14 13:04:18,471 - auth - INFO - Created new user: testuser_1752478457700
2025-07-14 13:04:23,591 - auth - INFO - Updated password for user: admin
2025-07-14 13:04:23,966 - auth - INFO - Updated password for user: admin
2025-07-14 13:04:24,535 - auth - INFO - Updated password for user: admin
2025-07-14 13:04:25,482 - auth - INFO - Updated password for user: admin
2025-07-14 13:04:26,422 - auth - INFO - Updated password for user: admin
2025-07-14 13:04:26,788 - auth - INFO - Updated password for user: admin
2025-07-14 13:04:27,454 - auth - INFO - Updated password for user: admin
2025-07-14 13:09:53,594 - auth - INFO - Created new user: testuser_1752478793200
2025-07-14 13:09:54,735 - auth - INFO - Created new user: testuser_1752478793397
2025-07-14 13:09:54,937 - auth - INFO - Created new user: testuser_1752478794746
2025-07-14 13:09:55,136 - auth - INFO - Created new user: testuser_1752478794749
2025-07-14 13:09:55,315 - auth - INFO - Created new user: testuser_1752478794747
2025-07-14 13:09:55,499 - auth - INFO - Created new user: testuser_1752478794751
2025-07-14 13:09:55,688 - auth - INFO - Created new user: testuser_1752478794754
2025-07-14 13:10:00,581 - auth - INFO - Updated password for user: admin
2025-07-14 13:10:00,954 - auth - INFO - Updated password for user: admin
2025-07-14 13:10:02,260 - auth - INFO - Updated password for user: admin
2025-07-14 13:10:02,791 - auth - INFO - Updated password for user: admin
2025-07-14 13:10:03,347 - auth - INFO - Updated password for user: admin
2025-07-14 13:10:03,718 - auth - INFO - Updated password for user: admin
2025-07-14 13:10:04,098 - auth - INFO - Updated password for user: admin
2025-07-14 13:23:57,044 - auth - INFO - Created new user: testuser_1752479636636
2025-07-14 13:23:57,845 - auth - ERROR - Database error in get_user_by_username: Object <User at 0x137f7e25a50> cannot be converted to 'persistent' state, as this identity map is no longer valid.  Has the owning Session been closed? (Background on this error at: https://sqlalche.me/e/20/lkrp)
2025-07-14 13:23:58,234 - auth - INFO - Created new user: testuser_1752479637838
2025-07-14 13:23:58,448 - auth - INFO - Created new user: testuser_1752479637847
2025-07-14 13:23:58,640 - auth - INFO - Created new user: testuser_1752479637836
2025-07-14 13:23:58,834 - auth - INFO - Created new user: testuser_1752479637842
2025-07-14 13:23:59,046 - auth - INFO - Created new user: testuser_1752479638245
2025-07-14 13:23:59,247 - auth - INFO - Created new user: testuser_1752479638253
2025-07-14 13:23:59,280 - auth - ERROR - Database error in get_user_by_username: Object <User at 0x137f7ec1cf0> cannot be converted to 'persistent' state, as this identity map is no longer valid.  Has the owning Session been closed? (Background on this error at: https://sqlalche.me/e/20/lkrp)
2025-07-14 13:24:04,006 - auth - INFO - Updated password for user: admin
2025-07-14 13:24:05,674 - auth - INFO - Updated password for user: admin
2025-07-14 13:24:06,073 - auth - INFO - Updated password for user: admin
2025-07-14 13:24:06,674 - auth - INFO - Updated password for user: admin
2025-07-14 13:24:07,084 - auth - INFO - Updated password for user: admin
2025-07-14 13:24:07,484 - auth - INFO - Updated password for user: admin
2025-07-14 13:24:08,274 - auth - INFO - Updated password for user: admin
2025-07-14 13:43:05,825 - auth - INFO - Created new user: testuser_1752480785460
2025-07-14 13:43:06,742 - auth - INFO - Created new user: testuser_1752480785642
2025-07-14 13:43:07,151 - auth - INFO - Created new user: testuser_1752480786942
2025-07-14 13:43:07,335 - auth - INFO - Created new user: testuser_1752480786933
2025-07-14 13:43:07,520 - auth - INFO - Created new user: testuser_1752480786952
2025-07-14 13:43:07,709 - auth - INFO - Created new user: testuser_1752480786955
2025-07-14 13:43:07,887 - auth - INFO - Created new user: testuser_1752480786947
2025-07-14 13:43:11,231 - auth - ERROR - Database error in get_user_by_username: Object <User at 0x137f7e6d330> cannot be converted to 'persistent' state, as this identity map is no longer valid.  Has the owning Session been closed? (Background on this error at: https://sqlalche.me/e/20/lkrp)
2025-07-14 13:43:12,603 - auth - INFO - Updated password for user: admin
2025-07-14 13:43:13,551 - auth - INFO - Updated password for user: admin
2025-07-14 13:43:14,504 - auth - INFO - Updated password for user: admin
2025-07-14 13:43:14,868 - auth - INFO - Updated password for user: admin
2025-07-14 13:43:15,244 - auth - INFO - Updated password for user: admin
2025-07-14 13:43:15,825 - auth - INFO - Updated password for user: admin
2025-07-14 13:43:16,201 - auth - INFO - Updated password for user: admin
2025-07-14 13:49:03,302 - auth - INFO - Created new user: testuser_1752481142368
2025-07-14 13:49:04,427 - auth - INFO - Created new user: testuser_1752481143309
2025-07-14 13:49:04,624 - auth - INFO - Created new user: testuser_1752481144229
2025-07-14 13:49:04,808 - auth - INFO - Created new user: testuser_1752481144222
2025-07-14 13:49:04,984 - auth - INFO - Created new user: testuser_1752481144226
2025-07-14 13:49:05,193 - auth - INFO - Created new user: testuser_1752481144433
2025-07-14 13:49:08,786 - auth - ERROR - Database error in get_user_by_username: Object <User at 0x137f80751e0> cannot be converted to 'persistent' state, as this identity map is no longer valid.  Has the owning Session been closed? (Background on this error at: https://sqlalche.me/e/20/lkrp)
2025-07-14 13:49:10,170 - auth - INFO - Updated password for user: admin
2025-07-14 13:49:10,917 - auth - INFO - Updated password for user: admin
2025-07-14 13:49:11,673 - auth - INFO - Updated password for user: admin
2025-07-14 13:49:11,673 - auth - ERROR - Database error in get_user_by_username: Object <User at 0x137f80b8dc0> cannot be converted to 'persistent' state, as this identity map is no longer valid.  Has the owning Session been closed? (Background on this error at: https://sqlalche.me/e/20/lkrp)
2025-07-14 13:49:12,239 - auth - INFO - Updated password for user: admin
2025-07-14 13:49:12,808 - auth - INFO - Updated password for user: admin
2025-07-14 13:49:13,547 - auth - INFO - Updated password for user: admin
2025-07-14 14:02:12,331 - auth - INFO - Created new user: testuser_1752481931941
2025-07-14 14:02:13,440 - auth - INFO - Created new user: testuser_1752481932134
2025-07-14 14:02:13,657 - auth - INFO - Created new user: testuser_1752481933448
2025-07-14 14:02:13,843 - auth - INFO - Created new user: testuser_1752481933454
2025-07-14 14:02:14,028 - auth - INFO - Created new user: testuser_1752481933452
2025-07-14 14:02:14,211 - auth - INFO - Created new user: testuser_1752481933453
2025-07-14 14:02:14,394 - auth - INFO - Created new user: testuser_1752481933458
2025-07-14 14:02:15,234 - auth - ERROR - Database error in update_last_login: Instance '<User at 0x137f7fb9d80>' is not persistent within this Session
2025-07-14 14:02:19,138 - auth - INFO - Updated password for user: admin
2025-07-14 14:02:20,078 - auth - INFO - Updated password for user: admin
2025-07-14 14:02:20,629 - auth - INFO - Updated password for user: admin
2025-07-14 14:02:21,564 - auth - INFO - Updated password for user: admin
2025-07-14 14:02:22,302 - auth - INFO - Updated password for user: admin
2025-07-14 14:02:22,673 - auth - INFO - Updated password for user: admin
2025-07-14 14:02:23,422 - auth - INFO - Updated password for user: admin
