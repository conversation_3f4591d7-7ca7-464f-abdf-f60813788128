2025-07-04 12:26:51,764 - db_connector - INFO - Ensuring database directory exists
2025-07-04 12:26:51,764 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 12:26:51,786 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 12:26:51,786 - db_connector - INFO - Seeding initial user data
2025-07-04 12:26:51,797 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 12:26:51,809 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 12:31:52,264 - db_connector - INFO - Ensuring database directory exists
2025-07-04 12:31:52,264 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 12:31:52,282 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 12:31:52,282 - db_connector - INFO - Seeding initial user data
2025-07-04 12:31:52,299 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 12:31:52,303 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 12:44:05,651 - db_connector - INFO - Ensuring database directory exists
2025-07-04 12:44:05,651 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 12:44:05,656 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 12:44:05,656 - db_connector - INFO - Seeding initial user data
2025-07-04 12:44:05,676 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 12:44:05,679 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 12:46:51,162 - db_connector - INFO - Ensuring database directory exists
2025-07-04 12:46:51,162 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 12:46:51,167 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 12:46:51,168 - db_connector - INFO - Seeding initial user data
2025-07-04 12:46:51,186 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 12:46:51,187 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 12:48:46,331 - db_connector - INFO - Ensuring database directory exists
2025-07-04 12:48:46,331 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 12:48:46,336 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 12:48:46,336 - db_connector - INFO - Seeding initial user data
2025-07-04 12:48:46,352 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 12:48:46,352 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 12:53:31,724 - db_connector - INFO - Ensuring database directory exists
2025-07-04 12:53:31,725 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 12:53:31,730 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 12:53:31,730 - db_connector - INFO - Seeding initial user data
2025-07-04 12:53:31,746 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 12:53:31,746 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 13:00:10,759 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:00:10,759 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:00:10,762 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:00:10,762 - db_connector - INFO - Seeding initial user data
2025-07-04 13:00:10,781 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:00:10,785 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 13:10:02,527 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:10:02,527 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:10:02,527 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:10:02,527 - db_connector - INFO - Seeding initial user data
2025-07-04 13:10:03,497 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:10:03,514 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 13:17:13,095 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:17:13,095 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:17:13,095 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:17:13,095 - db_connector - INFO - Seeding initial user data
2025-07-04 13:17:14,027 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:17:14,037 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 13:23:11,212 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:23:11,212 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:23:11,212 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:23:11,212 - db_connector - INFO - Seeding initial user data
2025-07-04 13:23:12,141 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:23:12,159 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 13:28:08,702 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:28:08,702 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:28:08,705 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:28:08,705 - db_connector - INFO - Seeding initial user data
2025-07-04 13:28:09,637 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:28:09,653 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 13:39:16,203 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:39:16,203 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:39:16,222 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:39:16,222 - db_connector - INFO - Seeding initial user data
2025-07-04 13:39:17,160 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:39:17,175 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 13:45:38,599 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:45:38,599 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:45:38,599 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:45:38,599 - db_connector - INFO - Seeding initial user data
2025-07-04 13:45:39,566 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:45:39,582 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 13:47:34,102 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:47:34,102 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:47:34,102 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:47:34,102 - db_connector - INFO - Seeding initial user data
2025-07-04 13:47:35,080 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:47:35,080 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 13:51:22,228 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:51:22,229 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:51:22,234 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:51:22,234 - db_connector - INFO - Seeding initial user data
2025-07-04 13:51:23,190 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:51:23,202 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 13:52:33,160 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:52:33,160 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:52:33,167 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:52:33,167 - db_connector - INFO - Seeding initial user data
2025-07-04 13:52:33,187 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:52:33,191 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 13:54:27,620 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:54:27,620 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:54:27,634 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:54:27,634 - db_connector - INFO - Seeding initial user data
2025-07-04 13:54:28,630 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:54:28,638 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 13:57:49,616 - db_connector - INFO - Ensuring database directory exists
2025-07-04 13:57:49,616 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 13:57:49,633 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 13:57:49,633 - db_connector - INFO - Seeding initial user data
2025-07-04 13:57:50,619 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 13:57:50,626 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 14:07:27,068 - db_connector - INFO - Ensuring database directory exists
2025-07-04 14:07:27,068 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 14:07:27,073 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 14:07:27,073 - db_connector - INFO - Seeding initial user data
2025-07-04 14:07:28,046 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 14:07:28,049 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 14:58:30,438 - db_connector - INFO - Ensuring database directory exists
2025-07-04 14:58:30,438 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 14:58:30,453 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 14:58:30,453 - db_connector - INFO - Seeding initial user data
2025-07-04 14:58:31,387 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 14:58:31,397 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 15:16:21,214 - db_connector - INFO - Ensuring database directory exists
2025-07-04 15:16:21,215 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 15:16:21,228 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 15:16:21,228 - db_connector - INFO - Seeding initial user data
2025-07-04 15:16:22,191 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 15:16:22,199 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 16:22:45,908 - db_connector - INFO - Ensuring database directory exists
2025-07-04 16:22:45,912 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 16:22:45,954 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 16:22:45,955 - db_connector - INFO - Seeding initial user data
2025-07-04 16:22:48,449 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 16:22:48,456 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 17:48:57,883 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:48:57,883 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:48:57,902 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:48:57,902 - db_connector - INFO - Seeding initial user data
2025-07-04 17:48:58,899 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:48:58,899 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 17:49:28,720 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:49:28,720 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:49:28,724 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:49:28,724 - db_connector - INFO - Seeding initial user data
2025-07-04 17:49:29,717 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:49:29,734 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 17:51:22,420 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:51:22,420 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:51:22,427 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:51:22,427 - db_connector - INFO - Seeding initial user data
2025-07-04 17:51:22,444 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:51:22,444 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 17:51:43,139 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:51:43,139 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:51:43,145 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:51:43,145 - db_connector - INFO - Seeding initial user data
2025-07-04 17:51:43,162 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:51:43,163 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 17:51:58,497 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:51:58,497 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:51:58,503 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:51:58,503 - db_connector - INFO - Seeding initial user data
2025-07-04 17:51:58,523 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:51:58,525 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 17:52:20,793 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:52:20,793 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:52:20,800 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:52:20,800 - db_connector - INFO - Seeding initial user data
2025-07-04 17:52:20,816 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:52:20,821 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 17:52:41,297 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:52:41,297 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:52:41,303 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:52:41,303 - db_connector - INFO - Seeding initial user data
2025-07-04 17:52:41,322 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:52:41,324 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 17:52:58,799 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:52:58,799 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:52:58,808 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:52:58,808 - db_connector - INFO - Seeding initial user data
2025-07-04 17:52:58,827 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:52:58,830 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 17:53:18,818 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:53:18,818 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:53:18,821 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:53:18,821 - db_connector - INFO - Seeding initial user data
2025-07-04 17:53:18,845 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:53:18,849 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 17:53:55,358 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:53:55,358 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:53:55,362 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:53:55,362 - db_connector - INFO - Seeding initial user data
2025-07-04 17:53:56,341 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:53:56,350 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 17:54:14,612 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:54:14,612 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:54:14,620 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:54:14,620 - db_connector - INFO - Seeding initial user data
2025-07-04 17:54:14,642 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:54:14,642 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 17:54:35,003 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:54:35,003 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:54:35,011 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:54:35,011 - db_connector - INFO - Seeding initial user data
2025-07-04 17:54:35,026 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:54:35,026 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 17:56:35,825 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:56:35,826 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:56:35,830 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:56:35,832 - db_connector - INFO - Seeding initial user data
2025-07-04 17:56:36,819 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:56:36,828 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-04 17:57:31,672 - db_connector - INFO - Ensuring database directory exists
2025-07-04 17:57:31,672 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 17:57:31,686 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 17:57:31,686 - db_connector - INFO - Seeding initial user data
2025-07-04 17:57:31,707 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 17:57:31,707 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 18:03:43,580 - db_connector - INFO - Ensuring database directory exists
2025-07-04 18:03:43,584 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 18:03:43,589 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 18:03:43,589 - db_connector - INFO - Seeding initial user data
2025-07-04 18:03:43,606 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 18:03:43,606 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 18:09:51,746 - db_connector - INFO - Ensuring database directory exists
2025-07-04 18:09:51,746 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 18:09:51,756 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 18:09:51,756 - db_connector - INFO - Seeding initial user data
2025-07-04 18:09:51,769 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 18:09:51,780 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 18:10:12,746 - db_connector - INFO - Ensuring database directory exists
2025-07-04 18:10:12,746 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 18:10:12,752 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 18:10:12,752 - db_connector - INFO - Seeding initial user data
2025-07-04 18:10:12,773 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 18:10:12,775 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 18:11:32,933 - db_connector - INFO - Ensuring database directory exists
2025-07-04 18:11:32,933 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 18:11:32,935 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 18:11:32,935 - db_connector - INFO - Seeding initial user data
2025-07-04 18:11:32,952 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 18:11:32,968 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 18:14:03,213 - db_connector - INFO - Ensuring database directory exists
2025-07-04 18:14:03,213 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 18:14:03,217 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 18:14:03,217 - db_connector - INFO - Seeding initial user data
2025-07-04 18:14:03,224 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 18:14:03,235 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-04 18:14:57,560 - db_connector - INFO - Ensuring database directory exists
2025-07-04 18:14:57,560 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-04 18:14:57,560 - db_connector - INFO - Successfully initialized database with all models
2025-07-04 18:14:57,560 - db_connector - INFO - Seeding initial user data
2025-07-04 18:14:57,560 - db_connector - INFO - Initial user data seeded successfully
2025-07-04 18:14:57,560 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 10:17:43,694 - db_connector - INFO - Ensuring database directory exists
2025-07-05 10:17:43,694 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 10:17:43,701 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 10:17:43,701 - db_connector - INFO - Seeding initial user data
2025-07-05 10:17:43,703 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 10:17:43,703 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 10:23:44,471 - db_connector - INFO - Ensuring database directory exists
2025-07-05 10:23:44,471 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 10:23:44,471 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 10:23:44,471 - db_connector - INFO - Seeding initial user data
2025-07-05 10:23:44,486 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 10:23:44,490 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 10:36:01,604 - db_connector - INFO - Ensuring database directory exists
2025-07-05 10:36:01,610 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 10:36:01,619 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 10:36:01,619 - db_connector - INFO - Seeding initial user data
2025-07-05 10:36:01,637 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 10:36:01,639 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 10:37:50,384 - db_connector - INFO - Ensuring database directory exists
2025-07-05 10:37:50,385 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 10:37:50,394 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 10:37:50,394 - db_connector - INFO - Seeding initial user data
2025-07-05 10:37:50,411 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 10:37:50,415 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 11:59:40,361 - db_connector - INFO - Ensuring database directory exists
2025-07-05 11:59:40,361 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 11:59:40,377 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 11:59:40,377 - db_connector - INFO - Seeding initial user data
2025-07-05 11:59:40,401 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 11:59:40,403 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 12:13:05,854 - db_connector - INFO - Ensuring database directory exists
2025-07-05 12:13:05,854 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 12:13:05,865 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 12:13:05,865 - db_connector - INFO - Seeding initial user data
2025-07-05 12:13:06,800 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 12:13:06,816 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 12:24:40,645 - db_connector - INFO - Ensuring database directory exists
2025-07-05 12:24:40,645 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 12:24:40,652 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 12:24:40,653 - db_connector - INFO - Seeding initial user data
2025-07-05 12:24:41,695 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 12:24:41,698 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 14:13:22,225 - db_connector - INFO - Ensuring database directory exists
2025-07-05 14:13:22,225 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 14:13:22,243 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 14:13:22,244 - db_connector - INFO - Seeding initial user data
2025-07-05 14:13:23,234 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 14:13:23,251 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 14:37:37,104 - db_connector - INFO - Ensuring database directory exists
2025-07-05 14:37:37,105 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 14:37:37,110 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 14:37:37,110 - db_connector - INFO - Seeding initial user data
2025-07-05 14:37:38,085 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 14:37:38,094 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 14:55:31,606 - db_connector - INFO - Ensuring database directory exists
2025-07-05 14:55:31,606 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 14:55:31,615 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 14:55:31,615 - db_connector - INFO - Seeding initial user data
2025-07-05 14:55:32,589 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 14:55:32,605 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 15:00:27,406 - db_connector - INFO - Ensuring database directory exists
2025-07-05 15:00:27,406 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 15:00:27,406 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 15:00:27,406 - db_connector - INFO - Seeding initial user data
2025-07-05 15:00:28,403 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 15:00:28,414 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 15:01:36,055 - db_connector - INFO - Ensuring database directory exists
2025-07-05 15:01:36,056 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 15:01:36,061 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 15:01:36,061 - db_connector - INFO - Seeding initial user data
2025-07-05 15:01:37,039 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 15:01:37,045 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 15:05:35,142 - db_connector - INFO - Ensuring database directory exists
2025-07-05 15:05:35,142 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 15:05:35,142 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 15:05:35,142 - db_connector - INFO - Seeding initial user data
2025-07-05 15:05:36,172 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 15:05:36,182 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 15:07:34,972 - db_connector - INFO - Ensuring database directory exists
2025-07-05 15:07:34,973 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 15:07:34,977 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 15:07:34,978 - db_connector - INFO - Seeding initial user data
2025-07-05 15:07:35,905 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 15:07:35,909 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 15:07:50,263 - db_connector - INFO - Ensuring database directory exists
2025-07-05 15:07:50,263 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 15:07:50,271 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 15:07:50,271 - db_connector - INFO - Seeding initial user data
2025-07-05 15:07:51,261 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 15:07:51,272 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 15:12:06,644 - db_connector - INFO - Ensuring database directory exists
2025-07-05 15:12:06,645 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 15:12:06,646 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 15:12:06,646 - db_connector - INFO - Seeding initial user data
2025-07-05 15:12:07,675 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 15:12:07,682 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 15:17:24,683 - db_connector - INFO - Ensuring database directory exists
2025-07-05 15:17:24,684 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 15:17:24,690 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 15:17:24,690 - db_connector - INFO - Seeding initial user data
2025-07-05 15:17:25,637 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 15:17:25,653 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 16:09:38,508 - db_connector - INFO - Ensuring database directory exists
2025-07-05 16:09:38,508 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 16:09:38,519 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 16:09:38,519 - db_connector - INFO - Seeding initial user data
2025-07-05 16:09:39,526 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 16:09:39,542 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 16:13:03,470 - db_connector - INFO - Ensuring database directory exists
2025-07-05 16:13:03,470 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 16:13:03,489 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 16:13:03,489 - db_connector - INFO - Seeding initial user data
2025-07-05 16:13:03,508 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 16:13:03,508 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 16:16:14,641 - db_connector - INFO - Ensuring database directory exists
2025-07-05 16:16:14,641 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 16:16:14,653 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 16:16:14,653 - db_connector - INFO - Seeding initial user data
2025-07-05 16:16:14,673 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 16:16:14,677 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 16:19:21,614 - db_connector - INFO - Ensuring database directory exists
2025-07-05 16:19:21,615 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 16:19:21,623 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 16:19:21,623 - db_connector - INFO - Seeding initial user data
2025-07-05 16:19:22,647 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 16:19:22,655 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 16:19:48,511 - db_connector - INFO - Ensuring database directory exists
2025-07-05 16:19:48,511 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 16:19:48,519 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 16:19:48,519 - db_connector - INFO - Seeding initial user data
2025-07-05 16:19:49,452 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 16:19:49,489 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 16:24:13,193 - db_connector - INFO - Ensuring database directory exists
2025-07-05 16:24:13,193 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 16:24:13,210 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 16:24:13,210 - db_connector - INFO - Seeding initial user data
2025-07-05 16:24:14,131 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 16:24:14,147 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 16:25:09,511 - db_connector - INFO - Ensuring database directory exists
2025-07-05 16:25:09,512 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 16:25:09,517 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 16:25:09,517 - db_connector - INFO - Seeding initial user data
2025-07-05 16:25:10,677 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 16:25:10,686 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 16:32:28,210 - db_connector - INFO - Ensuring database directory exists
2025-07-05 16:32:28,210 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 16:32:28,221 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 16:32:28,222 - db_connector - INFO - Seeding initial user data
2025-07-05 16:32:29,230 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 16:32:29,240 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 16:43:07,860 - db_connector - INFO - Ensuring database directory exists
2025-07-05 16:43:07,861 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 16:43:07,867 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 16:43:07,868 - db_connector - INFO - Seeding initial user data
2025-07-05 16:43:08,873 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 16:43:08,877 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 16:49:17,883 - db_connector - INFO - Ensuring database directory exists
2025-07-05 16:49:17,883 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 16:49:17,890 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 16:49:17,890 - db_connector - INFO - Seeding initial user data
2025-07-05 16:49:18,820 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 16:49:18,831 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 17:08:44,025 - db_connector - INFO - Ensuring database directory exists
2025-07-05 17:08:44,025 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 17:08:44,045 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 17:08:44,045 - db_connector - INFO - Seeding initial user data
2025-07-05 17:08:45,030 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 17:08:45,038 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 17:10:20,229 - db_connector - INFO - Ensuring database directory exists
2025-07-05 17:10:20,229 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 17:10:20,237 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 17:10:20,237 - db_connector - INFO - Seeding initial user data
2025-07-05 17:10:21,220 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 17:10:21,228 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 17:13:21,441 - db_connector - INFO - Ensuring database directory exists
2025-07-05 17:13:21,442 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 17:13:21,447 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 17:13:21,447 - db_connector - INFO - Seeding initial user data
2025-07-05 17:13:22,368 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 17:13:22,384 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 17:15:41,542 - db_connector - INFO - Ensuring database directory exists
2025-07-05 17:15:41,542 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 17:15:41,542 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 17:15:41,542 - db_connector - INFO - Seeding initial user data
2025-07-05 17:15:42,499 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 17:15:42,515 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 17:18:20,681 - db_connector - INFO - Ensuring database directory exists
2025-07-05 17:18:20,681 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 17:18:20,687 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 17:18:20,687 - db_connector - INFO - Seeding initial user data
2025-07-05 17:18:21,787 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 17:18:21,803 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 18:22:05,427 - db_connector - INFO - Ensuring database directory exists
2025-07-05 18:22:05,427 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 18:22:05,442 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 18:22:05,442 - db_connector - INFO - Seeding initial user data
2025-07-05 18:22:06,424 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 18:22:06,434 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-05 18:26:37,036 - db_connector - INFO - Ensuring database directory exists
2025-07-05 18:26:37,036 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 18:26:37,046 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 18:26:37,046 - db_connector - INFO - Seeding initial user data
2025-07-05 18:26:37,064 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 18:26:37,067 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 18:32:22,174 - db_connector - INFO - Ensuring database directory exists
2025-07-05 18:32:22,174 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 18:32:22,174 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 18:32:22,174 - db_connector - INFO - Seeding initial user data
2025-07-05 18:32:22,205 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 18:32:22,210 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 18:36:25,039 - db_connector - INFO - Ensuring database directory exists
2025-07-05 18:36:25,039 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 18:36:25,039 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 18:36:25,039 - db_connector - INFO - Seeding initial user data
2025-07-05 18:36:25,056 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 18:36:25,073 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 18:49:06,842 - db_connector - INFO - Ensuring database directory exists
2025-07-05 18:49:06,843 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 18:49:06,846 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 18:49:06,846 - db_connector - INFO - Seeding initial user data
2025-07-05 18:49:06,873 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 18:49:06,878 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 18:50:34,147 - db_connector - INFO - Ensuring database directory exists
2025-07-05 18:50:34,148 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 18:50:34,148 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 18:50:34,148 - db_connector - INFO - Seeding initial user data
2025-07-05 18:50:34,168 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 18:50:34,174 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 18:55:22,566 - db_connector - INFO - Ensuring database directory exists
2025-07-05 18:55:22,567 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 18:55:22,575 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 18:55:22,576 - db_connector - INFO - Seeding initial user data
2025-07-05 18:55:22,603 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 18:55:22,608 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-05 19:00:00,269 - db_connector - INFO - Ensuring database directory exists
2025-07-05 19:00:00,269 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-05 19:00:00,275 - db_connector - INFO - Successfully initialized database with all models
2025-07-05 19:00:00,275 - db_connector - INFO - Seeding initial user data
2025-07-05 19:00:00,294 - db_connector - INFO - Initial user data seeded successfully
2025-07-05 19:00:00,298 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-07 11:44:46,139 - db_connector - INFO - Ensuring database directory exists
2025-07-07 11:44:46,140 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 11:44:46,146 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 11:44:46,146 - db_connector - INFO - Seeding initial user data
2025-07-07 11:44:46,162 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 11:44:46,166 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-07 11:51:56,060 - db_connector - INFO - Ensuring database directory exists
2025-07-07 11:51:56,060 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 11:51:56,069 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 11:51:56,069 - db_connector - INFO - Seeding initial user data
2025-07-07 11:51:57,076 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 11:51:57,082 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 12:14:55,320 - db_connector - INFO - Ensuring database directory exists
2025-07-07 12:14:55,320 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 12:14:55,330 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 12:14:55,330 - db_connector - INFO - Seeding initial user data
2025-07-07 12:14:56,292 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 12:14:56,299 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 12:31:36,966 - db_connector - INFO - Ensuring database directory exists
2025-07-07 12:31:36,967 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 12:31:36,983 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 12:31:36,983 - db_connector - INFO - Seeding initial user data
2025-07-07 12:31:37,932 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 12:31:37,937 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 14:43:52,041 - db_connector - INFO - Ensuring database directory exists
2025-07-07 14:43:52,042 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 14:43:52,051 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 14:43:52,051 - db_connector - INFO - Seeding initial user data
2025-07-07 14:43:53,052 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 14:43:53,061 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 14:54:43,794 - db_connector - INFO - Ensuring database directory exists
2025-07-07 14:54:43,794 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 14:54:43,801 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 14:54:43,801 - db_connector - INFO - Seeding initial user data
2025-07-07 14:54:44,724 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 14:54:44,740 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 17:06:32,776 - db_connector - INFO - Ensuring database directory exists
2025-07-07 17:06:32,780 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 17:06:32,792 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 17:06:32,792 - db_connector - INFO - Seeding initial user data
2025-07-07 17:06:33,871 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 17:06:33,879 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 17:19:12,418 - db_connector - INFO - Ensuring database directory exists
2025-07-07 17:19:12,418 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 17:19:12,433 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 17:19:12,433 - db_connector - INFO - Seeding initial user data
2025-07-07 17:19:13,394 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 17:19:13,399 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 17:23:43,141 - db_connector - INFO - Ensuring database directory exists
2025-07-07 17:23:43,141 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 17:23:43,157 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 17:23:43,158 - db_connector - INFO - Seeding initial user data
2025-07-07 17:23:44,226 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 17:23:44,230 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 17:27:43,700 - db_connector - INFO - Ensuring database directory exists
2025-07-07 17:27:43,700 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 17:27:43,712 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 17:27:43,712 - db_connector - INFO - Seeding initial user data
2025-07-07 17:27:44,662 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 17:27:44,666 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 17:41:36,155 - db_connector - INFO - Ensuring database directory exists
2025-07-07 17:41:36,155 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 17:41:36,171 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 17:41:36,171 - db_connector - INFO - Seeding initial user data
2025-07-07 17:41:37,152 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 17:41:37,157 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 17:46:25,239 - db_connector - INFO - Ensuring database directory exists
2025-07-07 17:46:25,239 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 17:46:25,255 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 17:46:25,255 - db_connector - INFO - Seeding initial user data
2025-07-07 17:46:26,204 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 17:46:26,217 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 17:48:15,799 - db_connector - INFO - Ensuring database directory exists
2025-07-07 17:48:15,800 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 17:48:15,806 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 17:48:15,806 - db_connector - INFO - Seeding initial user data
2025-07-07 17:48:16,779 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 17:48:16,783 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 18:00:40,085 - db_connector - INFO - Ensuring database directory exists
2025-07-07 18:00:40,085 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 18:00:40,106 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 18:00:40,106 - db_connector - INFO - Seeding initial user data
2025-07-07 18:00:41,070 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 18:00:41,086 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 18:15:58,459 - db_connector - INFO - Ensuring database directory exists
2025-07-07 18:15:58,459 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 18:15:58,470 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 18:15:58,470 - db_connector - INFO - Seeding initial user data
2025-07-07 18:15:59,462 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 18:15:59,477 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 18:25:36,020 - db_connector - INFO - Ensuring database directory exists
2025-07-07 18:25:36,020 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 18:25:36,036 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 18:25:36,036 - db_connector - INFO - Seeding initial user data
2025-07-07 18:25:37,015 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 18:25:37,020 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-07 18:35:08,602 - db_connector - INFO - Ensuring database directory exists
2025-07-07 18:35:08,602 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-07 18:35:08,613 - db_connector - INFO - Successfully initialized database with all models
2025-07-07 18:35:08,613 - db_connector - INFO - Seeding initial user data
2025-07-07 18:35:09,541 - db_connector - INFO - Initial user data seeded successfully
2025-07-07 18:35:09,553 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-08 13:47:19,827 - db_connector - INFO - Ensuring database directory exists
2025-07-08 13:47:19,827 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-08 13:47:19,837 - db_connector - INFO - Successfully initialized database with all models
2025-07-08 13:47:19,837 - db_connector - INFO - Seeding initial user data
2025-07-08 13:47:20,811 - db_connector - INFO - Initial user data seeded successfully
2025-07-08 13:47:20,816 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-08 13:49:36,518 - db_connector - INFO - Ensuring database directory exists
2025-07-08 13:49:36,518 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-08 13:49:36,518 - db_connector - INFO - Successfully initialized database with all models
2025-07-08 13:49:36,518 - db_connector - INFO - Seeding initial user data
2025-07-08 13:49:36,539 - db_connector - INFO - Initial user data seeded successfully
2025-07-08 13:49:36,545 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-08 14:20:08,912 - db_connector - INFO - Ensuring database directory exists
2025-07-08 14:20:08,912 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-08 14:20:08,925 - db_connector - INFO - Successfully initialized database with all models
2025-07-08 14:20:08,925 - db_connector - INFO - Seeding initial user data
2025-07-08 14:20:08,945 - db_connector - INFO - Initial user data seeded successfully
2025-07-08 14:20:08,948 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-08 15:57:21,562 - db_connector - INFO - Ensuring database directory exists
2025-07-08 15:57:21,562 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-08 15:57:21,578 - db_connector - INFO - Successfully initialized database with all models
2025-07-08 15:57:21,578 - db_connector - INFO - Seeding initial user data
2025-07-08 15:57:22,504 - db_connector - INFO - Initial user data seeded successfully
2025-07-08 15:57:22,504 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-08 16:36:07,767 - db_connector - INFO - Ensuring database directory exists
2025-07-08 16:36:07,767 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-08 16:36:07,778 - db_connector - INFO - Successfully initialized database with all models
2025-07-08 16:36:07,778 - db_connector - INFO - Seeding initial user data
2025-07-08 16:36:08,857 - db_connector - INFO - Initial user data seeded successfully
2025-07-08 16:36:08,862 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-08 16:40:56,019 - db_connector - INFO - Ensuring database directory exists
2025-07-08 16:40:56,019 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-08 16:40:56,030 - db_connector - INFO - Successfully initialized database with all models
2025-07-08 16:40:56,031 - db_connector - INFO - Seeding initial user data
2025-07-08 16:40:57,029 - db_connector - INFO - Initial user data seeded successfully
2025-07-08 16:40:57,033 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-08 16:51:39,774 - db_connector - INFO - Ensuring database directory exists
2025-07-08 16:51:39,774 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-08 16:51:39,785 - db_connector - INFO - Successfully initialized database with all models
2025-07-08 16:51:39,785 - db_connector - INFO - Seeding initial user data
2025-07-08 16:51:40,774 - db_connector - INFO - Initial user data seeded successfully
2025-07-08 16:51:40,774 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-08 17:47:21,961 - db_connector - INFO - Ensuring database directory exists
2025-07-08 17:47:21,961 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-08 17:47:21,961 - db_connector - INFO - Successfully initialized database with all models
2025-07-08 17:47:21,973 - db_connector - INFO - Seeding initial user data
2025-07-08 17:47:22,899 - db_connector - INFO - Initial user data seeded successfully
2025-07-08 17:47:22,899 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-08 18:25:13,063 - db_connector - INFO - Ensuring database directory exists
2025-07-08 18:25:13,063 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-08 18:25:13,076 - db_connector - INFO - Successfully initialized database with all models
2025-07-08 18:25:13,076 - db_connector - INFO - Seeding initial user data
2025-07-08 18:25:14,047 - db_connector - INFO - Initial user data seeded successfully
2025-07-08 18:25:14,053 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 10:23:16,910 - db_connector - INFO - Ensuring database directory exists
2025-07-11 10:23:16,910 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 10:23:16,910 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 10:23:16,910 - db_connector - INFO - Seeding initial user data
2025-07-11 10:23:17,873 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 10:23:17,878 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 10:25:13,641 - db_connector - INFO - Ensuring database directory exists
2025-07-11 10:25:13,641 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 10:25:13,641 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 10:25:13,641 - db_connector - INFO - Seeding initial user data
2025-07-11 10:25:14,590 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 10:25:14,593 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 10:26:17,959 - db_connector - INFO - Ensuring database directory exists
2025-07-11 10:26:17,959 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 10:26:17,964 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 10:26:17,964 - db_connector - INFO - Seeding initial user data
2025-07-11 10:26:17,979 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 10:26:17,979 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 10:28:21,968 - db_connector - INFO - Ensuring database directory exists
2025-07-11 10:28:21,968 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 10:28:21,975 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 10:28:21,975 - db_connector - INFO - Seeding initial user data
2025-07-11 10:28:21,993 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 10:28:21,997 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 10:34:13,682 - db_connector - INFO - Ensuring database directory exists
2025-07-11 10:34:13,686 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 10:34:13,689 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 10:34:13,689 - db_connector - INFO - Seeding initial user data
2025-07-11 10:34:13,704 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 10:34:13,708 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 10:39:46,883 - db_connector - INFO - Ensuring database directory exists
2025-07-11 10:39:46,883 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 10:39:46,893 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 10:39:46,893 - db_connector - INFO - Seeding initial user data
2025-07-11 10:39:46,912 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 10:39:46,916 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 10:47:06,993 - db_connector - INFO - Ensuring database directory exists
2025-07-11 10:47:06,993 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 10:47:06,993 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 10:47:06,993 - db_connector - INFO - Seeding initial user data
2025-07-11 10:47:07,010 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 10:47:07,027 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 10:47:27,393 - db_connector - INFO - Ensuring database directory exists
2025-07-11 10:47:27,393 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 10:47:27,399 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 10:47:27,399 - db_connector - INFO - Seeding initial user data
2025-07-11 10:47:28,335 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 10:47:28,341 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 11:01:50,292 - db_connector - INFO - Ensuring database directory exists
2025-07-11 11:01:50,292 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 11:01:50,296 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 11:01:50,296 - db_connector - INFO - Seeding initial user data
2025-07-11 11:01:51,265 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 11:01:51,270 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 11:10:17,223 - db_connector - INFO - Ensuring database directory exists
2025-07-11 11:10:17,223 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 11:10:17,231 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 11:10:17,232 - db_connector - INFO - Seeding initial user data
2025-07-11 11:10:17,254 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 11:10:17,257 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 11:16:31,324 - db_connector - INFO - Ensuring database directory exists
2025-07-11 11:16:31,324 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 11:16:31,329 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 11:16:31,329 - db_connector - INFO - Seeding initial user data
2025-07-11 11:16:31,341 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 11:16:31,349 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 11:17:31,947 - db_connector - INFO - Ensuring database directory exists
2025-07-11 11:17:31,947 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 11:17:31,963 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 11:17:31,963 - db_connector - INFO - Seeding initial user data
2025-07-11 11:17:31,986 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 11:17:31,986 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 11:28:03,944 - db_connector - INFO - Ensuring database directory exists
2025-07-11 11:28:03,944 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 11:28:03,944 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 11:28:03,944 - db_connector - INFO - Seeding initial user data
2025-07-11 11:28:03,970 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 11:28:03,970 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 11:32:48,701 - db_connector - INFO - Ensuring database directory exists
2025-07-11 11:32:48,701 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 11:32:48,712 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 11:32:48,712 - db_connector - INFO - Seeding initial user data
2025-07-11 11:32:48,726 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 11:32:48,732 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 11:35:41,385 - db_connector - INFO - Ensuring database directory exists
2025-07-11 11:35:41,385 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 11:35:41,395 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 11:35:41,395 - db_connector - INFO - Seeding initial user data
2025-07-11 11:35:42,308 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 11:35:42,328 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 11:38:24,890 - db_connector - INFO - Ensuring database directory exists
2025-07-11 11:38:24,890 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 11:38:24,903 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 11:38:24,903 - db_connector - INFO - Seeding initial user data
2025-07-11 11:38:25,863 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 11:38:25,863 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 11:40:21,719 - db_connector - INFO - Ensuring database directory exists
2025-07-11 11:40:21,719 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 11:40:21,733 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 11:40:21,733 - db_connector - INFO - Seeding initial user data
2025-07-11 11:40:22,663 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 11:40:22,663 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 11:41:31,383 - db_connector - INFO - Ensuring database directory exists
2025-07-11 11:41:31,383 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 11:41:31,404 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 11:41:31,404 - db_connector - INFO - Seeding initial user data
2025-07-11 11:41:32,325 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 11:41:32,325 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 14:05:19,893 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:05:19,893 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:05:19,898 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:05:19,898 - db_connector - INFO - Seeding initial user data
2025-07-11 14:05:20,874 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:05:20,874 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 14:40:42,623 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:40:42,623 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:40:42,623 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:40:42,623 - db_connector - INFO - Seeding initial user data
2025-07-11 14:40:43,576 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:40:43,587 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 14:53:28,844 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:53:28,845 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:53:28,845 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:53:28,845 - db_connector - INFO - Seeding initial user data
2025-07-11 14:53:28,862 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:53:28,872 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 14:53:55,094 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:53:55,094 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:53:55,094 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:53:55,094 - db_connector - INFO - Seeding initial user data
2025-07-11 14:53:55,111 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:53:55,111 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 14:54:16,343 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:54:16,343 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:54:16,343 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:54:16,343 - db_connector - INFO - Seeding initial user data
2025-07-11 14:54:16,365 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:54:16,369 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 14:56:22,196 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:56:22,196 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:56:22,205 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:56:22,205 - db_connector - INFO - Seeding initial user data
2025-07-11 14:56:23,158 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:56:23,163 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 14:56:51,825 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:56:51,825 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:56:51,827 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:56:51,827 - db_connector - INFO - Seeding initial user data
2025-07-11 14:56:51,847 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:56:51,847 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 14:57:08,491 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:57:08,491 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:57:08,495 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:57:08,495 - db_connector - INFO - Seeding initial user data
2025-07-11 14:57:08,512 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:57:08,512 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 14:57:26,186 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:57:26,186 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:57:26,193 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:57:26,193 - db_connector - INFO - Seeding initial user data
2025-07-11 14:57:27,142 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:57:27,142 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 14:57:44,505 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:57:44,505 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:57:44,512 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:57:44,512 - db_connector - INFO - Seeding initial user data
2025-07-11 14:57:44,529 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:57:44,529 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 14:58:05,749 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:58:05,750 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:58:05,756 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:58:05,756 - db_connector - INFO - Seeding initial user data
2025-07-11 14:58:05,774 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:58:05,778 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 14:58:20,353 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:58:20,353 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:58:20,360 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:58:20,360 - db_connector - INFO - Seeding initial user data
2025-07-11 14:58:21,304 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:58:21,304 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 14:58:35,128 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:58:35,129 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:58:35,130 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:58:35,130 - db_connector - INFO - Seeding initial user data
2025-07-11 14:58:36,121 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:58:36,127 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 14:59:35,229 - db_connector - INFO - Ensuring database directory exists
2025-07-11 14:59:35,229 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 14:59:35,232 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 14:59:35,232 - db_connector - INFO - Seeding initial user data
2025-07-11 14:59:36,205 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 14:59:36,205 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 15:00:19,067 - db_connector - INFO - Ensuring database directory exists
2025-07-11 15:00:19,068 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 15:00:19,076 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 15:00:19,076 - db_connector - INFO - Seeding initial user data
2025-07-11 15:00:20,019 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 15:00:20,019 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 15:00:42,888 - db_connector - INFO - Ensuring database directory exists
2025-07-11 15:00:42,888 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 15:00:42,899 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 15:00:42,900 - db_connector - INFO - Seeding initial user data
2025-07-11 15:00:42,912 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 15:00:42,912 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 15:08:04,094 - db_connector - INFO - Ensuring database directory exists
2025-07-11 15:08:04,094 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 15:08:04,110 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 15:08:04,110 - db_connector - INFO - Seeding initial user data
2025-07-11 15:08:05,229 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 15:08:05,239 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 15:10:34,449 - db_connector - INFO - Ensuring database directory exists
2025-07-11 15:10:34,449 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 15:10:34,468 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 15:10:34,468 - db_connector - INFO - Seeding initial user data
2025-07-11 15:10:35,456 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 15:10:35,461 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 15:11:20,807 - db_connector - INFO - Ensuring database directory exists
2025-07-11 15:11:20,807 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 15:11:20,826 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 15:11:20,827 - db_connector - INFO - Seeding initial user data
2025-07-11 15:11:21,774 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 15:11:21,774 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 15:13:19,137 - db_connector - INFO - Ensuring database directory exists
2025-07-11 15:13:19,138 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 15:13:19,144 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 15:13:19,144 - db_connector - INFO - Seeding initial user data
2025-07-11 15:13:19,157 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 15:13:19,160 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 15:15:22,604 - db_connector - INFO - Ensuring database directory exists
2025-07-11 15:15:22,604 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 15:15:22,620 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 15:15:22,620 - db_connector - INFO - Seeding initial user data
2025-07-11 15:15:23,567 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 15:15:23,579 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 15:17:23,290 - db_connector - INFO - Ensuring database directory exists
2025-07-11 15:17:23,290 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 15:17:23,290 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 15:17:23,290 - db_connector - INFO - Seeding initial user data
2025-07-11 15:17:23,313 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 15:17:23,316 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-11 15:22:06,825 - db_connector - INFO - Ensuring database directory exists
2025-07-11 15:22:06,825 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 15:22:06,825 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 15:22:06,825 - db_connector - INFO - Seeding initial user data
2025-07-11 15:22:07,767 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 15:22:07,772 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-11 16:14:50,580 - db_connector - INFO - Ensuring database directory exists
2025-07-11 16:14:50,580 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-11 16:14:50,590 - db_connector - INFO - Successfully initialized database with all models
2025-07-11 16:14:50,590 - db_connector - INFO - Seeding initial user data
2025-07-11 16:14:50,612 - db_connector - INFO - Initial user data seeded successfully
2025-07-11 16:14:50,613 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-12 10:20:05,311 - db_connector - INFO - Ensuring database directory exists
2025-07-12 10:20:05,311 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 10:20:05,409 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 10:20:05,409 - db_connector - INFO - Seeding initial user data
2025-07-12 10:20:06,410 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 10:20:06,410 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 10:59:21,184 - db_connector - INFO - Ensuring database directory exists
2025-07-12 10:59:21,184 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 10:59:21,203 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 10:59:21,203 - db_connector - INFO - Seeding initial user data
2025-07-12 10:59:22,200 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 10:59:22,200 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 11:14:53,659 - db_connector - INFO - Ensuring database directory exists
2025-07-12 11:14:53,659 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 11:14:53,665 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 11:14:53,665 - db_connector - INFO - Seeding initial user data
2025-07-12 11:14:54,672 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 11:14:54,677 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 12:51:40,847 - db_connector - INFO - Ensuring database directory exists
2025-07-12 12:51:40,847 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 12:51:40,856 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 12:51:40,856 - db_connector - INFO - Seeding initial user data
2025-07-12 12:51:41,851 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 12:51:41,855 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 13:01:20,145 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:01:20,145 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:01:20,151 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:01:20,151 - db_connector - INFO - Seeding initial user data
2025-07-12 13:01:21,131 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:01:21,131 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 13:02:22,780 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:02:22,780 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:02:22,809 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:02:22,809 - db_connector - INFO - Seeding initial user data
2025-07-12 13:02:23,895 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:02:23,901 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 13:05:05,727 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:05:05,727 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:05:05,765 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:05:05,765 - db_connector - INFO - Seeding initial user data
2025-07-12 13:05:06,728 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:05:06,733 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 13:12:16,298 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:12:16,299 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:12:16,451 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:12:16,451 - db_connector - INFO - Seeding initial user data
2025-07-12 13:12:17,395 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:12:17,402 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 13:19:38,414 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:19:38,414 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:19:38,423 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:19:38,423 - db_connector - INFO - Seeding initial user data
2025-07-12 13:19:39,400 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:19:39,405 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 13:20:33,856 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:20:33,856 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:20:33,863 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:20:33,864 - db_connector - INFO - Seeding initial user data
2025-07-12 13:20:33,881 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:20:33,883 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-12 13:21:01,508 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:21:01,508 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:21:01,515 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:21:01,515 - db_connector - INFO - Seeding initial user data
2025-07-12 13:21:02,452 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:21:02,457 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 13:21:24,607 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:21:24,607 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:21:24,617 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:21:24,617 - db_connector - INFO - Seeding initial user data
2025-07-12 13:21:24,632 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:21:24,634 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-12 13:21:57,713 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:21:57,713 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:21:57,724 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:21:57,724 - db_connector - INFO - Seeding initial user data
2025-07-12 13:21:58,688 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:21:58,693 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 13:22:20,553 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:22:20,554 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:22:20,556 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:22:20,556 - db_connector - INFO - Seeding initial user data
2025-07-12 13:22:20,575 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:22:20,575 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-12 13:22:39,284 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:22:39,284 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:22:39,295 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:22:39,295 - db_connector - INFO - Seeding initial user data
2025-07-12 13:22:39,311 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:22:39,314 - db_connector - INFO - Knowledge base already contains data, skipping seed
2025-07-12 13:36:29,210 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:36:29,211 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:36:29,216 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:36:29,216 - db_connector - INFO - Seeding initial user data
2025-07-12 13:36:30,172 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:36:30,185 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 13:51:44,315 - db_connector - INFO - Ensuring database directory exists
2025-07-12 13:51:44,316 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 13:51:44,321 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 13:51:44,321 - db_connector - INFO - Seeding initial user data
2025-07-12 13:51:45,279 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 13:51:45,284 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 14:01:32,729 - db_connector - INFO - Ensuring database directory exists
2025-07-12 14:01:32,729 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 14:01:32,760 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 14:01:32,760 - db_connector - INFO - Seeding initial user data
2025-07-12 14:01:33,721 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 14:01:33,728 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 14:09:38,081 - db_connector - INFO - Ensuring database directory exists
2025-07-12 14:09:38,081 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 14:09:38,087 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 14:09:38,087 - db_connector - INFO - Seeding initial user data
2025-07-12 14:09:39,013 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 14:09:39,026 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 14:18:26,918 - db_connector - INFO - Ensuring database directory exists
2025-07-12 14:18:26,918 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 14:18:26,918 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 14:18:26,918 - db_connector - INFO - Seeding initial user data
2025-07-12 14:18:27,880 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 14:18:27,885 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
2025-07-12 14:39:53,150 - db_connector - INFO - Ensuring database directory exists
2025-07-12 14:39:53,150 - db_connector - INFO - Creating database tables from SQLAlchemy models
2025-07-12 14:39:53,153 - db_connector - INFO - Successfully initialized database with all models
2025-07-12 14:39:53,153 - db_connector - INFO - Seeding initial user data
2025-07-12 14:39:54,088 - db_connector - INFO - Initial user data seeded successfully
2025-07-12 14:39:54,104 - db_connector - INFO - Successfully seeded knowledge base with 5 entries
