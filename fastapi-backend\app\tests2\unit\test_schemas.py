import pytest
from pydantic import ValidationError
from schemas import (
    UserSchemas, annotation_schemas, FTPConfig, ImageProcessingSchemas, PDF_ExtractorSchemas, RequestResponseSchemas, AdminSettingsSchemas
)

# List of (schema, valid, invalid) tuples for each model
schema_cases = [
    # UserSchemas
    (UserSchemas.UserRegisterRequest, {"username": "a", "password": "bcd12345", "confirm_password": "bcd12345", "role": "admin", "full_name": "A", "email": "<EMAIL>"}, {"username": "a"}),
    (UserSchemas.LoginRequest, {"username": "a", "password": "bcd12345"}, {"username": "a"}),
    (UserSchemas.ChangePasswordRequest, {"current_password": "a", "new_password": "bcd12345", "confirm_password": "bcd12345"}, {"current_password": "a"}),
    (UserSchemas.UserCreate, {"username": "a", "password_hash": "hash", "email": "<EMAIL>", "full_name": "A", "role": "admin"}, {"username": "a"}),
    (UserSchemas.AddUserRequest, {"username": "a", "password": "bcd12345", "full_name": "A", "email": "<EMAIL>", "role": "admin"}, {"username": "a"}),
    (UserSchemas.UserUpdate, {"email": "<EMAIL>"}, {"email": 123}),
    (UserSchemas.RefreshTokenRequest, {"refresh_token": "tok"}, {}),
    (UserSchemas.UserResponse, {"id": 1, "username": "a", "email": "<EMAIL>", "full_name": "A", "role": "admin", "is_active": True, "created_at": "2020-01-01T00:00:00"}, {"id": 1}),
    (UserSchemas.TokenResponse, {"access_token": "tok", "refresh_token": "tok", "token_type": "bearer"}, {"access_token": "tok"}),
    (UserSchemas.AccessTokenResponse, {"access_token": "tok", "token_type": "bearer"}, {"access_token": "tok"}),
    # annotation_schemas
    (annotation_schemas.ImageInfo, {"name": "img", "path": "/x", "size": 1, "type": "file"}, {"name": "img"}),
    (annotation_schemas.BatchInfo, {"id": 1, "batch_name": "b", "images": ["a"], "image_count": 1, "status": "assigned"}, {"id": 1}),
    (annotation_schemas.SaveLabelsRequest, {"session_id": "s", "labels": {"a": "b"}}, {"labels": {}}),
    (annotation_schemas.SaveLabelsResponse, {"success": True}, {}),
    (annotation_schemas.ImageResponse, {"images": [{"name": "img", "path": "/x", "type": "file"}], "session_id": "s"}, {"images": []}),
    (annotation_schemas.CropImageRequest, {"image_path": "/x"}, {}),
    (annotation_schemas.CropImageResponse, {"success": True}, {}),
    # FTPConfig
    (FTPConfig, {"host": "h", "port": 21, "user": "u", "pwd": "p"}, {"host": "h"}),
    # ImageProcessingSchemas
    (ImageProcessingSchemas.ImageBatchBase, {"batch_name": "b", "images": ["a"], "image_count": 1}, {"batch_name": "b"}),
    (ImageProcessingSchemas.ProcessedManualBatch, {"id": 1, "batch_name": "b", "images": ["a"], "image_count": 1}, {"id": 1}),
    (ImageProcessingSchemas.ProcessedVerificationBatch, {"id": 1, "batch_name": "b", "images": ["a"], "image_count": 1}, {"id": 1}),
    (ImageProcessingSchemas.BatchAssignmentRequest, {"username": "a", "batch_name": "b"}, {"username": "a"}),
    # PDF_ExtractorSchemas
    (PDF_ExtractorSchemas.ImageBase, {"image_data": "data", "image_type": "png"}, {"image_data": "data"}),
    (PDF_ExtractorSchemas.Image, {"id": 1, "page_id": 1, "image_data": "data", "image_type": "png"}, {"id": 1}),
    (PDF_ExtractorSchemas.PageBase, {"page_number": 1, "text_content": "t"}, {"page_number": 1}),
    (PDF_ExtractorSchemas.Page, {"id": 1, "document_id": 1, "page_number": 1, "text_content": "t", "images": []}, {"id": 1}),
    (PDF_ExtractorSchemas.DocumentBase, {"filename": "f"}, {}),
    (PDF_ExtractorSchemas.Document, {"id": 1, "filename": "f", "upload_time": "2020-01-01T00:00:00", "pages": []}, {"id": 1}),
    (PDF_ExtractorSchemas.DocumentSummary, {"id": 1, "filename": "f", "upload_time": "2020-01-01T00:00:00", "page_count": 1, "total_images": 1}, {"id": 1}),
    # RequestResponseSchemas
    (RequestResponseSchemas.GenerateQARequest, {"sources": ["a"], "qaCount": 1, "difficulty": "easy", "model": "m"}, {"sources": ["a"]}),
    (RequestResponseSchemas.GenerateConceptsRequest, {"sources": ["a"], "sampleCount": 1, "difficulty": "easy", "model": "m"}, {"sources": ["a"]}),
    (RequestResponseSchemas.JsonContentRequest, {"json_path": "p"}, {}),
    (RequestResponseSchemas.TaskResponse, {"task_id": "t", "image_path": "p", "labels": {}}, {"task_id": "t"}),
    (RequestResponseSchemas.TasksResponse, {"success": True, "tasks": [{"task_id": "t", "image_path": "p", "labels": {}}]}, {"success": True}),
    # AdminSettingsSchemas
    (AdminSettingsSchemas.AdminSetting, {"key": "k", "value": "v", "updated_at": "2020-01-01T00:00:00"}, {"key": "k"}),
    (AdminSettingsSchemas.AdminInstruction, {"id": 1, "mode": "annotation", "instructions": "do this"}, {"mode": "annotation"}),
    # Generic Success/Error schemas
    (UserSchemas.SuccessResponse, {"success": True, "message": "OK", "data": {"x": 1}}, {}),
    (UserSchemas.ErrorResponse, {"success": False, "error": "oops", "details": [{"field": "x"}]}, {}),

    # Annotation_schemas generic responses
    (annotation_schemas.SuccessResponse, {"success": True, "message": "Done", "data": {}}, {}),
    (annotation_schemas.ErrorResponse, {"detail": "Bad!"}, {}),

    # RequestResponseSchemas generic responses
    (RequestResponseSchemas.SuccessResponse, {"success": True, "message": "OK"}, {}),
    (RequestResponseSchemas.ErrorResponse, {"success": False, "error": "fail"}, {}),
]

@pytest.mark.parametrize("schema,valid,invalid", schema_cases)
def test_schema_valid(schema, valid, invalid):
    obj = schema.parse_obj(valid)
    assert obj
    # AccessTokenResponse and RequestResponseSchemas.SuccessResponse allow missing fields
    if schema.__name__ == "AccessTokenResponse":
        obj2 = schema.parse_obj(invalid)
        assert getattr(obj2, 'token_type', None) == "bearer"
    elif schema.__name__ == "SuccessResponse" and schema.__module__.endswith("RequestResponseSchemas"):
        # message is optional in this SuccessResponse, so empty dict should parse
        obj2 = schema.parse_obj(invalid)
        assert obj2
    else:
        with pytest.raises(ValidationError):
            schema.parse_obj(invalid) 