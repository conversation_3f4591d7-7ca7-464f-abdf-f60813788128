import React from 'react'
import { render, screen } from '@testing-library/react'

// Simple context test
describe('Context Tests', () => {
  it('should test context provider', () => {
    const MockContext = React.createContext({ value: 'test' })

    const TestComponent = () => {
      const context = React.useContext(MockContext)
      return <div>{context.value}</div>
    }

    const Provider = ({ children }: { children: React.ReactNode }) => (
      <MockContext.Provider value={{ value: 'hello world' }}>
        {children}
      </MockContext.Provider>
    )

    render(
      <Provider>
        <TestComponent />
      </Provider>
    )

    expect(screen.getByText('hello world')).toBeTruthy()
  })

  it('should handle context state changes', () => {
    const MockContext = React.createContext({
      count: 0,
      increment: () => {}
    })

    const TestComponent = () => {
      const { count, increment } = React.useContext(MockContext)
      return (
        <div>
          <span>Count: {count}</span>
          <button onClick={increment}>Increment</button>
        </div>
      )
    }

    const Provider = ({ children }: { children: React.ReactNode }) => {
      const [count, setCount] = React.useState(0)
      return (
        <MockContext.Provider value={{
          count,
          increment: () => setCount(c => c + 1)
        }}>
          {children}
        </MockContext.Provider>
      )
    }

    render(
      <Provider>
        <TestComponent />
      </Provider>
    )

    expect(screen.getByText('Count: 0')).toBeTruthy()

    const button = screen.getByText('Increment')
    button.click()
    expect(screen.getByText('Count: 1')).toBeTruthy()
  })
})
