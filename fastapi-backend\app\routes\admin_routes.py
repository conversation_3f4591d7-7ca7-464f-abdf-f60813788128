from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from sqlalchemy import text
from db.db_connector import get_db_connection as get_db
from dependencies.auth import get_current_active_user, get_user_service, UserService, create_user_response, get_annotator_mode, require_admin
from db.models.image_annotation import ImageAnnotation
from db.models.image_verification import ImageVerification
from db.models.supervision import Supervision
from db.models.datasets import Datasets
from db.models.admin_settings import AdminSettings

from cache import flush_cache, cache_set, cache_delete
from typing import List, Optional
from schemas.UserSchemas import SuccessResponse, ErrorResponse, UserResponse, UserUpdate
from schemas.UserSchemas import UserCreate, AddUserRequest
from pydantic import BaseModel
from schemas.FTPConfig import FTPConfig
from core.nas_connector import parse_ftp_url, create_nas_connector, get_ftp_connector
from core.config import get_settings, ITEMS_PER_PAGE
from schemas.AdminSettingsSchemas import AdminInstruction
from utils.google_drive.google_auth import OAuthConfig
from utils.google_drive.google_drive import GoogleDriveClient

import os
import json
import logging
import math
from services.batch_service import get_batch_manager
from cache.admin_cache import invalidate_stats_cache
from urllib.parse import unquote
from utils.image_processing import get_images_from_folder,get_image_from_storage
from core.security import hash_password
from db.models.user import AnnotatorMode
from googleapiclient.http import MediaIoBaseUpload
from io import BytesIO
from dotenv import set_key, unset_key

logger = logging.getLogger('admin_routes')

router = APIRouter(
    prefix="/admin",
    tags=["Admin"],
    dependencies=[Depends(get_current_active_user), Depends(require_admin())],
    responses={401: {"model": ErrorResponse}, 403: {"model": ErrorResponse}})


class NASConnectionRequest(BaseModel):
    nas_type: str
    nas_url: str
    nas_username: str
    nas_password: str
    client_id: Optional[str] = None
    redirect_after: bool = False

class InstructionsRequest(BaseModel):
    mode: str
    instructions: str
    dataset: Optional[int] = None
    
class ActionRequest(BaseModel):
    action: str

class SelectAnnotationFolderRequest(BaseModel):
    folder_path: str

class SelectVerificationFoldersRequest(BaseModel):
    image_folder: str
    label_file: str

class SelectDatasetRequest(BaseModel):
    dataset_id: int
    mode: str

class ConfigureGoogleDriveRequest(BaseModel):
    client_id: str
    client_secret: str
    folder_id: Optional[str] = None

class MergeDatasetRequest(BaseModel):
    dataset_name: str
    storage_destination: Optional[str] = "current"

#................................. Dashboard .................................
@router.get("/dashboard", response_model=SuccessResponse)
async def dashboard(request: Request):
    """
    Main admin dashboard endpoint.
    Returns configuration and status data for the admin dashboard.
    """
    try:
        settings = get_settings()
        nas_status = settings.nas_settings.is_connected
        drive_client = GoogleDriveClient(request)
        drive_status = drive_client.is_configured()

        data = {
            "config": {
                "nas_connected": nas_status,
                "drive_connected": drive_status
            }
        }

        return SuccessResponse(
            success=True,
            message="Dashboard data retrieved successfully",
            data=data
        )

    except Exception as e:
        logger.error(f"Error in dashboard route: {str(e)}")
        logger.exception("Detailed error:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dashboard data"
        )

@router.post("/connect-nas",response_model=SuccessResponse)
async def connect_nas(nas_req: NASConnectionRequest):
    """
    Connect to a custom NAS using provided credentials and update global settings.
    """
    url = nas_req.nas_url if nas_req.nas_url.startswith("ftp://") else f"ftp://{nas_req.nas_url}"
    
    host, port = parse_ftp_url(url)
    
    ftp_cfg = FTPConfig(host=host, port=port, user=nas_req.nas_username, pwd=nas_req.nas_password)
    
    connector = await create_nas_connector(ftp_cfg)
    
    if not connector or not await connector.authenticate():
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,
                             detail="Authentication failed with provided credentials")
    
    settings = get_settings()
    settings.nas_settings.url = url
    settings.nas_settings.nas_username = nas_req.nas_username
    settings.nas_settings.nas_password = nas_req.nas_password
    settings.nas_settings.type = nas_req.nas_type
    
    # Persist NAS credentials to .env file
    dotenv_path = os.path.join(os.getcwd(), '.env')
    set_key(dotenv_path, 'NAS_URL', url)
    set_key(dotenv_path, 'NAS_USERNAME', nas_req.nas_username)
    set_key(dotenv_path, 'NAS_PASSWORD', nas_req.nas_password)
    set_key(dotenv_path, 'NAS_TYPE', nas_req.nas_type)
    # Persist optional client id to .env
    if nas_req.client_id:
        set_key(dotenv_path, 'CLIENT_ID', nas_req.client_id)
    else:
        unset_key(dotenv_path, 'CLIENT_ID')
    
    return SuccessResponse(success=True, message="Successfully connected to NAS")

@router.post("/disconnect-nas",response_model=SuccessResponse)
async def disconnect_nas():
    """
    Clear any custom NAS settings from global configuration.
    """
    settings = get_settings()
    settings.nas_settings.url = ""
    settings.nas_settings.nas_username = ""
    settings.nas_settings.nas_password = ""
    settings.nas_settings.type = ""
    # Remove NAS credentials from .env file
    dotenv_path = os.path.join(os.getcwd(), '.env')
    unset_key(dotenv_path, 'NAS_URL')
    unset_key(dotenv_path, 'NAS_USERNAME')
    unset_key(dotenv_path, 'NAS_PASSWORD')
    unset_key(dotenv_path, 'NAS_TYPE')
    unset_key(dotenv_path, 'CLIENT_ID')
    await cache_delete("nas_credentials")
    
    return SuccessResponse(success=True, message="Disconnected from NAS")

@router.get("/check-nas-connection", response_model=SuccessResponse)
async def check_nas_connection():
    """
    Check if a NAS connection is currently active.
    """
    settings = get_settings()
    connector = await get_ftp_connector()
    connected = connector is not None
    using_custom = bool(settings.nas_settings.nas_username and settings.nas_settings.nas_password)
    data = {"connected": connected, "using_custom": using_custom, "type": settings.nas_settings.type}
    return SuccessResponse(success=True, message="NAS connection status", data=data)

#................................. Google Drive .................................
@router.post("/configure-google-drive", response_model=SuccessResponse)
async def configure_google_drive(req: ConfigureGoogleDriveRequest, request: Request):
    """Configure Google Drive connection"""
    cfg = OAuthConfig(request)
    filename = cfg.save(req.client_id, req.client_secret, req.folder_id)
    if not filename:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="Failed to save Google Drive configuration")
    client = GoogleDriveClient(request)
    if client.is_configured():
        return SuccessResponse(success=True,
                               message="Google Drive connection successful",
                               data={"connected": True})
    auth_url = client.auth_url()
    return SuccessResponse(success=True,
                           message="Please complete authentication",
                           data={"auth_url": auth_url, "connected": False})

@router.get("/check-google-drive-connection", response_model=SuccessResponse)
async def check_google_drive_connection(request: Request):
    """Check if Google Drive is connected and authenticated"""
    client = GoogleDriveClient(request)
    connected = client.is_configured()
    return SuccessResponse(success=True,
                           message="Google Drive connection status",
                           data={"connected": connected})



@router.post("/reset-google-drive", response_model=SuccessResponse)
async def reset_google_drive(request: Request):
    """Reset all Google Drive configuration and credentials"""
    cfg = OAuthConfig(request)
    if cfg.reset():
        return SuccessResponse(success=True, message="Google Drive configuration has been reset")
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Failed to reset Google Drive configuration")

#................................. Manage Users .................................
@router.post("/flush-db",response_model=SuccessResponse)
async def flush_db(db: Session = Depends(get_db)):
    """
    Flush the image-related tables and reset admin settings indices, then flush Redis cache.
    """
    try:
        total_deleted = 0
        total_deleted += db.query(ImageAnnotation).delete()
        total_deleted += db.query(ImageVerification).delete()
        total_deleted += db.query(Supervision).delete()
        total_deleted += db.query(Datasets).delete()
        total_deleted += db.query(AdminSettings).delete()

        db.commit()
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                detail=f"Error flushing database tables: {str(e)}")

    try:
        cache_flushed = await flush_cache()
    except Exception as e:
        cache_flushed = False
        cache_error = str(e)

    if cache_flushed:
        message = f"Database tables and Redis cache flushed successfully. {total_deleted} records have been deleted."
    else:
        warning = cache_error if 'cache_error' in locals() else "Redis cache flush failed"
        message = (f"Database tables flushed successfully, but {warning}. "
                        f"{total_deleted} records have been deleted.")

    return SuccessResponse(success=True, message=message)


@router.get("/users", response_model=List[UserResponse])
async def list_users(
    user_service: UserService = Depends(get_user_service)
):
    users = user_service.get_all()
    return [create_user_response(user) for user in users]

@router.get("/users/{username}", response_model=UserResponse)
async def get_user(
    username: str,
    user_service: UserService = Depends(get_user_service)
):
    user = user_service.get_user_by_username(username)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return create_user_response(user)

@router.put("/users/{username}", response_model=UserResponse)
async def update_user(
    username: str,
    user_update: UserUpdate,
    user_service: UserService = Depends(get_user_service)
):
    user = user_service.get_user_by_username(username)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    updated_user = user_service.update_user(user, user_update)
    return create_user_response(updated_user)

@router.post("/users/{username}/suspend", response_model=SuccessResponse)
async def suspend_user(
    username: str,
    action_req: ActionRequest,
    user_service: UserService = Depends(get_user_service)
):
    user = user_service.get_user_by_username(username)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    if action_req.action == "suspend":
        user.is_active = False
    elif action_req.action == "unsuspend":
        user.is_active = True
    else:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid action")
    user_service.db.commit()
    return SuccessResponse(success=True, message=f"User {username} has been {'suspended' if not user.is_active else 'unsuspended'}")

@router.post("/add-user", response_model=SuccessResponse)
async def add_user(req: AddUserRequest, user_service: UserService = Depends(get_user_service)):
    """
    Add a new user to the database using plaintext password and optional annotation mode.
    """
    # Hash the provided password
    hashed = hash_password(req.password)
    # Prepare UserCreate DTO
    user_data = UserCreate(
        username=req.username,
        password_hash=hashed,
        full_name=req.full_name,
        email=req.email,
        role=req.role,
        is_active=req.is_active
    )
    user = user_service.create_user(user_data)
    if not user:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to create user")
    # Set annotator_mode if provided and valid
    if req.annotation_mode:
        try:
            mode_enum = AnnotatorMode(req.annotation_mode) if isinstance(req.annotation_mode, str) else req.annotation_mode
            user.annotator_mode = mode_enum
            user_service.db.commit()
        except ValueError:
            # ignore invalid annotation_mode
            pass
    return SuccessResponse(success=True, message="User added successfully", data={"user": create_user_response(user)})

#................................. Edit Instructions .................................
@router.get("/edit-instructions", response_model=AdminInstruction)
async def get_edit_instructions(mode: Optional[str] = None, dataset: Optional[int] = None, db: Session = Depends(get_db)):
    """
    Get instructions for supervision mode or dataset-specific instructions.
    """
    if mode == "supervision":
        settings_obj = db.query(AdminSettings).first()
        if not settings_obj:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Supervision instructions not found")
        return AdminInstruction(id=settings_obj.id, mode="supervision", instructions=settings_obj.supervision_instructions or "")
    if mode and dataset is not None:
        data_obj = db.query(Datasets).filter(Datasets.id == dataset).first()
        if not data_obj:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Dataset not found")
        return AdminInstruction(id=data_obj.id, mode=mode, instructions=data_obj.instructions or "")
    raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Mode and dataset parameters are required")

@router.post("/edit-instructions", response_model=SuccessResponse)
async def edit_instructions(req: InstructionsRequest, db: Session = Depends(get_db)):
    """
    Save instructions for supervision mode or dataset-specific instructions.
    """
    if req.mode == "supervision":
        settings_obj = db.query(AdminSettings).first()
        if not settings_obj:
            settings_obj = AdminSettings()
            db.add(settings_obj)
            db.flush()
        settings_obj.supervision_instructions = req.instructions
        db.commit()
        return SuccessResponse(success=True, message="Supervision instructions saved.")
    if req.dataset is None:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Dataset ID is required for dataset instructions")
    data_obj = db.query(Datasets).filter(Datasets.id == req.dataset).first()
    if not data_obj:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Dataset not found")
    data_obj.instructions = req.instructions
    data_obj.annotator_mode = req.mode
    db.commit()
    return SuccessResponse(success=True, message=f'Instructions for dataset "{data_obj.dataset_name}" saved.')

#................................. OCR Directory .................................
@router.get("/ocr-directory", response_model=SuccessResponse)
async def admin_ocr_directory(annotator_mode: str = Depends(get_annotator_mode)):
    """
    Get OCR directory settings and statistics for the admin dashboard.
    """
    try:
        return SuccessResponse(success=True, message="OCR directory settings retrieved successfully", data={"annotation_mode": annotator_mode})

    except Exception as e:
        logger.error(f"Error in admin_ocr_directory route: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load OCR directory settings")

@router.get("/browse-nas-directory", response_model=SuccessResponse)
async def browse_nas_directory(path: str = Query("/", description="Directory path to browse")):
    """
    Browse a directory on the NAS and return its contents.
    """
    connector = await get_ftp_connector()
    if not connector:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Not connected to NAS. Please connect first.")
    items = await connector.list_directory(path)
    if not items:
        return SuccessResponse(success=True, message="Directory is empty", data={"path": path, "items": [], "count": 0})
    if isinstance(items, dict) and "data" in items and "files" in items["data"]:
        items_list = items["data"]["files"]
    elif isinstance(items, list):
        items_list = items
    else:
        items_list = []
        logger.warning(f"Unexpected format received from list_directory for {path}: {items}")
    parent_path = os.path.dirname(path) if path != "/" else "/"
    return SuccessResponse(success=True, message="Directory contents", data={"path": path, 
     "parent_path": parent_path, "items": items_list, "count": len(items_list)})

@router.post("/select-annotation-folder", response_model=SuccessResponse)
async def select_annotation_folder(req: SelectAnnotationFolderRequest, db: Session = Depends(get_db)):
    """
    Select a folder for Annotation annotation mode.
    """
    folder_path = req.folder_path
    if not folder_path:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No folder path provided")
    connector = await get_ftp_connector()
    if not connector:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Not connected to NAS. Please connect first.")
    items = await connector.list_directory(folder_path)
    if items is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Folder not found: {folder_path}")

    batch_manager = get_batch_manager()
    # Retrieve optional client_id from environment
    client_id = os.getenv('CLIENT_ID', None)
    success, message, batch_count = await batch_manager.create_batches_from_folder(folder_path, mode="annotation", client_id=client_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Folder set but failed to create batches: {message}")
    return SuccessResponse(success=True, message=f"annotation annotation folder set to: {folder_path}. {message}", 
                           data={"folder_path": folder_path, "folder_name": os.path.basename(folder_path), "batch_count": batch_count})

@router.post("/select-verification-folders", response_model=SuccessResponse)
async def select_verification_folders(req: SelectVerificationFoldersRequest, db: Session = Depends(get_db)):
    """
    Select folders for verification mode via browser.
    """
    image_folder = req.image_folder.replace("\\", "/").replace("//", "/")
    if not image_folder.startswith("/"):
        image_folder = "/" + image_folder
    label_file = req.label_file.replace("\\", "/").replace("//", "/")
    if not label_file.startswith("/"):
        label_file = "/" + label_file
    connector = await get_ftp_connector()
    if not connector:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to connect to FTP storage")
    try:
        items = await connector.list_directory(image_folder)
        if not items:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Image folder not found or empty: {image_folder}")
        content = await connector.get_file_content(label_file)
        if not content:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Label file not found or empty: {label_file}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating files: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error validating files: {str(e)}")
    
    if isinstance(content, bytes):
        encodings = ["utf-8", "utf-8-sig", "latin1"]
        decoded = None
        last_error = None
        for encoding in encodings:
            try:
                decoded = content.decode(encoding)
                logger.info(f"Successfully decoded file using {encoding} encoding")
                break
            except UnicodeDecodeError as e:
                last_error = e
        if decoded is None:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Failed to decode with any encoding: {str(last_error)}")
        content = decoded
    if not content.strip():
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Label file is empty: {label_file}")
    try:
        parsed_json = json.loads(content)
    except json.JSONDecodeError as e:
        logger.error(f"JSON parse error for {label_file}: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid JSON format: {str(e)}")
    if not isinstance(parsed_json, dict):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="JSON file must contain a dictionary mapping filenames to labels")
    if not parsed_json:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="JSON file contains an empty dictionary")
    logger.info(f"Successfully validated JSON file: {label_file} via FTP ({len(parsed_json)} entries)")

    batch_manager = get_batch_manager()
    # Retrieve optional client_id from environment
    client_id = os.getenv('CLIENT_ID', None)
    success, message, batch_count = await batch_manager.create_batches_from_folder(image_folder, mode="verification", label_file=label_file, client_id=client_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Folders set but failed to create batches: {message}")
    return SuccessResponse(success=True, message=f"Verification folders set successfully. {message}",
                            data={"image_folder": image_folder, "image_folder_name": os.path.basename(image_folder),
                                   "label_file": label_file, "label_file_name": os.path.basename(label_file), "batch_count": batch_count})

@router.post("/select-dataset", response_model=SuccessResponse)
async def select_dataset(req: SelectDatasetRequest, db: Session = Depends(get_db)):
    """
    Select a dataset for annotation or verification.
    """
    if req.mode not in ["annotation", "verification"]:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid mode. Must be 'annotation' or 'verification'")
    dataset = db.query(Datasets).filter(Datasets.id == req.dataset_id).first()

    if not dataset:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Dataset not found")
    
    if dataset.annotator_mode != req.mode:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Mode mismatch: dataset mode is {dataset.annotator_mode}")
    
    if req.mode == "annotation":
        settings_obj = db.query(AdminSettings).first()
        if not settings_obj:
            settings_obj = AdminSettings()
            db.add(settings_obj)
            db.flush()
        settings_obj.annotation_dataset_name = dataset.dataset_name
        db.commit()
    else:
        settings_obj = db.query(AdminSettings).first()
        if not settings_obj:
            settings_obj = AdminSettings()
            db.add(settings_obj)
            db.flush()
        settings_obj.verification_dataset_name = dataset.dataset_name
        db.commit()

    await invalidate_stats_cache("ocr_directory")
    return SuccessResponse(success=True, message=f"Successfully selected {req.mode} dataset: {dataset.dataset_name}")

@router.get("/get-datasets", response_model=SuccessResponse)
async def get_datasets(mode: str = Query(..., description="annotation or verification mode"), db: Session = Depends(get_db)):
    """
    Get available datasets for a specific mode.
    """
    if mode not in ["annotation", "verification"]:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Invalid mode. Must be 'annotation' or 'verification'")
    datasets_query = db.query(Datasets).filter(Datasets.annotator_mode == mode).order_by(Datasets.dataset_name).all()
    datasets_list = []
    for ds in datasets_query:
        entry = {
            "id": ds.id,
            "name": ds.dataset_name,
            "folder_path": ds.dataset_image_path,
            "label_folder": (ds.label_folder_path if mode == "verification" else None),
            "total_batches": ds.total_batch,
            "completed_batches": ds.completed_batch,
            "progress_percentage": round((ds.completed_batch / ds.total_batch) * 100) if ds.total_batch > 0 else 0
        }
        datasets_list.append(entry)
    return SuccessResponse(
        success=True,
        message=f"Found {len(datasets_list)} datasets for {mode} mode",
        data={
            "datasets": datasets_list,
            "count": len(datasets_list)
        }
    )

@router.get("/browser/{folder:path}", response_model=SuccessResponse)
async def browser(
    folder: str,
    page: int = Query(1, ge=1, description="Page number")
):
    """
    Admin file browser API endpoint.
    """
    selected_folder = unquote(folder)

    images, total = await get_images_from_folder(selected_folder, page=page)
    total_pages = math.ceil(total / ITEMS_PER_PAGE) if ITEMS_PER_PAGE > 0 else 0

    parent_folder = os.path.dirname(selected_folder.rstrip("/"))
    if parent_folder == selected_folder or not parent_folder:
        parent_folder = ""

    data = {
        "images": images,
        "folder": selected_folder,
        "page": page,
        "total_pages": total_pages,
        "total_images": total,
        "parent_folder": parent_folder
    }
    return SuccessResponse(success=True, message="Directory listing", data=data)

#.......................Data Delivery.......................

@router.get("/data-delivery", response_model=SuccessResponse)
def data_delivery(dataset: Optional[int] = Query(None), db: Session = Depends(get_db)):
    """
    Admin dashboard for tracking auditor batch completion statistics.
    """
    try:
        ds_list = db.query(Datasets).order_by(Datasets.dataset_name).all()

        datasets = [{"id": ds.id, "name": ds.dataset_name} for ds in ds_list]
        valid_ids = [d["id"] for d in datasets]
        selected_dataset = dataset if dataset in valid_ids else (datasets[0]["id"] if datasets else None)

        merged_datasets = [ {"id": ds.id, "name": ds.dataset_name}
                            for ds in ds_list if ds.dataset_status and ds.dataset_status.lower() == "merged" ]
        pending_datasets = [ {"id": ds.id, "name": ds.dataset_name}
                             for ds in ds_list if not ds.dataset_status or ds.dataset_status.lower() != "merged" ]

        # Compute stats for the selected dataset
        selected_ds = next((ds for ds in ds_list if ds.id == selected_dataset), None)
        if selected_ds:
            total_completed = selected_ds.audited_batch
            total_batches = selected_ds.total_batch
        else:
            total_completed = 0
            total_batches = 0

        data = {
            "datasets": datasets,
            "selected_dataset": selected_dataset,
            "total_completed": total_completed,
            "total_batches": total_batches,
            "merged_datasets": merged_datasets,
            "pending_datasets": pending_datasets,
        }
        return SuccessResponse(success=True, message="Data delivery statistics", data=data)
    except Exception as e:
        logger.error(f"Error in data_delivery route: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.post("/merge-dataset-json", response_model=SuccessResponse)
async def merge_dataset_json(
    req: MergeDatasetRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Merge all JSON files in a dataset folder and save the result to the output folder.
    """
    try:
        dataset_name = req.dataset_name
        storage_destination = req.storage_destination
        connector = await get_ftp_connector()
        
        if not dataset_name:
            return SuccessResponse(success=False, message="No dataset name provided")
        
        paths = {
            'input': f'/Data/DATP Datasets/VERIFIED DATASET/{dataset_name}',
            'output': f'/Data/DATP Datasets/Final_Dataset',
            'output_file': f'{dataset_name}.json'
        }
        
        if not connector or not await connector.directory_exists(paths['input']):
            error_msg = 'Failed to connect to NAS' if not connector else f'Dataset directory not found: {paths["input"]}'
            return SuccessResponse(success=False, message=error_msg)
        
        if storage_destination == 'current' and not await connector.directory_exists(paths['output']):
            logger.info(f"Creating output directory: {paths['output']}")
            if not await connector.create_directory(paths['output']):
                return SuccessResponse(success=False, message=f'Failed to create output directory: {paths["output"]}')
        
        items = await connector.list_directory(paths['input'])
        json_files = [item for item in items if isinstance(item, dict) and 
                     item.get('type') == 'file' and 
                     item.get('name', '').lower().endswith('.json')] if items else []

        if not json_files:
            return SuccessResponse(success=False, message=f'No JSON files found in dataset directory: {paths["input"]}')
        
        merged_data = {}
        file_count = 0
        
        for json_file in json_files:
            file_path = f"{paths['input']}/{json_file.get('name')}"
            try:
                content = await connector.get_file_content(file_path)
                if not content:
                    logger.warning(f"Could not read file content: {file_path}")
                    continue
                for encoding in ['utf-8', 'utf-8-sig', 'latin1']:
                    try:
                        file_data = json.loads(content.decode(encoding))
                        if isinstance(file_data, dict):
                            merged_data.update(file_data)
                            file_count += 1
                            break
                    except (UnicodeDecodeError, json.JSONDecodeError):
                        continue
                else:
                    logger.warning(f"Could not decode file with any encoding: {file_path}")
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {str(e)}")
        
        if file_count == 0:
            return SuccessResponse(success=False, message='No valid JSON files could be processed')
        
        json_content = json.dumps(merged_data, indent=2, ensure_ascii=False).encode('utf-8')
        upload_success = False
        drive_link = None
        file_id = None
        
        if storage_destination == 'current':
            full_output_path = f"{paths['output']}/{paths['output_file']}"
            upload_success = await connector.save_file(full_output_path, json_content)
            if upload_success:
                logger.info(f"Successfully merged {file_count} JSON files into {full_output_path}")
            else:
                return SuccessResponse(success=False, message=f'Failed to save merged file to {full_output_path}')
                
        elif storage_destination == 'google-drive':
            client = GoogleDriveClient(request)
            if not client.is_configured():
                return SuccessResponse(success=False, message='Google Drive is not configured. Please configure it in the Admin Dashboard.')
            svc = client.admin_service()
            if not svc:
                return SuccessResponse(success=False, message='Failed to connect to Google Drive')
            try:
                folder_path = ['Data', 'DATP Datasets', 'Final_Dataset']
                current_parent = client.oauth.folder_id if client.oauth.folder_id else 'root'
                for folder_name in folder_path:
                    query = f"name='{folder_name}' and parents in '{current_parent}' and mimeType='application/vnd.google-apps.folder' and trashed=false"
                    existing_folders = svc.files().list(q=query, fields='files(id,name)').execute()
                    
                    if existing_folders.get('files'):
                        current_parent = existing_folders['files'][0]['id']
                    else:
                        folder_metadata = {
                            'name': folder_name,
                            'mimeType': 'application/vnd.google-apps.folder',
                            'parents': [current_parent]
                        }
                        created_folder = svc.files().create(body=folder_metadata, fields='id,name').execute()
                        current_parent = created_folder['id']
                
                media = MediaIoBaseUpload(BytesIO(json_content), mimetype='application/json', resumable=True)
                file_metadata = {
                    'name': paths['output_file'], 
                    'mimeType': 'application/json',
                    'parents': [current_parent]
                }
                drive_file = svc.files().create(
                    body=file_metadata,
                    media_body=media,
                    fields='id,webViewLink,parents'
                ).execute()
                file_id = drive_file.get('id')
                drive_link = drive_file.get('webViewLink')
                upload_success = bool(file_id)
                if not upload_success:
                    return SuccessResponse(success=False, message='Failed to upload to Google Drive - no file ID returned')
            except Exception as drive_error:
                logger.error(f"Google Drive upload error: {str(drive_error)}")
                return SuccessResponse(success=False, message=f'Google Drive upload failed: {str(drive_error)}')
        
        if upload_success:
            try:
                db.execute(text("UPDATE datasets SET dataset_status = 'merged' WHERE dataset_name = :name"),
                           {"name": dataset_name})
                db.commit()
            except Exception as db_error:
                logger.error(f"Error updating dataset status: {db_error}")
        
        success_message = f'Successfully merged {file_count} JSON files'
        if storage_destination == 'google-drive':
            success_message += ' and uploaded to Google Drive'
        
        response = {
            'success': True,
            'message': success_message,
            'file_count': file_count,
            'output_file': paths['output_file'],
            'total_entries': len(merged_data)
        }
        if storage_destination == 'google-drive':
            response.update({'drive_link': drive_link, 'drive_file_id': file_id})
        return SuccessResponse(**response)
        
    except Exception as e:
        logger.error(f"Error merging dataset JSON: {str(e)}")
        return SuccessResponse(success=False, message=f'Error: {str(e)}')
    
@router.get("/image/{image_path:path}")
async def admin_get_image(image_path: str):
    try:
        return await get_image_from_storage(image_path, include_response_time=True)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
