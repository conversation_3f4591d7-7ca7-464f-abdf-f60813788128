import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
os.environ['DATABASE_URL'] = 'sqlite:///C:/Users/<USER>/Desktop/DADP-Prod-FN-1/fastapi-backend/app/database.db'

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import event
os.environ['ENV'] = 'test'
from main import app
from db import db_connector
from db.db_connector import get_db_connection

# Set ENV to test before app loads

@pytest.fixture(scope='session', autouse=True)
def seed_initial_data():
    db_connector.initialize_database()
    db_connector.seed_knowledge_base()

@pytest.fixture(scope='function')
def db_session():
    from db.models import user, image_annotation, image_verification, datasets, knowledge_base, pdf_extractor  # import all models
    engine = create_engine(os.environ['DATABASE_URL'], connect_args={"check_same_thread": False})
    @event.listens_for(engine, "connect")
    def _enable_sqlite_settings(dbapi_conn, conn_record):
        # Enforce foreign key constraints for SQLite
        dbapi_conn.execute("PRAGMA foreign_keys=ON")
        # Enable WAL journal mode for better concurrency
        dbapi_conn.execute("PRAGMA journal_mode=WAL")
    # Drop and recreate all tables for a clean slate
    user.Base.metadata.drop_all(bind=engine)
    user.Base.metadata.create_all(bind=engine)
    image_annotation.Base.metadata.drop_all(bind=engine)
    image_annotation.Base.metadata.create_all(bind=engine)
    image_verification.Base.metadata.drop_all(bind=engine)
    image_verification.Base.metadata.create_all(bind=engine)
    datasets.Base.metadata.drop_all(bind=engine)
    datasets.Base.metadata.create_all(bind=engine)
    knowledge_base.Base.metadata.drop_all(bind=engine)
    knowledge_base.Base.metadata.create_all(bind=engine)
    pdf_extractor.Base.metadata.drop_all(bind=engine)
    pdf_extractor.Base.metadata.create_all(bind=engine)
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    connection = engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    db_connector.SessionLocal = lambda: session
    # Dependency override for FastAPI
    def get_db_override():
        try:
            yield session
        finally:
            pass
    app.dependency_overrides[get_db_connection] = get_db_override
    yield session
    session.close()
    transaction.rollback()
    connection.close()
    app.dependency_overrides.clear()

@pytest.fixture(scope='function')
def client(db_session):
    with TestClient(app) as c:
        yield c 

@pytest.fixture(scope='function')
def supervision_client(db_session):
    """
    TestClient fixture authenticated as a supervision annotator.
    """
    with TestClient(app) as client:
        payload = {
            "username": "supuser",
            "password": "Suppass123!",
            "confirm_password": "Suppass123!",
            "role": "annotator",
            "full_name": "Sup User",
            "email": "<EMAIL>"
        }
        reg_resp = client.post('/api/auth/register', json=payload)
        assert reg_resp.status_code == 200, f"Registration failed: {reg_resp.text}"
        login_resp = client.post('/api/auth/login', data={"username": "supuser", "password": "Suppass123!"})
        assert login_resp.status_code == 200, f"Login failed: {login_resp.text}"
        # Ensure user record has annotator_mode set to supervision
        from db.models.user import User, AnnotatorMode
        user = db_session.query(User).filter(User.username == payload["username"]).one()
        user.annotator_mode = AnnotatorMode.SUPERVISION
        db_session.commit()
        yield client 