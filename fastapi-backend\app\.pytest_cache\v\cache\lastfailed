{"tests2/contract/test_openapi.py::test_api_contract[GET /admin/google-drive-callback]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/auth/refresh-token]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/auth/register]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/auth/login]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/modes]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/datasets]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/verifiers]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/files]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/tasks]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/image]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/history]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/auditor/audit-record]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/auditor/save-labels]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/dashboard]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/connect-nas]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/disconnect-nas]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/check-nas-connection]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/configure-google-drive]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/check-google-drive-connection]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/reset-google-drive]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/flush-db]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/users]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/users/{username}]": true, "tests2/contract/test_openapi.py::test_api_contract[PUT /api/admin/users/{username}]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/users/{username}/suspend]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/add-user]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/edit-instructions]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/edit-instructions]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/ocr-directory]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/browse-nas-directory]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/select-annotation-folder]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/select-verification-folders]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/select-dataset]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/get-datasets]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/browser/{folder}]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/data-delivery]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/merge-dataset-json]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/image/{image_path}]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/annotator/dashboard]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/annotator/annotate]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/annotator/next-set]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/annotator/image/{image_path}]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/annotator/save-labels]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/NoteOCR/documents/]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/NoteOCR/documents/{document_id}]": true, "tests2/contract/test_openapi.py::test_api_contract[DELETE /api/NoteOCR/documents/{document_id}]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/NoteOCR/images/upload]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/NoteOCR/images/extract-ocr]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/NoteOCR/images/chat]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/supervision/upload]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/supervision/review]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/supervision/check-status/{image_id}]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/supervision/document-image/{image_id}]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/supervision/save-document]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/supervision/download_csv/{image_id}]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/supervision/download_txt/{image_id}]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/supervision/list-drive-folders]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/supervision/download_csv]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/client/datasets]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/client/datasets/{dataset_id}]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/synthetic-dataset/models]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/synthetic-dataset/dataset-types]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/synthetic-dataset/generate]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/synthetic-dataset/generate-nonref]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/synthetic-dataset/knowledge-entries]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/knowledge-base/entries]": true, "tests2/contract/test_openapi.py::test_api_contract[POST /api/knowledge-base/entries]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/knowledge-base/topics]": true, "tests2/contract/test_openapi.py::test_api_contract[GET /api/knowledge-base/entries/{entry_id}]": true, "tests2/contract/test_openapi.py::test_api_contract[PUT /api/knowledge-base/entries/{entry_id}]": true, "tests2/contract/test_openapi.py::test_api_contract[DELETE /api/knowledge-base/entries/{entry_id}]": true, "tests2/contract/test_openapi.py::TestClient": true, "tests2/contract/test_openapi.py::test_api_contract": true, "tests2/contract/test_contract.py::test_contract[GET /admin/google-drive-callback]": true, "tests2/contract/test_contract.py::test_contract[GET /api/auth/verify]": true, "tests2/contract/test_contract.py::test_contract[POST /api/auth/change-password]": true, "tests2/contract/test_contract.py::test_contract[GET /api/auth/me]": true, "tests2/contract/test_contract.py::test_contract[GET /api/auditor/modes]": true, "tests2/contract/test_contract.py::test_contract[GET /api/auditor/datasets]": true, "tests2/contract/test_contract.py::test_contract[GET /api/auditor/verifiers]": true, "tests2/contract/test_contract.py::test_contract[GET /api/auditor/files]": true, "tests2/contract/test_contract.py::test_contract[GET /api/auditor/tasks]": true, "tests2/contract/test_contract.py::test_contract[GET /api/auditor/image]": true, "tests2/contract/test_contract.py::test_contract[GET /api/auditor/history]": true, "tests2/contract/test_contract.py::test_contract[POST /api/auditor/audit-record]": true, "tests2/contract/test_contract.py::test_contract[POST /api/auditor/save-labels]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/dashboard]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/connect-nas]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/disconnect-nas]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/check-nas-connection]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/configure-google-drive]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/check-google-drive-connection]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/reset-google-drive]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/flush-db]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/users]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/users/{username}]": true, "tests2/contract/test_contract.py::test_contract[PUT /api/admin/users/{username}]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/users/{username}/suspend]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/add-user]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/edit-instructions]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/edit-instructions]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/ocr-directory]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/browse-nas-directory]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/select-annotation-folder]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/select-verification-folders]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/select-dataset]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/get-datasets]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/browser/{folder}]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/data-delivery]": true, "tests2/contract/test_contract.py::test_contract[POST /api/admin/merge-dataset-json]": true, "tests2/contract/test_contract.py::test_contract[GET /api/admin/image/{image_path}]": true, "tests2/contract/test_contract.py::test_contract[GET /api/annotator/dashboard]": true, "tests2/contract/test_contract.py::test_contract[GET /api/annotator/annotate]": true, "tests2/contract/test_contract.py::test_contract[GET /api/annotator/next-set]": true, "tests2/contract/test_contract.py::test_contract[GET /api/annotator/image/{image_path}]": true, "tests2/contract/test_contract.py::test_contract[POST /api/annotator/save-labels]": true, "tests2/contract/test_contract.py::test_contract[POST /api/NoteOCR/documents/]": true, "tests2/contract/test_contract.py::test_contract[GET /api/NoteOCR/documents/{document_id}]": true, "tests2/contract/test_contract.py::test_contract[DELETE /api/NoteOCR/documents/{document_id}]": true, "tests2/contract/test_contract.py::test_contract[POST /api/NoteOCR/images/upload]": true, "tests2/contract/test_contract.py::test_contract[POST /api/NoteOCR/images/extract-ocr]": true, "tests2/contract/test_contract.py::test_contract[POST /api/NoteOCR/images/chat]": true, "tests2/contract/test_contract.py::test_contract[POST /api/supervision/upload]": true, "tests2/contract/test_contract.py::test_contract[GET /api/supervision/review]": true, "tests2/contract/test_contract.py::test_contract[GET /api/supervision/check-status/{image_id}]": true, "tests2/contract/test_contract.py::test_contract[GET /api/supervision/document-image/{image_id}]": true, "tests2/contract/test_contract.py::test_contract[POST /api/supervision/save-document]": true, "tests2/contract/test_contract.py::test_contract[GET /api/supervision/download_csv/{image_id}]": true, "tests2/contract/test_contract.py::test_contract[GET /api/supervision/download_txt/{image_id}]": true, "tests2/contract/test_contract.py::test_contract[POST /api/supervision/list-drive-folders]": true, "tests2/contract/test_contract.py::test_contract[POST /api/supervision/download_csv]": true, "tests2/contract/test_contract.py::test_contract[GET /api/client/datasets]": true, "tests2/contract/test_contract.py::test_contract[GET /api/client/datasets/{dataset_id}]": true, "tests2/contract/test_contract.py::test_contract[GET /api/synthetic-dataset/models]": true, "tests2/contract/test_contract.py::test_contract[GET /api/synthetic-dataset/dataset-types]": true, "tests2/contract/test_contract.py::test_contract[POST /api/synthetic-dataset/generate]": true, "tests2/contract/test_contract.py::test_contract[POST /api/synthetic-dataset/generate-nonref]": true, "tests2/contract/test_contract.py::test_contract[GET /api/synthetic-dataset/knowledge-entries]": true, "tests2/contract/test_contract.py::test_contract[GET /api/knowledge-base/entries]": true, "tests2/contract/test_contract.py::test_contract[POST /api/knowledge-base/entries]": true, "tests2/contract/test_contract.py::test_contract[GET /api/knowledge-base/topics]": true, "tests2/contract/test_contract.py::test_contract[GET /api/knowledge-base/entries/{entry_id}]": true, "tests2/contract/test_contract.py::test_contract[PUT /api/knowledge-base/entries/{entry_id}]": true, "tests2/contract/test_contract.py::test_contract[DELETE /api/knowledge-base/entries/{entry_id}]": true, "tests2/integration/test_backup_restore.py::test_data_dump_and_restore": true, "tests2/integration/test_logging.py::test_query_metrics_logging": true, "tests2/integration/test_naming_conventions.py::test_table_and_column_naming_conventions": true, "tests2/integration/test_knowledge_base.py::test_entries_query_params_invalid": true, "tests2/unit/test_schemas.py::test_schema_valid[UserRegisterRequest-valid42-invalid42]": true, "tests2/unit/test_schemas.py::test_schema_valid[ChangePasswordRequest-valid43-invalid43]": true, "tests2/contract/test_contract.py::test_contract[GET /api/telegram/channels]": true, "tests2/contract/test_contract.py::test_contract[POST /api/telegram/disconnect]": true, "tests2/contract/test_contract.py::test_contract[POST /api/telegram/download-image]": true, "tests2/contract/test_contract.py::test_contract[POST /api/telegram/upload-to-drive]": true, "tests2/contract/test_contract.py::test_contract[GET /api/telegram/export-analytics-csv]": true, "tests2/integration/test_supervision.py::test_list_drive_folders_success": true, "tests2/integration/test_supervision.py::test_create_batch_from_folder": true, "tests2/integration/test_supervision.py::test_list_supervision_batches": true, "tests2/integration/test_supervision.py::test_get_batch_progress": true, "tests2/integration/test_supervision.py::test_download_batch_results": true, "tests2/integration/test_supervision.py::test_supervision_analytics": true, "tests2/integration/test_supervision.py::test_assign_batch_to_annotator": true, "tests2/integration/test_supervision.py::test_get_annotator_performance": true, "tests2/integration/test_supervision.py::test_long_running_task_status": true, "tests2/integration/test_supervision.py::test_cancel_long_running_task": true, "tests2/integration/test_supervision.py::test_supervision_real_time_updates": true, "tests2/integration/test_supervision.py::test_batch_quality_metrics": true, "tests2/integration/test_supervision.py::test_supervision_export_reports": true, "tests2/integration/test_supervision.py::test_supervision_unauthorized_access": true, "tests2/integration/test_telegram.py::test_check_auth_missing_session": true, "tests2/integration/test_concurrency.py::test_concurrent_file_upload_simulation": true}