import pytest
from main import app
from fastapi.testclient import TestClient

client = TestClient(app)

# Dynamically collect all routes from app
routes = [
    (route.path, list(route.methods))
    for route in app.routes
    if hasattr(route, 'methods') and route.methods
]

# Public routes that should work without authentication
public_routes = [
    '/api/auth/register',
    '/api/auth/login', 
    '/api/auth/refresh',
    '/api/auth/refresh-token',
    '/api/NoteOCR/images/extractor-mode',
    '/api/NoteOCR/documents/',
    '/api/NoteOCR/images/upload',
    '/api/NoteOCR/images/extract-ocr',
    '/api/NoteOCR/images/chat',
    '/docs',
    '/openapi.json',
    '/redoc'
]

@pytest.fixture(scope='module')
def auth_headers():
    """Create a test user and return auth headers for protected route testing."""
    try:
        # Register a test user
        payload = {
            "username": "smoketest", 
            "password": "SmokePass123!", 
            "confirm_password": "SmokePass123!", 
            "role": "annotator", 
            "full_name": "Smoke Test User", 
            "email": "<EMAIL>"
        }
        client.post('/api/auth/register', json=payload)
        
        # Login to get token
        login_resp = client.post('/api/auth/login', data={
            "username": "smoketest", 
            "password": "SmokePass123!"
        })
        
        if login_resp.status_code == 200:
            token = login_resp.json().get("access_token")
            return {"Authorization": f"Bearer {token}"}, dict(login_resp.cookies)
        else:
            return None, None
    except Exception:
        return None, None

@pytest.mark.parametrize('path,methods', routes)
def test_smoke_public_routes(path, methods):
    """Test public routes without authentication - should not return 500."""
    if path in public_routes:
        for method in methods:
            if method in ['GET', 'POST']:
                try:
                    if method == 'GET':
                        resp = client.get(path)
                    elif method == 'POST':
                        resp = client.post(path, json={})  # Empty body for POST
                    
                    # Public routes should not crash (no 500 errors)
                    # 503 is acceptable for infrastructure services being unavailable
                    assert resp.status_code < 500 or resp.status_code == 503, f"Public route {method} {path} crashed with {resp.status_code}"
                    
                    # Some public routes might return 400/422 for invalid data, which is fine
                    assert resp.status_code not in [500, 501, 502, 504], f"Server error on {method} {path}"
                    
                except Exception as e:
                    pytest.fail(f"Exception on public route {method} {path}: {e}")

@pytest.mark.parametrize('path,methods', routes)
def test_smoke_protected_routes(path, methods, auth_headers):
    """Test protected routes with authentication - should not return 500."""
    headers, cookies = auth_headers
    
    if path not in public_routes and headers:
        for method in methods:
            if method in ['GET', 'POST']:
                try:
                    if method == 'GET':
                        resp = client.get(path, headers=headers, cookies=cookies)
                    elif method == 'POST':
                        resp = client.post(path, json={}, headers=headers, cookies=cookies)
                    
                    # Protected routes should not crash (no 500 errors)
                    # They might return 400/403/404 for invalid data/permissions, which is fine
                    # 503 is acceptable for infrastructure services being unavailable
                    assert resp.status_code < 500 or resp.status_code == 503, f"Protected route {method} {path} crashed with {resp.status_code}"
                    
                except Exception as e:
                    pytest.fail(f"Exception on protected route {method} {path}: {e}")

def test_smoke_app_startup():
    """Test that the FastAPI app starts up correctly."""
    assert app is not None
    assert hasattr(app, 'routes')
    assert len(app.routes) > 0

def test_smoke_basic_endpoints():
    """Test that critical endpoints are accessible."""
    # Test OpenAPI docs
    resp = client.get('/docs')
    assert resp.status_code == 200
    
    # Test OpenAPI schema
    resp = client.get('/openapi.json')
    assert resp.status_code == 200
    
    # Test that we can at least reach the auth endpoints
    resp = client.post('/api/auth/register', json={})
    assert resp.status_code in [400, 422]  # Should fail validation, not crash

def test_smoke_route_discovery():
    """Test that route discovery is working correctly."""
    discovered_routes = [path for path, methods in routes]
    
    # Should find some core routes
    assert '/api/auth/register' in discovered_routes
    assert '/api/auth/login' in discovered_routes
    assert '/docs' in discovered_routes
    
    # Should have a reasonable number of routes
    assert len(discovered_routes) > 10, "Too few routes discovered"
    assert len(discovered_routes) < 200, "Too many routes discovered - check for duplicates" 