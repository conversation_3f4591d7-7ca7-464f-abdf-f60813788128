"""
Knowledge Base Routes.
Routes for accessing and managing knowledge base entries.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from db.db_connector import get_db_connection
from dependencies.auth import get_current_active_user, check_roles
from db.models.user import UserRole
from db.models.knowledge_base import KnowledgeEntry
from typing import List, Optional
from pydantic import BaseModel
import logging

logger = logging.getLogger('knowledge_base_routes')

router = APIRouter(
    prefix="/knowledge-base",
    tags=["Knowledge Base"],
    dependencies=[Depends(get_current_active_user), Depends(check_roles([UserRole.ADMIN]))],
)

class KnowledgeEntryResponse(BaseModel):
    """Schema for knowledge base entry response"""
    id: int
    title: str
    topic: str
    content: str
    source: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

class KnowledgeEntryCreate(BaseModel):
    """Schema for creating knowledge base entry"""
    title: str
    topic: str
    content: str
    source: Optional[str] = None

class KnowledgeEntryUpdate(BaseModel):
    """Schema for updating knowledge base entry"""
    title: Optional[str] = None
    topic: Optional[str] = None
    content: Optional[str] = None
    source: Optional[str] = None

@router.get("/entries", response_model=List[KnowledgeEntryResponse])
async def get_knowledge_entries(
    topic: Optional[str] = None,
    skip: int = 0, 
    limit: int = 100,
    db: Session = Depends(get_db_connection)
):
    """
    Get all knowledge base entries with optional filtering by topic.
    
    Parameters:
    - topic: Optional filter by topic
    - skip: Number of entries to skip (pagination)
    - limit: Maximum number of entries to return (pagination)
    """
    try:
        query = db.query(KnowledgeEntry)
        
        if topic:
            query = query.filter(KnowledgeEntry.topic == topic)
        
        entries = query.offset(skip).limit(limit).all()
        return [entry.to_dict() for entry in entries]
    except Exception as e:
        logger.error(f"Error fetching knowledge entries: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch knowledge entries: {str(e)}"
        )

@router.get("/topics", response_model=List[str])
async def get_knowledge_topics(db: Session = Depends(get_db_connection)):
    """
    Get all unique topics in the knowledge base.
    """
    try:
        # Using distinct to get unique topics
        topics = db.query(KnowledgeEntry.topic).distinct().all()
        return [topic[0] for topic in topics]
    except Exception as e:
        logger.error(f"Error fetching knowledge topics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch knowledge topics: {str(e)}"
        )

@router.get("/entries/{entry_id}", response_model=KnowledgeEntryResponse)
async def get_knowledge_entry(
    entry_id: int,
    db: Session = Depends(get_db_connection)
):
    """
    Get a specific knowledge base entry by ID.
    
    Parameters:
    - entry_id: ID of the knowledge base entry
    """
    try:
        entry = db.query(KnowledgeEntry).filter(KnowledgeEntry.id == entry_id).first()
        if not entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Knowledge entry with ID {entry_id} not found"
            )
        return entry.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching knowledge entry {entry_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch knowledge entry: {str(e)}"
        )

@router.post("/entries", response_model=KnowledgeEntryResponse, status_code=status.HTTP_201_CREATED)
async def create_knowledge_entry(
    entry: KnowledgeEntryCreate,
    db: Session = Depends(get_db_connection)
):
    """
    Create a new knowledge base entry.
    
    Parameters:
    - entry: Knowledge entry data
    """
    try:
        db_entry = KnowledgeEntry(
            title=entry.title,
            topic=entry.topic,
            content=entry.content,
            source=entry.source
        )
        db.add(db_entry)
        db.commit()
        db.refresh(db_entry)
        return db_entry.to_dict()
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating knowledge entry: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create knowledge entry: {str(e)}"
        )

@router.put("/entries/{entry_id}", response_model=KnowledgeEntryResponse)
async def update_knowledge_entry(
    entry_id: int,
    entry_update: KnowledgeEntryUpdate,
    db: Session = Depends(get_db_connection)
):
    """
    Update an existing knowledge base entry.
    
    Parameters:
    - entry_id: ID of the knowledge base entry to update
    - entry_update: Knowledge entry data to update
    """
    try:
        db_entry = db.query(KnowledgeEntry).filter(KnowledgeEntry.id == entry_id).first()
        if not db_entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Knowledge entry with ID {entry_id} not found"
            )
        
        # Update fields that are provided
        update_data = entry_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_entry, key, value)
        
        db.commit()
        db.refresh(db_entry)
        return db_entry.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating knowledge entry {entry_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update knowledge entry: {str(e)}"
        )

@router.delete("/entries/{entry_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_knowledge_entry(
    entry_id: int,
    db: Session = Depends(get_db_connection)
):
    """
    Delete a knowledge base entry.
    
    Parameters:
    - entry_id: ID of the knowledge base entry to delete
    """
    try:
        db_entry = db.query(KnowledgeEntry).filter(KnowledgeEntry.id == entry_id).first()
        if not db_entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Knowledge entry with ID {entry_id} not found"
            )
        
        db.delete(db_entry)
        db.commit()
        return None
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting knowledge entry {entry_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete knowledge entry: {str(e)}"
        ) 