/**
 * REAL API Integration Tests - Auditor Endpoints
 * Tests actual API calls to FastAPI backend for auditor functionality
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'

// Helper functions
async function makeAuthenticatedRequest(
  endpoint: string, 
  options: RequestInit = {}, 
  authToken?: string
): Promise<Response> {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...options.headers,
  }

  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
    credentials: 'include',
  })

  return response
}

async function loginUser(username: string, password: string): Promise<string | null> {
  try {
    const response = await makeAuthenticatedRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password })
    })

    if (response.ok) {
      const data = await response.json()
      return data.access_token || null
    }
    return null
  } catch (error) {
    console.error('Login failed:', error)
    return null
  }
}

const TEST_AUDITOR = {
  username: 'test_auditor',
  password: 'test_password_123'
}

describe('REAL API Integration Tests - Auditor Endpoints', () => {
  jest.setTimeout(30000)
  let auditorToken: string

  beforeAll(async () => {
    // Login as auditor for all tests
    auditorToken = await loginUser(TEST_AUDITOR.username, TEST_AUDITOR.password) || ''
    expect(auditorToken).toBeTruthy()
  })

  describe('GET /auditor/modes', () => {
    it('should fetch available audit modes', async () => {
      const response = await makeAuthenticatedRequest('/auditor/modes', {
        method: 'GET'
      }, auditorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
      expect(data).toContain('annotation')
      expect(data).toContain('verification')
    })
  })

  describe('GET /auditor/datasets', () => {
    it('should fetch available datasets for audit', async () => {
      const response = await makeAuthenticatedRequest('/auditor/datasets', {
        method: 'GET'
      }, auditorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
      if (data.length > 0) {
        expect(data[0]).toHaveProperty('dataset_name')
        expect(data[0]).toHaveProperty('dataset_image_path')
      }
    })
  })

  describe('POST /auditor/select-dataset', () => {
    it('should select dataset for auditing', async () => {
      // First get available datasets
      const datasetsResponse = await makeAuthenticatedRequest('/auditor/datasets', {
        method: 'GET'
      }, auditorToken)
      
      expect(datasetsResponse.status).toBe(200)
      const datasets = await datasetsResponse.json()
      
      if (datasets.length > 0) {
        const selectedDataset = {
          dataset_name: datasets[0].dataset_name,
          mode: 'annotation'
        }

        const response = await makeAuthenticatedRequest('/auditor/select-dataset', {
          method: 'POST',
          body: JSON.stringify(selectedDataset)
        }, auditorToken)

        expect(response.status).toBe(200)
        
        const data = await response.json()
        expect(data).toHaveProperty('success', true)
        expect(data).toHaveProperty('dataset_name', selectedDataset.dataset_name)
      }
    })
  })

  describe('GET /auditor/audit-data', () => {
    it('should fetch audit data for selected dataset', async () => {
      const response = await makeAuthenticatedRequest('/auditor/audit-data', {
        method: 'GET'
      }, auditorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('images')
      expect(data).toHaveProperty('labels')
      expect(Array.isArray(data.images)).toBe(true)
    })
  })

  describe('POST /auditor/submit-audit', () => {
    it('should submit audit results', async () => {
      // First get audit data
      const auditDataResponse = await makeAuthenticatedRequest('/auditor/audit-data', {
        method: 'GET'
      }, auditorToken)
      
      expect(auditDataResponse.status).toBe(200)
      const auditData = await auditDataResponse.json()
      
      // Submit audit results
      const auditResults = {
        audit_results: {
          'image1.jpg': {
            'quality_score': 8.5,
            'issues': [],
            'approved': true,
            'comments': 'Good quality annotation'
          }
        }
      }

      const response = await makeAuthenticatedRequest('/auditor/submit-audit', {
        method: 'POST',
        body: JSON.stringify(auditResults)
      }, auditorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })
  })

  describe('GET /auditor/history', () => {
    it('should fetch audit history', async () => {
      const response = await makeAuthenticatedRequest('/auditor/history', {
        method: 'GET'
      }, auditorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
      if (data.length > 0) {
        expect(data[0]).toHaveProperty('audit_id')
        expect(data[0]).toHaveProperty('dataset_name')
        expect(data[0]).toHaveProperty('audit_date')
        expect(data[0]).toHaveProperty('status')
      }
    })
  })

  describe('GET /auditor/statistics', () => {
    it('should fetch audit statistics', async () => {
      const response = await makeAuthenticatedRequest('/auditor/statistics', {
        method: 'GET'
      }, auditorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('total_audits')
      expect(data).toHaveProperty('approved_count')
      expect(data).toHaveProperty('rejected_count')
      expect(data).toHaveProperty('average_quality_score')
      expect(typeof data.total_audits).toBe('number')
    })
  })

  describe('POST /auditor/export-results', () => {
    it('should export audit results', async () => {
      const exportRequest = {
        dataset_name: 'test_dataset',
        format: 'csv',
        date_range: {
          start_date: '2024-01-01',
          end_date: '2024-12-31'
        }
      }

      const response = await makeAuthenticatedRequest('/auditor/export-results', {
        method: 'POST',
        body: JSON.stringify(exportRequest)
      }, auditorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('download_url')
      expect(data).toHaveProperty('file_name')
    })
  })

  describe('Error Handling', () => {
    it('should handle unauthorized access', async () => {
      const response = await makeAuthenticatedRequest('/auditor/modes', {
        method: 'GET'
      }, 'invalid_token')

      expect(response.status).toBe(401)
    })

    it('should handle invalid dataset selection', async () => {
      const invalidDataset = {
        dataset_name: 'non_existent_dataset',
        mode: 'invalid_mode'
      }

      const response = await makeAuthenticatedRequest('/auditor/select-dataset', {
        method: 'POST',
        body: JSON.stringify(invalidDataset)
      }, auditorToken)

      expect(response.status).toBe(400)
    })
  })
})
