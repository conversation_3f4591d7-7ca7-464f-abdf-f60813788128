"""
Authentication service module.
This module contains business logic for authentication operations.
"""
from typing import <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>
from sqlalchemy.orm import Session

from core.security import (
    hash_password,
    verify_password,
    create_access_token,
    create_refresh_token,
)
from db.models.user import UserRole
from schemas.UserSchemas import (
    UserRegisterRequest,
    LoginRequest,
    ChangePasswordRequest,
    UserResponse,
    TokenResponse,
    UserCreate,
)
from dependencies.auth import UserService, create_user_response


class AuthService:
    """Service for authentication operations"""

    @staticmethod
    def register_user(db: Session, user_data: UserRegisterRequest) -> Tuple[bool, Union[UserResponse, str]]:
        """
        Register a new user

        Args:
            db: Database session
            user_data: User registration data

        Returns:
            Tuple containing success status and either UserResponse object or error message
        """
        user_service = UserService(db)
        # Check for existing username or email
        if user_service.get_user_by_username(user_data.username):
            return False, "<PERSON><PERSON><PERSON> already registered"
        if user_service.get_user_by_email(user_data.email):
            return False, "<PERSON>ail already registered"
        # Validate role
        try:
            user_role = UserRole(user_data.role)
        except ValueError:
            return False, f"Invalid role. Must be one of: {', '.join([r.value for r in UserRole])}"
        # Prepare data and create user
        new_user_data = UserCreate(
            username=user_data.username,
            password_hash=hash_password(user_data.password),
            email=user_data.email,
            full_name=user_data.full_name,
            role=user_role
        )
        try:
            new_user = user_service.create_user(new_user_data)
            return True, new_user
        except Exception as e:
            return False, f"Error creating user: {str(e)}"

    @staticmethod
    def authenticate_user(db: Session, login_data: LoginRequest) -> Optional[UserResponse]:
        """
        Authenticate a user with username and password

        Args:
            db: Database session
            login_data: Login credentials

        Returns:
            UserResponse object if authentication successful, None otherwise
        """
        user_service = UserService(db)
        user = user_service.get_user_by_username(login_data.username)
        if not user or not verify_password(login_data.password, user.password_hash):
            return None
        # Update last login timestamp
        updated_user = user_service.update_last_login(user)
        # Return response schema
        return create_user_response(updated_user)

    @staticmethod
    def generate_tokens(username: str) -> TokenResponse:
        """
        Generate access and refresh tokens for a user

        Args:
            username: Username

        Returns:
            TokenResponse object containing access and refresh tokens
        """
        access_token = create_access_token(data={"sub": username})
        refresh_token = create_refresh_token(data={"sub": username})
        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            user=None
        )

    @staticmethod
    def change_password(db: Session, username: str, password_data: ChangePasswordRequest) -> Tuple[bool, str]:
        """
        Change user password

        Args:
            db: Database session
            username: Username
            password_data: Password change data

        Returns:
            Tuple containing success status and message
        """
        user_service = UserService(db)
        user = user_service.get_user_by_username(username)
        if not user:
            return False, "User not found"
        if not verify_password(password_data.current_password, user.password_hash):
            return False, "Current password is incorrect"
        user_service.update_user_password(user, hash_password(password_data.new_password))
        return True, "Password changed successfully"

    @staticmethod
    def get_user_profile(db: Session, username: str) -> Optional[UserResponse]:
        """
        Get user profile

        Args:
            db: Database session
            username: Username

        Returns:
            UserResponse object if user exists, None otherwise
        """
        user_service = UserService(db)
        user = user_service.get_user_by_username(username)
        if not user:
            return None
        return create_user_response(user)
