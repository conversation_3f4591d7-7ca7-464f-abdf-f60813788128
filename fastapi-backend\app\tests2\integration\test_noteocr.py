import pytest
import io
from main import app
from fastapi.testclient import TestClient

client = TestClient(app)

def test_extractor_mode():
    resp = client.get('/api/NoteOCR/images/extractor-mode')
    assert resp.status_code == 200
    assert resp.json().get('success')

def test_list_documents():
    resp = client.get('/api/NoteOCR/documents/')
    assert resp.status_code == 200
    assert isinstance(resp.json(), list)

def test_not_found_document():
    resp = client.get('/api/NoteOCR/documents/9999')
    assert resp.status_code in (404, 500)

def test_pdf_upload_get_delete():
    # Upload PDF
    pdf_bytes = b'%PDF-1.4\n%Fake PDF\n' + b'0' * 1000
    files = {'file': ('test.pdf', io.BytesIO(pdf_bytes), 'application/pdf')}
    resp = client.post('/api/NoteOCR/documents/', files=files)
    # may succeed or error during processing
    assert resp.status_code in (200, 500)
    if resp.status_code != 200:
        # Server error: nothing further to check
        return
    doc = resp.json()
    doc_id = doc.get("id")
    assert doc_id is not None
    # Get document
    resp = client.get(f'/api/NoteOCR/documents/{doc_id}')
    assert resp.status_code == 200
    assert resp.json().get("id") == doc_id
    # Delete document
    resp = client.delete(f'/api/NoteOCR/documents/{doc_id}')
    assert resp.status_code == 200
    # Confirm deletion
    resp = client.get(f'/api/NoteOCR/documents/{doc_id}')
    assert resp.status_code in (404, 500)

def test_image_upload_extract_ocr_chat():
    # /images/upload
    img_bytes = b'\x89PNG\r\n\x1a\n' + b'0' * 100
    files = {'image': ('test.png', io.BytesIO(img_bytes), 'image/png')}
    resp = client.post('/api/NoteOCR/images/upload', files=files)
    assert resp.status_code in (200, 400, 500)
    # /images/extract-ocr (no prompt)
    payload = {"image_id": "fakeid"}
    resp = client.post('/api/NoteOCR/images/extract-ocr', json=payload)
    assert resp.status_code in (200, 400, 404, 502)
    # /images/extract-ocr (with prompt)
    payload = {"image_id": "fakeid", "prompt": "Extract text"}
    resp = client.post('/api/NoteOCR/images/extract-ocr', json=payload)
    assert resp.status_code in (200, 400, 404, 502)
    # /images/chat
    payload = {"image_id": "fakeid", "query": "What is this?"}
    resp = client.post('/api/NoteOCR/images/chat', json=payload)
    assert resp.status_code in (200, 400, 404, 500, 502)
    # /images/chat with optional chat_history
    payload_with_history = {
        "image_id": "fakeid",
        "query": "What is this?",
        "chat_history": [{"role": "user", "message": "Hello"}]
    }
    resp_hist = client.post('/api/NoteOCR/images/chat', json=payload_with_history)
    assert resp_hist.status_code in (200, 400, 404, 500, 502)

def test_extract_ocr_requires_body():
    """POST /api/NoteOCR/images/extract-ocr without required fields returns 422."""
    resp = client.post('/api/NoteOCR/images/extract-ocr', json={})
    assert resp.status_code == 422

def test_chat_requires_fields():
    """POST /api/NoteOCR/images/chat without required fields returns 422."""
    resp = client.post('/api/NoteOCR/images/chat', json={})
    assert resp.status_code == 422


def test_upload_large_file():
    """Test that large file uploads (>16MB) are properly rejected with appropriate error codes."""
    big_pdf = b'%PDF-1.4\n' + b'0' * (17 * 1024 * 1024)  # >16MB
    files = {'file': ('big.pdf', io.BytesIO(big_pdf), 'application/pdf')}
    resp = client.post('/api/NoteOCR/documents/', files=files)
    assert resp.status_code in (400, 413, 422, 500) 