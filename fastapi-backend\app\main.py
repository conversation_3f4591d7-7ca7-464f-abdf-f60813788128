"""
Main application module.
Entry point for the FastAPI application.
"""
from fastapi import FastAP<PERSON>, Request, HTTPException, status
from fastapi.responses import RedirectResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exception_handlers import http_exception_handler
from contextlib import asynccontextmanager
from starlette.middleware.sessions import SessionMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException
import os
import pickle
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

from routes import auth_routes
from routes.auditor_routes import router as auditor_router
from routes.admin_routes import router as admin_router
from routes.annotator_routes import router as annotator_router
from routes.NoteOCR_routes import router as NoteOCR_router
from routes.annotator_supervision_routes import router as annotator_supervision_router
from routes.client_routes import router as client_router
from routes.synthetic_dataset_routes import router as synthetic_dataset_router
from routes.knowledge_base_routes import router as knowledge_base_router
from routes.telegram_fetch_data_routes import router as telegram_router

from core.config import settings
from cache.redis_connector import create_redis_client, set_redis_client, set_redis_enabled
from utils.google_drive.google_auth import OAuthConfig, SCOPES, TOKEN_PICKLE_FILE
from cache.admin_cache import invalidate_stats_cache
from google_auth_oauthlib.flow import Flow

from core.logging import configure_app_logging
logger = configure_app_logging(
    console_for_root=settings.debug,
    console_for_modules=settings.debug
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Startup event for application resources (e.g., caching)"""

    if settings.redis_settings.enabled:
        try:
            client = await create_redis_client()
            if client:
                set_redis_client(client)
                logger.info("Async Redis client initialized for caching")
            else:
                set_redis_enabled(False)
                logger.warning("Async Redis client not available, caching disabled")
        except Exception as e:
            set_redis_enabled(False)
            logger.error(f"Error initializing async Redis client: {e}")
    
    # Log API keys availability
    if os.environ.get("GEMINI_API_KEY"):
        logger.info("GEMINI_API_KEY is available")
    else:
        logger.warning("GEMINI_API_KEY is not set. Synthetic dataset generation will not work properly.")
    
    yield

app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    description="Backend API for the application",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_settings.allow_origins,
    allow_credentials=settings.cors_settings.allow_credentials,
    allow_methods=settings.cors_settings.allow_methods,
    allow_headers=settings.cors_settings.allow_headers,
)

app.add_middleware(SessionMiddleware, secret_key=settings.jwt_settings.secret_key)

@app.exception_handler(StarletteHTTPException)
async def custom_http_exception_handler(request: Request, exc: StarletteHTTPException):
    response = await http_exception_handler(request, exc)
    response.headers["Access-Control-Allow-Origin"] = "http://localhost:3000"
    response.headers["Access-Control-Allow-Credentials"] = "true"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Authorization, Content-Type, Accept, Origin, User-Agent, DNT, Cache-Control, X-Mx-ReqToken, Keep-Alive, X-Requested-With, If-Modified-Since"
    return response

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    response = JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )
    response.headers["Access-Control-Allow-Origin"] = "http://localhost:3000"
    response.headers["Access-Control-Allow-Credentials"] = "true"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Authorization, Content-Type, Accept, Origin, User-Agent, DNT, Cache-Control, X-Mx-ReqToken, Keep-Alive, X-Requested-With, If-Modified-Since"
    logger.error(f"Unhandled exception: {exc}")
    return response

@app.get("/admin/google-drive-callback")
async def google_drive_callback(code: str = None, state: str = None, request: Request = None):
    """OAuth callback: handle Google's redirect and exchange code for tokens"""
    try:
        if not code:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No authorization code received")
        cfg = OAuthConfig(request)
        
        granted_scopes = request.query_params.get('scope', '').split()
        if granted_scopes:
            effective_scopes = granted_scopes
        else:
            effective_scopes = SCOPES
            
        flow = Flow.from_client_secrets_file(
            cfg.client_secret_file, 
            scopes=effective_scopes, 
            redirect_uri=str(request.url_for("google_drive_callback"))
        )
        
        try:
            flow.fetch_token(code=code)
        except Exception as fetch_error:
            logger.warning(f"First token fetch failed: {fetch_error}. Retrying with original scopes.")
            flow = Flow.from_client_secrets_file(
                cfg.client_secret_file, 
                scopes=SCOPES, 
                redirect_uri=str(request.url_for("google_drive_callback"))
            )
            flow._scopes = None
            flow.fetch_token(code=code)
            
        creds = flow.credentials
        with open(TOKEN_PICKLE_FILE, "wb") as token_file:
            pickle.dump(creds, token_file)
        await invalidate_stats_cache("google_drive_connection")
        status_str = "success"
        logger.info("Google Drive OAuth callback completed successfully")
    except Exception as e:
        logger.error(f"Error in Google Drive callback: {e}")
        status_str = "error"
    frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
    redirect_url = f"{frontend_url}/admin?view=dashboard&driveStatus={status_str}"
    return RedirectResponse(url=redirect_url)

app.include_router(auth_routes.router, prefix=settings.api_prefix)
app.include_router(auditor_router, prefix=settings.api_prefix)
app.include_router(admin_router, prefix=settings.api_prefix)
app.include_router(annotator_router, prefix=settings.api_prefix)
app.include_router(NoteOCR_router, prefix=settings.api_prefix)
app.include_router(annotator_supervision_router, prefix=settings.api_prefix)
app.include_router(client_router, prefix=settings.api_prefix)
app.include_router(synthetic_dataset_router, prefix=settings.api_prefix)
app.include_router(knowledge_base_router, prefix=settings.api_prefix)
app.include_router(telegram_router, prefix=settings.api_prefix)

@app.get("/", tags=["Root"])
async def root():
    """Root endpoint - API information"""
    return {
        "app_name": settings.app_name,
        "version": settings.version,
        "docs_url": "/docs",
    }

if __name__ == "__main__":
    import uvicorn

    mode = "debug" if settings.debug else "production"
    logger.info(f"Starting {settings.app_name} v{settings.version} in {mode} mode")
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="debug" if settings.debug else "info",
        access_log=settings.debug,
        forwarded_allow_ips=os.environ.get("FORWARDED_ALLOW_IPS", "*"),
        workers=int(os.environ.get("WORKERS", 1))
    )
