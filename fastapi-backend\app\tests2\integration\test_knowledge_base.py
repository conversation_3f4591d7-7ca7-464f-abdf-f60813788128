import pytest
from main import app
from fastapi.testclient import TestClient
from db import db_connector
from db.models.knowledge_base import KnowledgeEntry

client = TestClient(app)

@pytest.fixture(scope='module')
def admin_token():
    payload = {"username": "kbadmin", "password": "Kbpass123!", "confirm_password": "Kbpass123!", "role": "admin", "full_name": "KB Admin", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=payload)
    resp = client.post('/api/auth/login', data={"username": "kbadmin", "password": "Kbpass123!"})
    return resp.cookies

@pytest.fixture(scope='module')
def annotator_token():
    """Authenticated as annotator for KB RBAC testing"""
    payload = {"username": "kbaudit", "password": "KbAtt123!", "confirm_password": "KbAtt123!", "role": "annotator", "full_name": "<PERSON><PERSON> Audit", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=payload)
    resp = client.post('/api/auth/login', data={"username": payload["username"], "password": payload["password"]})
    assert resp.status_code == 200
    return resp.cookies

def test_create_entry(admin_token):
    payload = {"title": "Test KB", "topic": "test", "content": "Test content"}
    resp = client.post('/api/knowledge-base/entries', json=payload, cookies=admin_token)
    assert resp.status_code == 201
    entry_id = resp.json()["id"]
    # Fetch via API to verify creation
    get_resp = client.get(f'/api/knowledge-base/entries/{entry_id}', cookies=admin_token)
    assert get_resp.status_code == 200
    data = get_resp.json()
    assert data.get("title") == "Test KB"
    assert data.get("content") == "Test content"
    # Update entry via API
    update_resp = client.put(
        f'/api/knowledge-base/entries/{entry_id}',
        json={"content": "Updated"},
        cookies=admin_token
    )
    assert update_resp.status_code == 200
    updated = update_resp.json()
    assert updated.get("content") == "Updated"
    # Delete entry via API
    del_resp = client.delete(f'/api/knowledge-base/entries/{entry_id}', cookies=admin_token)
    assert del_resp.status_code == 204
    # Confirm deletion
    confirm = client.get(f'/api/knowledge-base/entries/{entry_id}', cookies=admin_token)
    assert confirm.status_code == 404

def test_title_content_length_boundaries(admin_token):
    # Min title/content
    payload = {"title": "a", "topic": "t", "content": "b"}
    resp = client.post('/api/knowledge-base/entries', json=payload, cookies=admin_token)
    assert resp.status_code in (201, 400)
    # Max title/content (256)
    payload["title"] = "t" * 256
    payload["content"] = "c" * 256
    resp = client.post('/api/knowledge-base/entries', json=payload, cookies=admin_token)
    assert resp.status_code in (201, 400)
    # Above max (257)
    payload["title"] = "t" * 257
    payload["content"] = "c" * 257
    resp = client.post('/api/knowledge-base/entries', json=payload, cookies=admin_token)
    # No enforced max-length, so may still succeed
    assert resp.status_code in (201, 400)

def test_topics(admin_token):
    # No topics present
    resp = client.get('/api/knowledge-base/topics', cookies=admin_token)
    assert resp.status_code == 200
    assert isinstance(resp.json(), list)
    # Create a new entry via API to seed topic
    create_resp = client.post(
        '/api/knowledge-base/entries',
        json={"title": "T2", "topic": "topic2", "content": "c"},
        cookies=admin_token
    )
    assert create_resp.status_code == 201
    # Check topics again
    resp2 = client.get('/api/knowledge-base/topics', cookies=admin_token)
    assert resp2.status_code == 200
    assert "topic2" in resp2.json()

def test_list_entries(admin_token):
    # Create two entries with distinct topics
    p1 = {"title": "EntryList1", "topic": "list", "content": "Content1"}
    p2 = {"title": "EntryList2", "topic": "filter", "content": "Content2"}
    id1 = client.post('/api/knowledge-base/entries', json=p1, cookies=admin_token).json().get('id')
    id2 = client.post('/api/knowledge-base/entries', json=p2, cookies=admin_token).json().get('id')
    # List all entries
    resp_all = client.get('/api/knowledge-base/entries', cookies=admin_token)
    assert resp_all.status_code == 200
    entries_all = resp_all.json()
    assert isinstance(entries_all, list)
    assert any(e.get('id') == id1 for e in entries_all)
    assert any(e.get('id') == id2 for e in entries_all)
    # Filter by topic 'list'
    resp_filter = client.get('/api/knowledge-base/entries?topic=list', cookies=admin_token)
    assert resp_filter.status_code == 200
    entries_filter = resp_filter.json()
    assert all(e.get('topic') == 'list' for e in entries_filter)
    # Test pagination with skip and limit
    resp_page = client.get('/api/knowledge-base/entries?skip=1&limit=1', cookies=admin_token)
    assert resp_page.status_code == 200
    entries_page = resp_page.json()
    assert isinstance(entries_page, list)
    assert len(entries_page) == 1

def test_update_nonexistent_entry(admin_token):
    # Attempt to update an entry that does not exist
    resp = client.put(
        '/api/knowledge-base/entries/99999',
        json={'content': 'NoSuch'},
        cookies=admin_token
    )
    assert resp.status_code == 404

def test_delete_nonexistent_entry(admin_token):
    # Attempt to delete an entry that does not exist
    resp = client.delete('/api/knowledge-base/entries/99999', cookies=admin_token)
    assert resp.status_code == 404

def test_knowledge_base_rbac_for_all_endpoints(annotator_token):
    """All KB endpoints should require admin role: unauthenticated->401, annotator->403"""
    client_no_auth = TestClient(app)
    sample_id = 9999
    endpoints = [
        ('get', '/api/knowledge-base/entries', {}),
        ('get', '/api/knowledge-base/topics', {}),
        ('get', f'/api/knowledge-base/entries/{sample_id}', {}),
        ('post', '/api/knowledge-base/entries', {'json': {'title': 'T', 'topic': 't', 'content': 'c'}}),
        ('put', f'/api/knowledge-base/entries/{sample_id}', {'json': {'content': 'u'}}),
        ('delete', f'/api/knowledge-base/entries/{sample_id}', {})
    ]
    for method, path, kwargs in endpoints:
        resp_unauth = getattr(client_no_auth, method)(path, **kwargs)
        assert resp_unauth.status_code == 401, f"Unauth {path} returned {resp_unauth.status_code}"
        resp_forbid = getattr(client, method)(path, cookies=annotator_token, **kwargs)
        assert resp_forbid.status_code == 403, f"Forbidden {path} returned {resp_forbid.status_code}"

def test_entries_combined_filters(admin_token):
    """GET entries with topic filter plus skip and limit should return correct subset"""
    # Create entries for two topics
    payload1 = {"title": "X1", "topic": "x", "content": "c"}
    payload2 = {"title": "Y1", "topic": "y", "content": "c"}
    id1 = client.post('/api/knowledge-base/entries', json=payload1, cookies=admin_token).json().get('id')
    id2 = client.post('/api/knowledge-base/entries', json=payload2, cookies=admin_token).json().get('id')
    # Filter by topic 'x', skip=0, limit=1
    resp = client.get('/api/knowledge-base/entries?topic=x&skip=0&limit=1', cookies=admin_token)
    assert resp.status_code == 200
    data = resp.json()
    assert isinstance(data, list)
    assert len(data) <= 1
    assert all(e.get('topic') == 'x' for e in data)


def test_bulk_insert_and_filter_pagination(admin_token):
    """Bulk insert entries, filter by topic, and test pagination."""
    # Bulk insert 15 entries with different topics
    for i in range(15):
        topic = "bulk_A" if i < 8 else "bulk_B"
        payload = {"title": f"BulkTitle{i}", "topic": topic, "content": f"bulk_content_{i}"}
        resp = client.post("/api/knowledge-base/entries", json=payload, cookies=admin_token)
        assert resp.status_code == 201
    
    # Filter by topic bulk_A (should have 8 entries)
    resp_a = client.get("/api/knowledge-base/entries?topic=bulk_A", cookies=admin_token)
    assert resp_a.status_code == 200
    data_a = resp_a.json()
    assert all(e["topic"] == "bulk_A" for e in data_a)
    assert len(data_a) == 8
    
    # Test larger pagination (skip=5, limit=5)
    resp_page = client.get("/api/knowledge-base/entries?skip=5&limit=5", cookies=admin_token)
    assert resp_page.status_code == 200
    data_page = resp_page.json()
    assert len(data_page) == 5


def test_partial_update_kb_entry(admin_token):
    """Partial updates only modify provided fields, leaving others unchanged."""
    # Create initial entry
    payload = {"title": "OriginalTitle", "topic": "OriginalTopic", "content": "OriginalContent"}
    resp = client.post("/api/knowledge-base/entries", json=payload, cookies=admin_token)
    assert resp.status_code == 201
    entry = resp.json()
    entry_id = entry["id"]
    
    # Update only the content field
    resp2 = client.put(f"/api/knowledge-base/entries/{entry_id}", json={"content": "UpdatedContent"}, cookies=admin_token)
    assert resp2.status_code == 200
    updated = resp2.json()
    
    # Verify only content changed, title and topic remain the same
    assert updated["title"] == "OriginalTitle"
    assert updated["topic"] == "OriginalTopic"
    assert updated["content"] == "UpdatedContent" 