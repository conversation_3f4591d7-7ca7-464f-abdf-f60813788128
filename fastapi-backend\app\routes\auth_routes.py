from fastapi import APIRouter, Depends, HTTPException, status, Response, Cookie, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from services.auth_service import AuthService
from core.security import verify_token, create_access_token
from dependencies.auth import (
    get_current_user, 
    get_user_service, 
    UserService
)
from db.db_connector import get_db_connection as get_db
from core.config import settings
from schemas.UserSchemas import (
    UserRegisterRequest,
    LoginRequest,
    ChangePasswordRequest,
    RefreshTokenRequest,
    UserResponse,
    TokenResponse,
    AccessTokenResponse,
    SuccessResponse,
    ErrorResponse
)


# Create router
router = APIRouter(
    prefix="/auth",
    tags=["Authentication"],
    responses={401: {"description": "Unauthorized"}}
)


@router.post("/refresh-token", response_model=AccessTokenResponse, responses={401: {"model": ErrorResponse}})
async def refresh_token(
    token_request: RefreshTokenRequest,
    db: Session = Depends(get_db),
    user_service: UserService = Depends(get_user_service)
):
    """Get a new access token using refresh token"""
    payload = verify_token(token_request.refresh_token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
            headers={"WWW-Authenticate": "Bearer"}
        )

    # Check token type
    if payload.get("token_type") != "refresh":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token type",
            headers={"WWW-Authenticate": "Bearer"}
        )

    # Get username from token
    username = payload.get("sub")
    if not username:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"}
        )

    # Check if user exists
    user = user_service.get_user_by_username(username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"}
        )

    # Create new access token
    access_token = create_access_token(data={"sub": username})

    # Return access token response
    return AccessTokenResponse(
        access_token=access_token,
        token_type="bearer"
    )

@router.post("/register", response_model=SuccessResponse, responses={400: {"model": ErrorResponse}, 500: {"model": ErrorResponse}})
async def register_user(user_data: UserRegisterRequest, db: Session = Depends(get_db)):
    """Register a new user"""
    success, result = AuthService.register_user(db, user_data)
    if not success:
        if "Invalid role" in result or "already registered" in result:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=result)
        else:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=result)
    return SuccessResponse(success=True, message="User registered successfully", data={"user_id": result.id})

@router.post("/login", response_model=TokenResponse, responses={401: {"model": ErrorResponse}})
async def login(response: Response, form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """Login and get access token"""
    login_req = LoginRequest(username=form_data.username, password=form_data.password)
    user = AuthService.authenticate_user(db, login_req)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"}
        )
    tokens = AuthService.generate_tokens(user.username)
    tokens.user = user
    
    # Set secure cookies
    response.set_cookie(
        key="access_token",
        value=tokens.access_token,
        httponly=True,
        secure=False,  # Set to True in production with HTTPS
        samesite="lax",
        max_age=60 * settings.jwt_settings.access_token_expire_minutes,
        path="/"  # Cookie path for client/server
    )
    response.set_cookie(
        key="refresh_token",
        value=tokens.refresh_token,
        httponly=True,
        secure=False,  # Set to True in production with HTTPS
        samesite="lax",
        max_age=60 * 60 * 24 * settings.jwt_settings.refresh_token_expire_days,
        path="/"  # Cookie path for client/server
    )
    
    return tokens

@router.post("/logout")
async def logout(response: Response):
    """Logout and clear cookies"""
    response.delete_cookie(key="access_token", path="/")
    response.delete_cookie(key="refresh_token", path="/")
    return {"message": "Successfully logged out"}

@router.post("/refresh")
async def refresh_access_token(
    response: Response,
    refresh_token: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    """Refresh access token using refresh token from cookie"""
    if not refresh_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Refresh token missing"
        )
        
    try:
        payload = verify_token(refresh_token)
        if payload is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
            
        if payload.get("token_type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type. Refresh token required."
            )
            
        username = payload.get("sub")
        user_service = UserService(db)
        user = user_service.get_user_by_username(username)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
            
        # Generate new tokens
        tokens = AuthService.generate_tokens(username)
        
        # Set new cookies
        response.set_cookie(
            key="access_token",
            value=tokens.access_token,
            httponly=True,
            secure=False,  # Set to True in production with HTTPS
            samesite="lax",
            max_age=60 * settings.jwt_settings.access_token_expire_minutes,
            path="/"  # Cookie path for client/server
        )
        response.set_cookie(
            key="refresh_token",
            value=tokens.refresh_token,
            httponly=True,
            secure=False,  # Set to True in production with HTTPS
            samesite="lax",
            max_age=60 * 60 * 24 * settings.jwt_settings.refresh_token_expire_days,
            path="/"  # Cookie path for client/server
        )
        
        return {"message": "Tokens refreshed successfully"}
        
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )

@router.get("/verify")
async def verify_auth(
    current_user: Dict[str, Any] = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Verify authentication status and return user info"""
    user = user_service.get_user_by_username(current_user["sub"])
    return {
        "authenticated": True,
        "user": {
            "username": user.username,
            "full_name": user.full_name,
            "role": user.role.value if hasattr(user.role, 'value') else str(user.role),
            "annotator_mode": user.annotator_mode if hasattr(user, 'annotator_mode') else None
        }
    }

@router.post("/change-password", response_model=SuccessResponse, responses={400: {"model": ErrorResponse}})
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Change user password"""
    success, message = AuthService.change_password(
        db=db,
        username=current_user["sub"],
        password_data=password_data
    )
    if not success:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=message)
    return SuccessResponse(success=True, message=message)

@router.get("/me", response_model=UserResponse, responses={404: {"model": ErrorResponse}})
async def get_user_profile(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user profile"""
    profile = AuthService.get_user_profile(db, current_user["sub"])
    if not profile:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return profile
