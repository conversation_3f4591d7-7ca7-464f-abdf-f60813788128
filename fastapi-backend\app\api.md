> **Note:** All API endpoints below are exposed under the `/api` prefix (e.g. `/api/auth/login`).
> The Google Drive OAuth callback endpoint (`/admin/google-drive-callback`) is **not** under `/api`.

---

## Authentication Routes (`/api/auth`)

| Method | Endpoint                    | Description                                  | Permission                     | Auth Type                   | Status Codes  | Bug Analysis / Notes                                                |
| ------ | --------------------------- | -------------------------------------------- | ------------------------------ | --------------------------- | ------------- | ------------------------------------------------------------------- |
| POST   | `/api/auth/refresh-token`   | Get a new access token using a refresh token | Public (refresh token in body) | Bearer Token (in JSON body) | 200, 401, 422 | Working as expected; 422 if body missing                            |
| POST   | `/api/auth/register`        | Register a new user                          | Public                         | None                        | 200, 400, 500 | Working as expected                                                 |
| POST   | `/api/auth/login`           | Login and set access & refresh cookies       | Public                         | None                        | 200, 401      | Working as expected                                                 |
| POST   | `/api/auth/logout`          | Logout and clear cookies                     | Public (no auth dependency)    | None                        | 200           | **Bug:** Missing auth check—anyone can clear another user's cookies |
| POST   | `/api/auth/refresh`         | Refresh access token using cookie            | Public (cookie-based)          | Cookie (`refresh_token`)    | 200, 401      | Bypasses FastAPI's `Depends`; manually reads cookie                 |
| GET    | `/api/auth/verify`          | Verify current access token validity         | Authenticated                  | Cookie (`access_token`)     | 200, 401      | Working as expected                                                 |
| POST   | `/api/auth/change-password` | Change the logged-in user's password         | Authenticated                  | Cookie (`access_token`)     | 200, 400      | Working as expected                                                 |
| GET    | `/api/auth/me`              | Get current user profile                     | Authenticated                  | Cookie (`access_token`)     | 200, 401, 403 | Working as expected; 403 for deactivated; 401 for deleted users     |

---

## Admin Routes (`/api/admin`) — *Admin only*

| Method | Endpoint                                   | Description                                     | Permission | Auth Type               | Status Codes  | Bug Analysis / Notes |
| ------ | ------------------------------------------ | ----------------------------------------------- | ---------- | ----------------------- | ------------- | -------------------- |
| GET    | `/api/admin/dashboard`                     | Retrieve admin dashboard config & statuses      | Admin only | Cookie (`access_token`) | 200, 500      | —                    |
| POST   | `/api/admin/connect-nas`                   | Configure a new NAS connection                  | Admin only | Cookie (`access_token`) | 200, 400      | —                    |
| POST   | `/api/admin/disconnect-nas`                | Clear NAS settings                              | Admin only | Cookie (`access_token`) | 200           | —                    |
| GET    | `/api/admin/check-nas-connection`          | Check current NAS connectivity                  | Admin only | Cookie (`access_token`) | 200           | —                    |
| POST   | `/api/admin/configure-google-drive`        | Configure Google Drive OAuth                    | Admin only | Cookie (`access_token`) | 200, 500      | —                    |
| GET    | `/api/admin/check-google-drive-connection` | Check Google Drive configuration                | Admin only | Cookie (`access_token`) | 200           | —                    |
| POST   | `/api/admin/reset-google-drive`            | Reset Google Drive configuration                | Admin only | Cookie (`access_token`) | 200, 500      | —                    |
| POST   | `/api/admin/flush-db`                      | Flush application database and Redis cache      | Admin only | Cookie (`access_token`) | 200, 500      | —                    |
| GET    | `/api/admin/users`                         | List all users                                  | Admin only | Cookie (`access_token`) | 200           | —                    |
| GET    | `/api/admin/users/{username}`              | Get a single user by username                   | Admin only | Cookie (`access_token`) | 200, 404      | —                    |
| PUT    | `/api/admin/users/{username}`              | Update a user's details                         | Admin only | Cookie (`access_token`) | 200, 404      | —                    |
| POST   | `/api/admin/users/{username}/suspend`      | Suspend or reactivate a user                    | Admin only | Cookie (`access_token`) | 200, 400, 404 | —                    |
| POST   | `/api/admin/add-user`                      | Add a new user                                  | Admin only | Cookie (`access_token`) | 200, 400      | —                    |
| GET    | `/api/admin/edit-instructions`             | Get current annotation instructions             | Admin only | Cookie (`access_token`) | 200, 400, 404 | —                    |
| POST   | `/api/admin/edit-instructions`             | Update annotation instructions                  | Admin only | Cookie (`access_token`) | 200, 400, 404 | —                    |
| GET    | `/api/admin/ocr-directory`                 | Get OCR directory settings and statistics       | Admin only | Cookie (`access_token`) | 200           | —                    |
| GET    | `/api/admin/browse-nas-directory`          | Browse NAS directory                            | Admin only | Cookie (`access_token`) | 200           | —                    |
| POST   | `/api/admin/select-annotation-folder`      | Select folder for annotation uploads            | Admin only | Cookie (`access_token`) | 200           | —                    |
| POST   | `/api/admin/select-verification-folders`   | Select folder(s) for verification               | Admin only | Cookie (`access_token`) | 200, 400, 500 | —                    |
| POST   | `/api/admin/select-dataset`                | Select a dataset for annotation or verification | Admin only | Cookie (`access_token`) | 200, 400, 404 | —                    |
| GET    | `/api/admin/get-datasets`                  | Get available datasets for a specific mode      | Admin only | Cookie (`access_token`) | 200, 400      | —                    |
| GET    | `/api/admin/browser/{folder:path}`         | Browse files in NAS                             | Admin only | Cookie (`access_token`) | 200, 500      | —                    |
| GET    | `/api/admin/data-delivery`                 | Get data-delivery statistics                    | Admin only | Cookie (`access_token`) | 200, 500      | —                    |
| POST   | `/api/admin/merge-dataset-json`            | Merge JSON files into dataset                   | Admin only | Cookie (`access_token`) | 200           | —                    |
| GET    | `/api/admin/image/{image_path:path}`       | Proxy an image file                             | Admin only | Cookie (`access_token`) | 200, 404      | —                    |

---

## Google Drive OAuth Callback

| Method | Endpoint                       | Description                            | Permission | Auth Type | Status Codes | Notes                   |
| ------ | ------------------------------ | -------------------------------------- | ---------- | --------- | ------------ | ----------------------- |
| GET    | `/admin/google-drive-callback` | Handle OAuth redirect & token exchange | Public     | None      | 307 Redirect | Not under `/api` prefix |

---

## Annotator Routes (`/api/annotator`) — *Annotator only*

| Method | Endpoint                            | Description                               | Permission     | Auth Type               | Status Codes | Bug Analysis / Notes      |
| ------ | ----------------------------------- | ----------------------------------------- | -------------- | ----------------------- | ------------ | ------------------------- |
| GET    | `/api/annotator/dashboard`          | Get annotator dashboard (mode)            | Annotator only | Cookie (`access_token`) | 200          | —                         |
| GET    | `/api/annotator/annotate`           | Fetch batch for annotation/verification   | Annotator only | Cookie (`access_token`) | 200, 204     | 204 if no tasks available |
| GET    | `/api/annotator/next-set`           | Assign and fetch next batch               | Annotator only | Cookie (`access_token`) | 200          | —                         |
| GET    | `/api/annotator/image/{image_path}` | Serve a single image (with response-time) | Annotator only | Cookie (`access_token`) | 200, 500     | —                         |
| POST   | `/api/annotator/save-labels`        | Save labels for a batch                   | Annotator only | Cookie (`access_token`) | 200, 500     | —                         |

---

## Supervision Routes (`/api/supervision`) — *Supervision mode only*

| Method | Endpoint                                     | Description                                          | Permission                     | Auth Type               | Status Codes  | Notes |
| ------ | -------------------------------------------- | ---------------------------------------------------- | ------------------------------ | ----------------------- | ------------- | ----- |
| POST   | `/api/supervision/upload`                    | Upload docs for model inference (local or Drive)     | Annotator (mode="supervision") | Cookie (`access_token`) | 200, 400      | —     |
| GET    | `/api/supervision/review`                    | Review processed docs (status, parsed fields)        | Annotator (mode="supervision") | Cookie (`access_token`) | 200, 400      | —     |
| GET    | `/api/supervision/check-status/{image_id}`   | Check model inference status                         | Annotator (mode="supervision") | Cookie (`access_token`) | 200, 404      | —     |
| GET    | `/api/supervision/document-image/{image_id}` | Serve document image for review interface            | Annotator (mode="supervision") | Cookie (`access_token`) | 200, 404, 500 | —     |
| POST   | `/api/supervision/save-document`             | Persist inference results to Google Sheets & cleanup | Annotator (mode="supervision") | Cookie (`access_token`) | 200, 400, 404 | —     |
| GET    | `/api/supervision/download_csv/{image_id}`   | Download CSV result file                             | Annotator (mode="supervision") | Cookie (`access_token`) | 200, 404      | —     |
| GET    | `/api/supervision/download_txt/{image_id}`   | Download TXT result file                             | Annotator (mode="supervision") | Cookie (`access_token`) | 200, 404      | —     |
| POST   | `/api/supervision/list-drive-folders`        | List Drive folders & files                           | Annotator (mode="supervision") | Cookie (`access_token`) | 200           | —     |
| POST   | `/api/supervision/download_csv`              | Export edited data as CSV                            | Annotator (mode="supervision") | Cookie (`access_token`) | 200, 400      | —     |

---

## Auditor Routes (`/api/auditor`) — *Auditor only*

| Method | Endpoint                    | Description                          | Permission   | Auth Type               | Status Codes  | Notes |
| ------ | --------------------------- | ------------------------------------ | ------------ | ----------------------- | ------------- | ----- |
| GET    | `/api/auditor/modes`        | List audit modes                     | Auditor only | Cookie (`access_token`) | 200           | —     |
| GET    | `/api/auditor/datasets`     | List datasets available for auditing | Auditor only | Cookie (`access_token`) | 200           | —     |
| GET    | `/api/auditor/verifiers`    | List verifiers for a dataset         | Auditor only | Cookie (`access_token`) | 200           | —     |
| GET    | `/api/auditor/files`        | List batch files for verifier/mode   | Auditor only | Cookie (`access_token`) | 200           | —     |
| GET    | `/api/auditor/tasks`        | Load tasks (images + labels)         | Auditor only | Cookie (`access_token`) | 200           | —     |
| GET    | `/api/auditor/image`        | Proxy an image from NAS              | Auditor only | Cookie (`access_token`) | 200, 404, 503 | —     |
| GET    | `/api/auditor/history`      | Fetch audit history for an auditor   | Auditor only | Cookie (`access_token`) | 200           | —     |
| POST   | `/api/auditor/audit-record` | Update an audit record (no content)  | Auditor only | Cookie (`access_token`) | 204, 400      | —     |
| POST   | `/api/auditor/save-labels`  | Save updated labels & update history | Auditor only | Cookie (`access_token`) | 200, 404, 500 | —     |

---

## Client Routes (`/api/client`) — *Client only*

| Method | Endpoint                    | Description                              | Permission  | Auth Type               | Status Codes | Notes |
| ------ | --------------------------- | ---------------------------------------- | ----------- | ----------------------- | ------------ | ----- |
| GET    | `/api/client/datasets`      | List datasets assigned to current client | Client only | Cookie (`access_token`) | 200          | —     |
| GET    | `/api/client/datasets/{id}` | Get detailed metrics for a dataset       | Client only | Cookie (`access_token`) | 200, 404     | —     |

---

## Knowledge Base Routes (`/api/knowledge-base`) — *Admin only*

| Method | Endpoint                           | Description                 | Permission | Auth Type               | Status Codes  | Notes |
| ------ | ---------------------------------- | --------------------------- | ---------- | ----------------------- | ------------- | ----- |
| GET    | `/api/knowledge-base/entries`      | List knowledge-base entries | Admin only | Cookie (`access_token`) | 200, 500      | —     |
| GET    | `/api/knowledge-base/topics`       | List unique KB topics       | Admin only | Cookie (`access_token`) | 200, 500      | —     |
| GET    | `/api/knowledge-base/entries/{id}` | Get a KB entry by ID        | Admin only | Cookie (`access_token`) | 200, 404, 500 | —     |
| POST   | `/api/knowledge-base/entries`      | Create a new KB entry       | Admin only | Cookie (`access_token`) | 201, 500      | —     |
| PUT    | `/api/knowledge-base/entries/{id}` | Update an existing KB entry | Admin only | Cookie (`access_token`) | 200, 404, 500 | —     |
| DELETE | `/api/knowledge-base/entries/{id}` | Delete a KB entry           | Admin only | Cookie (`access_token`) | 204, 404, 500 | —     |

---

## Synthetic Dataset Routes (`/api/synthetic-dataset`) — *Admin only*

| Method | Endpoint                                   | Description                        | Permission | Auth Type               | Status Codes | Notes |
| ------ | ------------------------------------------ | ---------------------------------- | ---------- | ----------------------- | ------------ | ----- |
| GET    | `/api/synthetic-dataset/models`            | List available synthetic models    | Admin only | Cookie (`access_token`) | 200, 500     | —     |
| GET    | `/api/synthetic-dataset/dataset-types`     | List available dataset types       | Admin only | Cookie (`access_token`) | 200, 500     | —     |
| POST   | `/api/synthetic-dataset/generate`          | Generate dataset (reference-based) | Admin only | Cookie (`access_token`) | 200          | —     |
| POST   | `/api/synthetic-dataset/generate-nonref`   | Generate dataset (non-reference)   | Admin only | Cookie (`access_token`) | 200          | —     |
| GET    | `/api/synthetic-dataset/knowledge-entries` | List KB entries for dataset        | Admin only | Cookie (`access_token`) | 200, 500     | —     |

---

## NoteOCR Routes (`/api/NoteOCR`) — *Public (no auth)*

| Method | Endpoint                             | Description                          | Permission | Auth Type | Status Codes            | Bug Analysis / Notes                             |
| ------ | ------------------------------------ | ------------------------------------ | ---------- | --------- | ----------------------- | ------------------------------------------------ |
| POST   | `/api/NoteOCR/documents/`            | Upload PDF and extract pages         | Public     | None      | 200, 400, 500           | **Bug:** Missing auth—anyone can upload/see docs |
| GET    | `/api/NoteOCR/documents/`            | List all uploaded documents          | Public     | None      | 200, 500                | Missing auth                                     |
| GET    | `/api/NoteOCR/documents/{id}`        | Get a PDF document with pages/images | Public     | None      | 200, 404, 500           | Missing auth                                     |
| DELETE | `/api/NoteOCR/documents/{id}`        | Delete a PDF document                | Public     | None      | 200, 404, 500           | **Bug:** Should require auth to delete           |
| GET    | `/api/NoteOCR/images/extractor-mode` | Check OCR service readiness          | Public     | None      | 200, 500                | Missing auth                                     |
| POST   | `/api/NoteOCR/images/upload`         | Upload image for OCR                 | Public     | None      | 200, 500                | Missing auth                                     |
| POST   | `/api/NoteOCR/images/extract-ocr`    | Perform OCR on an uploaded image     | Public     | None      | 200, 400, 500, 502      | Missing auth                                     |
| POST   | `/api/NoteOCR/images/chat`           | Chat about an image                  | Public     | None      | 200, 400, 404, 500, 502 | Missing auth                                     |

---

## Telegram Routes (`/api/telegram`) — *Admin only*

| Method | Endpoint                             | Description                         | Permission | Auth Type               | Status Codes       | Notes |
| ------ | ------------------------------------ | ----------------------------------- | ---------- | ----------------------- | ------------------ | ----- |
| POST   | `/api/telegram/connect`              | Connect to Telegram via API ID/hash | Admin only | Cookie (`access_token`) | 200, 500           | —     |
| POST   | `/api/telegram/verify-code`          | Submit Telegram verification code   | Admin only | Cookie (`access_token`) | 200, 500           | —     |
| POST   | `/api/telegram/verify-password`      | Submit Telegram 2FA password        | Admin only | Cookie (`access_token`) | 200, 500           | —     |
| GET    | `/api/telegram/check-auth`           | Check Telegram session auth status  | Admin only | Cookie (`access_token`) | 200, 500           | —     |
| GET    | `/api/telegram/channels`             | List Telegram channels              | Admin only | Cookie (`access_token`) | 200, 400, 500      | —     |
| POST   | `/api/telegram/disconnect`           | Disconnect Telegram session         | Admin only | Cookie (`access_token`) | 200, 400, 500      | —     |
| GET    | `/api/telegram/images`               | List images for a channel           | Admin only | Cookie (`access_token`) | 200, 400, 500      | —     |
| GET    | `/api/telegram/dates`                | List available dates for a channel  | Admin only | Cookie (`access_token`) | 200, 400, 500      | —     |
| POST   | `/api/telegram/download-image`       | Download a single image             | Admin only | Cookie (`access_token`) | 200, 400, 404, 500 | —     |
| POST   | `/api/telegram/download-multiple`    | Download multiple images            | Admin only | Cookie (`access_token`) | 200, 400, 500      | —     |
| GET    | `/api/telegram/analytics`            | Get channel analytics               | Admin only | Cookie (`access_token`) | 200, 400, 500      | —     |
| POST   | `/api/telegram/upload-to-drive`      | Upload images to Google Drive       | Admin only | Cookie (`access_token`) | 200, 400, 500      | —     |
| GET    | `/api/telegram/export-analytics-csv` | Export analytics CSV                | Admin only | Cookie (`access_token`) | 200, 400, 500      | —     |

---

