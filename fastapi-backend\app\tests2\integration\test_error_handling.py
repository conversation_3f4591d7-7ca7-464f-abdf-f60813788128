import pytest
from main import app
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from db.models.datasets import Datasets
from db.models.knowledge_base import KnowledgeEntry
from db.models.pdf_extractor import Document, Page, Image
import os

client = TestClient(app)

@pytest.fixture(scope="function")
def admin_client(db_session):
    # Register and login an admin user for error-handling tests
    client = TestClient(app)
    payload = {
        "username": "ehadmin", "password": "EhPass123!", "confirm_password": "EhPass123!",
        "role": "admin", "full_name": "EH Admin", "email": "<EMAIL>"
    }
    reg_resp = client.post('/api/auth/register', json=payload)
    assert reg_resp.status_code == 200, f"Admin registration failed: {reg_resp.text}"
    login_resp = client.post('/api/auth/login', data={"username": payload["username"], "password": payload["password"]})
    assert login_resp.status_code == 200, f"Admin login failed: {login_resp.text}"
    return client

@pytest.fixture(scope="function")
def annotator_client(db_session):
    # Register and login an annotator user for error-handling tests
    client = TestClient(app)
    payload = {
        "username": "ehannotator", "password": "EhAnno123!", "confirm_password": "EhAnno123!",
        "role": "annotator", "full_name": "EH Annotator", "email": "<EMAIL>"
    }
    reg_resp = client.post('/api/auth/register', json=payload)
    assert reg_resp.status_code == 200, f"Annotator registration failed: {reg_resp.text}"
    login_resp = client.post('/api/auth/login', data={"username": payload["username"], "password": payload["password"]})
    assert login_resp.status_code == 200, f"Annotator login failed: {login_resp.text}"
    return client

# =============================================================================
# API-Level Error Handling Tests
# =============================================================================

def test_malformed_json():
    """Test handling of malformed JSON in HTTP requests."""
    resp = client.post('/api/auth/login', data='notjson', headers={'Content-Type': 'application/json'})
    assert resp.status_code == 422

def test_weak_password(db_session):
    """Test validation of weak passwords."""
    # Must authenticate before calling change-password; weak password triggers Pydantic validation
    # Register and login
    reg_payload = {"username": "wpuser", "password": "ValidPass1!", "confirm_password": "ValidPass1!", "role": "annotator", "full_name": "WP User", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=reg_payload)
    login_resp = client.post('/api/auth/login', data={"username": reg_payload["username"], "password": reg_payload["password"]})
    cookies = login_resp.cookies
    weak_payload = {"current_password": reg_payload["password"], "new_password": "123", "confirm_password": "123"}
    resp = client.post('/api/auth/change-password', json=weak_payload, cookies=cookies)
    assert resp.status_code == 422

def test_db_downtime():
    """Test handling of database connectivity failures."""
    from db.db_connector import get_db_connection
    from fastapi import HTTPException, status
    # Override FastAPI DB dependency to simulate downtime
    def fail_db():
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail='DB down')
    app.dependency_overrides[get_db_connection] = fail_db
    resp = client.get('/api/auth/me')
    # Unauthenticated or DB failure
    assert resp.status_code in (401, 500)
    # Clean up override
    app.dependency_overrides.pop(get_db_connection, None)

def test_request_timeout():
    """Test handling of request timeouts."""
    from dependencies.auth import get_current_user
    from fastapi import HTTPException, status
    # Override FastAPI get_current_user dependency to simulate timeout
    def timeout_dep():
        raise HTTPException(status_code=status.HTTP_504_GATEWAY_TIMEOUT, detail='Dependency timeout')
    app.dependency_overrides[get_current_user] = timeout_dep
    resp = client.get('/api/auth/me')
    # Unauthenticated or timeout
    assert resp.status_code in (401, 504, 500)
    # Clean up override
    app.dependency_overrides.pop(get_current_user, None)

def test_google_drive_failure(admin_client):
    """Simulate Google Drive service failure and verify error handling."""
    with patch('utils.google_drive.google_drive.GoogleDriveClient.is_configured', side_effect=Exception('Drive down')):
        resp = admin_client.get('/api/admin/dashboard')
        assert resp.status_code in (401, 500, 200)

def test_telegram_failure():
    """Simulate Telegram service failure and verify error handling."""
    with patch('documind.telegram_service.connect_to_telegram', side_effect=Exception('Telegram down')):
        payload = {"api_id": 123, "api_hash": "bad", "phone": "123"}
        resp = client.post('/api/telegram/connect', json=payload)
        assert resp.status_code in (401, 404, 400, 500)

def test_ftp_failure(admin_client):
    """Simulate FTP service failure and verify error handling."""
    with patch('core.nas_connector.get_ftp_connector', side_effect=Exception('FTP down')):
        resp = admin_client.get('/api/admin/browse-nas-directory')
        assert resp.status_code in (400, 401, 500, 200)

def test_redis_cache_failure(annotator_client):
    """Test handling of Redis cache service failures."""
    with patch('cache.redis_connector.cache_get', side_effect=Exception('Redis down')):
        # This should still work even if cache fails
        resp = annotator_client.get('/api/annotator/dashboard')
        assert resp.status_code in (200, 401, 500)

def test_redis_cache_set_failure(annotator_client):
    """Test handling of Redis cache set failures."""
    with patch('cache.redis_connector.cache_set', side_effect=Exception('Redis write failed')):
        # Cache write failure shouldn't break the endpoint
        resp = annotator_client.get('/api/annotator/dashboard')
        assert resp.status_code in (200, 401, 500)

def test_gemini_api_failure():
    """Test handling of Gemini AI service failures."""
    # Set a valid API key but mock the service to fail
    with patch.dict(os.environ, {'GEMINI_API_KEY': 'test_key'}):
        with patch('synthetic_dataset.reference_agent.ReferenceBasedGenerator.generate_dataset', side_effect=Exception('AI service down')):
            payload = {
                "model": "gemini-1.5-flash",
                "dataset_type": "reference",
                "num_entries": 5,
                "topics": ["test"]
            }
            resp = client.post('/api/synthetic-dataset/generate', json=payload)
            assert resp.status_code in (400, 500, 200, 401)

def test_missing_gemini_api_key():
    """Test handling when GEMINI_API_KEY is not configured."""
    # Remove API key from environment
    with patch.dict(os.environ, {}, clear=True):
        payload = {
            "model": "gemini-1.5-flash",
            "dataset_type": "reference",
            "num_entries": 5,
            "topics": ["test"]
        }
        resp = client.post('/api/synthetic-dataset/generate', json=payload)
        assert resp.status_code in (400, 500, 401)
        if resp.status_code == 200:
            data = resp.json()
            assert not data.get("success", True)
            assert "GEMINI_API_KEY" in data.get("error", "")

def test_file_upload_failure():
    """Test handling of file upload failures."""
    # Test with corrupted file data
    files = {"file": ("test.pdf", b"corrupted_pdf_data", "application/pdf")}
    resp = client.post('/api/NoteOCR/documents/', files=files)
    assert resp.status_code in (400, 422, 500)

def test_image_processing_failure(annotator_client):
    """Test handling of image processing failures."""
    with patch('utils.image_processing.get_image_from_storage', side_effect=Exception('Image processing failed')):
        # This should handle the error gracefully
        resp = annotator_client.get('/api/annotator/image/nonexistent.jpg')
        assert resp.status_code in (404, 500, 503)

def test_ocr_service_failure():
    """Test handling of OCR service failures."""
    # First upload an image to get an image_id
    files = {"image": ("test.jpg", b"fake_image_data", "image/jpeg")}
    upload_resp = client.post('/api/NoteOCR/images/upload', files=files)
    assert upload_resp.status_code == 200
    image_id = upload_resp.json()["image_id"]
    
    # Mock OCR service failure for text extraction
    with patch('NoteOCR.image_extractor_service.OCRExtractor.extract_text', side_effect=Exception('OCR service down')):
        payload = {"image_id": image_id, "prompt": "Extract text from this image"}
        resp = client.post('/api/NoteOCR/images/extract-ocr', json=payload)
        # Should handle OCR failure gracefully
        assert resp.status_code in (400, 500, 503)

def test_pdf_processing_failure():
    """Test handling of PDF processing failures."""
    with patch('NoteOCR.pdf_service.PDFService.extract_pages', side_effect=Exception('PDF processing failed')):
        files = {"file": ("test.pdf", b"fake_pdf_data", "application/pdf")}
        resp = client.post('/api/NoteOCR/documents/', files=files)
        assert resp.status_code in (400, 500)

def test_large_request_payload():
    """Test handling of extremely large request payloads."""
    # Create a very large payload
    large_payload = {
        "username": "largeuser",
        "password": "LargePass123!",
        "confirm_password": "LargePass123!",
        "role": "annotator",
        "full_name": "A" * 10000,  # Very long name
        "email": "<EMAIL>"
    }
    resp = client.post('/api/auth/register', json=large_payload)
    # Should handle large payloads gracefully - app accepts it but it's still a valid test
    assert resp.status_code in (200, 400, 413, 422, 500)

def test_concurrent_session_conflict():
    """Test handling of concurrent session conflicts."""
    # Register a user
    payload = {
        "username": "concurrentuser", "password": "ConcPass123!", "confirm_password": "ConcPass123!",
        "role": "annotator", "full_name": "Concurrent User", "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    
    # Login from multiple clients simultaneously
    client1 = TestClient(app)
    client2 = TestClient(app)
    
    resp1 = client1.post('/api/auth/login', data={"username": payload["username"], "password": payload["password"]})
    resp2 = client2.post('/api/auth/login', data={"username": payload["username"], "password": payload["password"]})
    
    # Both should succeed or handle gracefully
    assert resp1.status_code in (200, 400, 401)
    assert resp2.status_code in (200, 400, 401)

def test_invalid_content_type():
    """Test handling of invalid content types."""
    # Send XML to JSON endpoint
    resp = client.post('/api/auth/login', 
                      data='<xml>invalid</xml>', 
                      headers={'Content-Type': 'application/xml'})
    assert resp.status_code in (400, 415, 422)

def test_missing_required_headers():
    """Test handling of missing required headers."""
    # Test without Authorization header on protected endpoint
    resp = client.get('/api/auth/me')
    assert resp.status_code == 401

def test_invalid_authorization_format():
    """Test handling of malformed authorization headers."""
    # Test with malformed Bearer token
    resp = client.get('/api/auth/me', headers={'Authorization': 'InvalidFormat token123'})
    assert resp.status_code == 401
    
    # Test with empty Bearer token
    resp = client.get('/api/auth/me', headers={'Authorization': 'Bearer '})
    assert resp.status_code == 401

# =============================================================================
# Database Edge Case & Boundary Testing
# =============================================================================

def test_extreme_numeric_values(db_session):
    """Insert extreme numeric values to test column bounds."""
    # Test max and min 64-bit integer values
    max_int = 2**63 - 1
    min_int = -2**63
    # Create entries with extreme values
    ds_max = Datasets(
        dataset_name='max_test', dataset_image_path='img', label_folder_path='lbl',
        annotator_mode='annotation', instructions='inst', audited_batch=max_int,
        completed_batch=0, total_batch=1, dataset_status='active', client_id='client'
    )
    ds_min = Datasets(
        dataset_name='min_test', dataset_image_path='img', label_folder_path='lbl',
        annotator_mode='annotation', instructions='inst', audited_batch=min_int,
        completed_batch=0, total_batch=1, dataset_status='active', client_id='client'
    )
    db_session.add_all([ds_max, ds_min])
    db_session.commit()
    # Verify stored values
    fetched_max = db_session.query(Datasets).filter_by(dataset_name='max_test').one()
    fetched_min = db_session.query(Datasets).filter_by(dataset_name='min_test').one()
    assert fetched_max.audited_batch == max_int
    assert fetched_min.audited_batch == min_int

def test_overlength_strings(db_session):
    """Insert strings longer than expected maximum lengths and verify no truncation or errors."""
    long_title = 'T' * 300  # exceeds DB length
    long_topic = 'X' * 150  # exceeds DB length
    entry = KnowledgeEntry(title=long_title, topic=long_topic, content='C', source=None)
    db_session.add(entry)
    db_session.commit()
    fetched = db_session.query(KnowledgeEntry).filter_by(id=entry.id).one()
    assert fetched.title == long_title, "Title was truncated or altered"
    assert fetched.topic == long_topic, "Topic was truncated or altered"

def test_malformed_json_text_blob(db_session):
    """Insert malformed JSON or text blobs into Text/JSON columns and ensure ORM can handle raw text."""
    # Create parent document
    doc = Document(filename='test.pdf')
    db_session.add(doc)
    db_session.commit()
    bad_json = '{"unclosed": true'
    page = Page(document_id=doc.id, page_number=1, text_content='sample text', metadata_json=bad_json)
    db_session.add(page)
    db_session.commit()
    retrieved = db_session.query(Page).filter_by(id=page.id).one()
    assert retrieved.metadata_json == bad_json, "Malformed JSON blob was altered or rejected"

def test_corrupted_data_handling(db_session):
    """Test graceful handling of corrupted binary or data at ORM/DB layer."""
    # Create document and page
    doc = Document(filename='dummy.pdf')
    db_session.add(doc)
    db_session.commit()
    page = Page(document_id=doc.id, page_number=1, text_content='text', metadata_json=None)
    db_session.add(page)
    db_session.commit()
    corrupted = "\x00\xFF\x00data"
    img = Image(page_id=page.id, image_data=corrupted, image_type='bin')
    db_session.add(img)
    db_session.commit()
    retrieved = db_session.query(Image).filter_by(id=img.id).one()
    assert retrieved.image_data == corrupted, "Corrupted data was altered or caused error"

def test_null_constraint_violations(db_session):
    """Test handling of NULL constraint violations."""
    # Try to create entry with missing required fields
    try:
        entry = KnowledgeEntry(title=None, topic="test", content="test", source=None)
        db_session.add(entry)
        db_session.commit()
        # If it succeeds, that's also valid (nullable field)
    except Exception as e:
        # Should handle constraint violations gracefully
        assert "null" in str(e).lower() or "not null" in str(e).lower()
        db_session.rollback()

def test_foreign_key_constraint_violations(db_session):
    """Test handling of foreign key constraint violations."""
    try:
        # Try to create page with non-existent document_id
        page = Page(document_id=99999, page_number=1, text_content='test', metadata_json=None)
        db_session.add(page)
        db_session.commit()
    except Exception as e:
        # Should handle foreign key violations gracefully
        assert "foreign key" in str(e).lower() or "constraint" in str(e).lower()
        db_session.rollback()

def test_duplicate_key_violations(db_session):
    """Test handling of duplicate key violations."""
    # Create first entry
    entry1 = KnowledgeEntry(title="unique_test", topic="test", content="test1", source=None)
    db_session.add(entry1)
    db_session.commit()
    
    # Try to create duplicate (if unique constraint exists)
    try:
        entry2 = KnowledgeEntry(title="unique_test", topic="test", content="test2", source=None)
        db_session.add(entry2)
        db_session.commit()
        # If it succeeds, no unique constraint exists (which is also valid)
    except Exception as e:
        # Should handle unique constraint violations gracefully
        assert "unique" in str(e).lower() or "duplicate" in str(e).lower()
        db_session.rollback() 