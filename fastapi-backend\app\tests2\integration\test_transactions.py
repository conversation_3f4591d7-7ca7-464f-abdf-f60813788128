import threading
import pytest
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from main import app
from db.db_connector import engine
from db.models.user import User

# HTTP client for endpoint-based concurrency tests
client = TestClient(app)

@pytest.fixture(scope='module')
def annotator_token():
    # Register and login an annotator for concurrent endpoint tests
    payload = {
        "username": "concurrentuser",
        "password": "Concurpass123!",
        "confirm_password": "Concurpass123!",
        "role": "annotator",
        "full_name": "Concurrent User",
        "email": "<EMAIL>"
    }
    client.post('/api/auth/register', json=payload)
    resp = client.post('/api/auth/login', data={"username": payload["username"], "password": payload["password"]})
    return resp.cookies

# 1. Concurrency via endpoints

def test_concurrent_save_labels(annotator_token):
    def save_labels():
        payload = {"labels": {"img1": "label1"}, "verification_mode": False, "batch_name": "batch1"}
        resp = client.post('/api/annotator/save-labels', json=payload, cookies=annotator_token)
        assert resp.status_code in (200, 500)
    threads = [threading.Thread(target=save_labels) for _ in range(5)]
    for t in threads:
        t.start()
    for t in threads:
        t.join()


def test_concurrent_batch_fetch(annotator_token):
    def fetch_batch():
        resp = client.get('/api/annotator/annotate', cookies=annotator_token)
        assert resp.status_code in (200, 204)
    threads = [threading.Thread(target=fetch_batch) for _ in range(5)]
    for t in threads:
        t.start()
    for t in threads:
        t.join()

# Helper for ORM-level concurrent inserts
def insert_user(SessionLocal, username, results, index):
    session = SessionLocal()
    try:
        user = User(username=username, full_name='F', email=f'{username}@test.com', password_hash='h')
        session.add(user)
        session.commit()
        results[index] = 'success'
    except Exception as e:
        results[index] = type(e).__name__
        session.rollback()
    finally:
        session.close()


def test_concurrent_inserts(db_session):
    engine_bind = db_session.get_bind()
    SessionLocal = sessionmaker(bind=engine_bind)
    results = [None, None]
    threads = []
    for i in range(2):
        t = threading.Thread(target=insert_user, args=(SessionLocal, 'raceuser', results, i))
        threads.append(t)
        t.start()
    for t in threads:
        t.join()
    # One success, one failure (either IntegrityError or ResourceClosedError)
    assert results.count('success') == 1
    # Both IntegrityError and ResourceClosedError indicate concurrent access issues
    assert 'IntegrityError' in results or 'ResourceClosedError' in results

# 2. Transaction isolation

def test_transaction_isolation(db_session):
    from threading import Thread
    from sqlalchemy.orm import sessionmaker as _sessionmaker
    # Set up session factory
    engine_bind = db_session.get_bind()
    SessionLocal = _sessionmaker(bind=engine_bind)
    # Seed a user
    session_main = SessionLocal()
    user = User(username='iso', full_name='Iso User', email='<EMAIL>', password_hash='h')
    session_main.add(user)
    session_main.commit()
    session_main.close()

    results = {}
    def read_user():
        session = SessionLocal()
        usr = session.query(User).filter_by(username='iso').one()
        results['read_full_name'] = usr.full_name
        session.close()
    def update_user():
        session = SessionLocal()
        session.query(User).filter_by(username='iso').update({'full_name': 'Updated'})
        session.commit()
        session.close()

    updater = Thread(target=update_user)
    reader = Thread(target=read_user)
    updater.start()
    reader.start()
    updater.join()
    reader.join()

    # Final state should reflect commit
    session_check = SessionLocal()
    usr_final = session_check.query(User).filter_by(username='iso').one()
    assert usr_final.full_name == 'Updated'
    session_check.close()

# 3. SQLite isolation levels

def test_read_uncommitted_pragma():
    """Verify SQLite read_uncommitted PRAGMA can be set and retrieved."""
    conn = engine.connect()
    conn.execute(text("PRAGMA read_uncommitted = true"))
    val = conn.execute(text("PRAGMA read_uncommitted")).scalar()
    assert val == 1
    conn.close()
