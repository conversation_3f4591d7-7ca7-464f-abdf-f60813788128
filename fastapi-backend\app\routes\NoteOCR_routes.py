import os
import tempfile
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Request
from sqlalchemy.orm import Session
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel
import base64
import logging

from NoteOCR.image_extractor_service import (
    get_ocr_extractor,
    ImageUploadResponse
)

# Configure logging
logger = logging.getLogger('NoteOCR_routes')


from db.models.pdf_extractor import  Document, Page, Image
from schemas.PDF_ExtractorSchemas import DocumentSummary, Document as DocumentSchema
from NoteOCR.pdf_service import PDFService
from db.db_connector import get_db_connection as get_db
router = APIRouter(prefix="/NoteOCR",tags=["NoteOCR"])
pdf_service = PDFService()



#.....................PDF Extractor Routes.....................
@router.post("/documents/", response_model=DocumentSchema)
async def upload_pdf(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """
    Upload a PDF file and extract its content page by page
    """
    # Verify it's a PDF
    if not file.filename.endswith(".pdf"):
        raise HTTPException(status_code=400, detail="File must be a PDF")
    
    # Save the uploaded file to a temporary location
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
    try:
        # Write the file content
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        try:
            # Extract pages data from the PDF
            pages_data = pdf_service.extract_pages(temp_file.name)
            
            # Create document record
            db_document = Document(filename=file.filename)
            db.add(db_document)
            db.commit()
            db.refresh(db_document)
            
            # Create page records
            for page_data in pages_data:
                db_page = Page(
                    document_id=db_document.id,
                    page_number=page_data['page_number'],
                    text_content=page_data['text_content'],
                    metadata_json=page_data['metadata_json']
                )
                db.add(db_page)
                db.commit()
                db.refresh(db_page)
                
                # Create image records for this page
                for img_data in page_data.get('images', []):
                    db_image = Image(
                        page_id=db_page.id,
                        image_data=img_data['image_data'],
                        image_type=img_data['image_type']
                    )
                    db.add(db_image)
            
            db.commit()
            
            # Return the document with all pages
            db.refresh(db_document)
            return db_document
            
        except Exception as e:
            # Rollback any database changes
            db.rollback()
            raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error handling uploaded file: {str(e)}")
    
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

@router.get("/documents/", response_model=List[DocumentSummary])
def get_documents(db: Session = Depends(get_db)):
    """
    Get a list of all uploaded documents
    """
    documents = db.query(Document).all()
    return [
        {
            "id": doc.id,
            "filename": doc.filename,
            "upload_time": doc.upload_time,
            "page_count": len(doc.pages),
            "total_images": sum(len(page.images) for page in doc.pages)
        } 
        for doc in documents
    ]

@router.get("/documents/{document_id}", response_model=DocumentSchema)
def get_document(document_id: int, db: Session = Depends(get_db)):
    """
    Get a document by ID, including all pages and images 
    """
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        return document
        
    except Exception as e:
        print(f"Error getting document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving document: {str(e)}")

@router.delete("/documents/{document_id}")
def delete_document(document_id: int, db: Session = Depends(get_db)):
    """
    Delete a document by ID
    """
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    db.delete(document)
    db.commit()
    
    return {"message": "Document deleted successfully"} 


#.....................Image Extractor Routes.....................

"""
Image extractor routes for OCR and image chat functionality
"""

# Pydantic models for image extractor routes
class ChatRequest(BaseModel):
    """Model for chat requests"""
    image_id: str  # ID of the image to chat about
    query: str
    chat_history: Optional[List[Dict[str, Any]]] = None

class CustomPromptRequest(BaseModel):
    """Model for custom prompt requests"""
    image_id: str  # required image ID to extract text for
    prompt: Optional[str] = None  # optional custom prompt; defaults to server-defined prompt

class ExtractTextResponse(BaseModel):
    """Model for text extraction response"""
    success: bool = True
    text: str
    method: Optional[str] = None

class ChatResponse(BaseModel):
    """Model for chat response"""
    success: bool = True
    response: str

class ErrorResponse(BaseModel):
    """Model for error responses"""
    success: bool = False
    error: str
    status_code: int

@router.get("/images/extractor-mode")
async def extractor_mode(request: Request) -> JSONResponse:
    """
    Normal mode route for OCR extraction from uploaded images
    """
    try:
        # Clean up old image metadata
        ocr_service = get_ocr_extractor()
        await ocr_service.cleanup_old_images()
        
        return JSONResponse(
            content={"success": True, "message": "OCR service ready"},
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error in extractor_mode: {str(e)}", exc_info=True)
        return JSONResponse(
            content=ErrorResponse(
                error="Failed to initialize OCR service",
                status_code=500
            ).model_dump(),
            status_code=500
        )

@router.post("/images/upload")
async def upload_image(
    image: UploadFile = File(...),
) -> Union[ImageUploadResponse, ErrorResponse]:
    """
    Upload an image for processing
    """
    try:
        ocr_service = get_ocr_extractor()
        result = await ocr_service.process_image(image)
        return JSONResponse(
            content={"success": True, **result.model_dump()},
            status_code=200
        )
    except HTTPException as he:
        return JSONResponse(
            content=ErrorResponse(
                error=str(he.detail),
                status_code=he.status_code
            ).model_dump(),
            status_code=he.status_code
        )
    except Exception as e:
        logger.error(f"Error in upload_image route: {str(e)}", exc_info=True)
        return JSONResponse(
            content=ErrorResponse(
                error=str(e),
                status_code=500
            ).model_dump(),
            status_code=500
        )

@router.post("/images/extract-ocr")
async def extract_text_custom(prompt_request: CustomPromptRequest) -> Union[ExtractTextResponse, ErrorResponse]:
    """
    Extract text from an image using a custom prompt
    """
    try:
        ocr_service = get_ocr_extractor()
        image_data = None
        filename = None

        if prompt_request.image_id:
            stored_image = await ocr_service.get_stored_image(prompt_request.image_id)
            image_data = stored_image['image_data']
            filename = stored_image['filename']
        else:
            return JSONResponse(
                content=ErrorResponse(
                    error='No image data or image_id provided',
                    status_code=400
                ).model_dump(),
                status_code=400
            )
                
        # Extract text with custom prompt
        extracted_text = await ocr_service.extract_text(
            image_data,
            custom_prompt=prompt_request.prompt
        )
        
        return JSONResponse(
            content=ExtractTextResponse(
                text=extracted_text,
                method='custom',
                success=True
            ).model_dump(),
            status_code=200
        )
            
    except HTTPException as he:
        return JSONResponse(
            content=ErrorResponse(
                error=str(he.detail),
                status_code=he.status_code
            ).model_dump(),
            status_code=he.status_code
        )
    except Exception as e:
        logger.error(f"Error in extract_text_custom route: {str(e)}", exc_info=True)
        return JSONResponse(
            content=ErrorResponse(
                error=str(e),
                status_code=500
            ).model_dump(),
            status_code=500
        )

@router.post("/images/chat")
async def chat_with_image(
    chat_request: ChatRequest
) -> Union[ChatResponse, ErrorResponse]:
    """
    Chat about an image
    """
    try:
        ocr_service = get_ocr_extractor()
        
        # Retrieve stored image using image_id from request
        stored_image = await ocr_service.get_stored_image(chat_request.image_id)
        image_bytes = stored_image['image_data']
        response = await ocr_service.chat_about_image(
            image_bytes,
            chat_request.query
        )

        return JSONResponse(
            content=ChatResponse(
                response=response,
                success=True
            ).model_dump(),
            status_code=200
        )
            
    except HTTPException as he:
        return JSONResponse(
            content=ErrorResponse(
                error=str(he.detail),
                status_code=he.status_code
            ).model_dump(),
            status_code=he.status_code
        )
    except Exception as e:
        error_msg = str(e)
        if chat_request.chat_history:
            error_msg += " (tried with chat history)"
            
        logger.error(f"Error in chat_with_image route: {error_msg}", exc_info=True)
        return JSONResponse(
            content=ErrorResponse(
                error=error_msg,
                status_code=500
            ).model_dump(),
            status_code=500
        )