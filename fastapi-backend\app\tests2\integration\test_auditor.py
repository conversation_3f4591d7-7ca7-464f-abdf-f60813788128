import pytest
from main import app
from fastapi.testclient import Test<PERSON><PERSON>
from db import db_connector
from db.models.image_verification import ImageVerification
from db.models.datasets import Datasets

@pytest.fixture(scope='function')
def auditor_client(db_session):
    with TestClient(app) as client:
        payload = {"username": "audituser", "password": "Auditpass123!", "confirm_password": "Auditpass123!", "role": "auditor", "full_name": "Audit User", "email": "<EMAIL>"}
        reg_resp = client.post('/api/auth/register', json=payload)
        assert reg_resp.status_code == 200, f"Registration failed: {reg_resp.text}"
        login_resp = client.post('/api/auth/login', data={"username": "audituser", "password": "Auditpass123!"})
        assert login_resp.status_code == 200, f"Login failed: {login_resp.text}"
        yield client

@pytest.fixture(scope='function')
def not_auditor_client(db_session):
    """Authenticated as wrong role (annotator) for RBAC testing"""
    with Test<PERSON>lient(app) as client:
        payload = {"username": "wrongaudit", "password": "Wrongpass123!", "confirm_password": "Wrongpass123!", "role": "annotator", "full_name": "Wrong Audit", "email": "<EMAIL>"}
        client.post('/api/auth/register', json=payload)
        resp = client.post('/api/auth/login', data={"username": payload["username"], "password": payload["password"]})
        assert resp.status_code == 200
        yield client

def test_modes(auditor_client):
    client = auditor_client
    resp = client.get('/api/auditor/modes')
    assert resp.status_code == 200
    assert isinstance(resp.json(), list)

def test_datasets(auditor_client):
    client = auditor_client
    resp = client.get('/api/auditor/datasets')
    assert resp.status_code == 200
    assert isinstance(resp.json(), list)

def test_verifiers(auditor_client):
    client = auditor_client
    # Missing query parameters returns 422
    resp_missing = client.get('/api/auditor/verifiers')
    assert resp_missing.status_code == 422
    # Valid parameters should return a list
    resp = client.get('/api/auditor/verifiers?dataset_name=ds&mode=annotation')
    assert resp.status_code == 200
    assert isinstance(resp.json(), list)

def test_history(auditor_client):
    client = auditor_client
    resp = client.get('/api/auditor/history?auditor_username=audituser')
    assert resp.status_code == 200
    assert isinstance(resp.json(), list)

def test_save_labels_db_and_boundaries(auditor_client):
    client = auditor_client
    payload = {"mode": "verification", "dataset_name": "ds", "verifier_username": "ver", "file_name": "file.json", "tasks": [{"labels": "label", "image_path": "img"}], "comments": "ok"}
    resp = client.post('/api/auditor/save-labels', json=payload)
    assert resp.status_code in (200, 404, 500, 503)
    # DB assertion (if 200)
    if resp.status_code == 200:
        db = db_connector.SessionLocal()
        record = db.query(ImageVerification).filter_by(dataset_name="ds").first()
        assert record is not None
        db.close()
    # Boundary: max file_name (255)
    payload["file_name"] = "f" * 255 + ".json"
    resp = client.post('/api/auditor/save-labels', json=payload)
    assert resp.status_code in (200, 400, 404, 500, 503)
    # Above max (256+)
    payload["file_name"] = "f" * 256 + ".json"
    resp = client.post('/api/auditor/save-labels', json=payload)
    assert resp.status_code in (400, 404, 500, 503)
    # Boundary: max comments (1024)
    payload["file_name"] = "file.json"
    payload["comments"] = "c" * 1024
    resp = client.post('/api/auditor/save-labels', json=payload)
    assert resp.status_code in (200, 400, 404, 500, 503)
    # Above max (1025)
    payload["comments"] = "c" * 1025
    resp = client.post('/api/auditor/save-labels', json=payload)
    assert resp.status_code in (400, 404, 500, 503) #nas not connected (503)

def test_unauthorized_auditor_endpoints():
    """All auditor endpoints should return 401 when no auth token is provided."""
    client = TestClient(app)
    endpoints = [
        ('get', '/api/auditor/files?dataset_name=x&verifier_username=y&mode=annotation', {}),
        ('get', '/api/auditor/tasks?mode=annotation&dataset_name=x&verifier_username=y&file_name=f.json', {}),
        ('get', '/api/auditor/image?path=somepath', {}),
        ('post', '/api/auditor/audit-record', {'json': {'mode': 'annotation', 'record_id': 1, 'auditor_username': 'u', 'audit_status': True}}),
    ]
    for method, path, kwargs in endpoints:
        resp = getattr(client, method)(path, **kwargs)
        assert resp.status_code == 401, f"Unauthorized {path} returned {resp.status_code}"


def test_list_files_and_tasks_empty(auditor_client):
    """When no data exists, list-files and list-tasks return empty lists."""
    resp_files = auditor_client.get('/api/auditor/files?dataset_name=ds&verifier_username=audituser&mode=annotation')
    assert resp_files.status_code == 200
    assert resp_files.json() == []
    resp_tasks = auditor_client.get('/api/auditor/tasks?mode=annotation&dataset_name=ds&verifier_username=audituser&file_name=f.json')
    assert resp_tasks.status_code == 200
    assert resp_tasks.json() == []


def test_image_proxy_success_and_not_found(monkeypatch, auditor_client):
    """GET /api/auditor/image should return content on hit and 404 when missing."""
    class FakeConn:
        async def get_file_content(self, path):
            return b'data' if path == 'exists.jpg' else None
    # Patch get_ftp_connector with an async function returning FakeConn
    import routes.auditor_routes as auditor_routes
    async def fake_get_ftp_connector():
        return FakeConn()
    monkeypatch.setattr(auditor_routes, 'get_ftp_connector', fake_get_ftp_connector)
    # Success
    resp = auditor_client.get('/api/auditor/image?path=exists.jpg')
    assert resp.status_code == 200
    assert resp.content == b'data'
    # Not found
    resp2 = auditor_client.get('/api/auditor/image?path=missing.jpg')
    assert resp2.status_code == 404


def test_audit_record_endpoint(auditor_client, db_session, monkeypatch):
    """POST /api/auditor/audit-record returns 400 for missing or non-existent record and 204 on success."""
    # Patch AuditorService to use the test session
    import services.auditor_service as aud_service
    monkeypatch.setattr(aud_service, 'SessionLocal', lambda **kwargs: db_session)
    # Missing fields -> 422
    resp_missing = auditor_client.post('/api/auditor/audit-record', json={})
    assert resp_missing.status_code == 422
    # Non-existent record -> 400
    payload = {'mode': 'annotation', 'record_id': 999, 'auditor_username': 'audituser', 'audit_status': True}
    resp_not = auditor_client.post('/api/auditor/audit-record', json=payload)
    assert resp_not.status_code == 400
    # Create a record manually
    from db.models.image_verification import ImageVerification
    rec = ImageVerification(
        annotator_username='a',
        auditor_username='audituser',
        dataset_name='ds',
        dataset_batch_name='batch',
        images='[]',
        label_file_path='file',
        image_count=1
    )
    db_session.add(rec)
    db_session.commit()
    # Ensure corresponding dataset exists so audited_batch can be incremented
    dataset = Datasets(
        dataset_name='ds',
        dataset_image_path='',
        label_folder_path='',
        annotator_mode='verification',
        instructions='',
        audited_batch=0,
        completed_batch=0,
        total_batch=0,
        dataset_status='',
        client_id=''
    )
    db_session.add(dataset)
    db_session.commit()
    # Successful update
    payload2 = {'mode': 'verification', 'record_id': rec.id, 'auditor_username': 'audituser', 'audit_status': False, 'comments': 'ok'}
    resp_ok = auditor_client.post('/api/auditor/audit-record', json=payload2)
    # Now should succeed with 204
    assert resp_ok.status_code == 204

def test_auditor_rbac_for_all_endpoints(not_auditor_client):
    """All auditor endpoints should require auditor role: unauthenticated -> 401, wrong-role -> 403"""
    client_no_auth = TestClient(app)
    endpoints = [
        ('get', '/api/auditor/modes', {}),
        ('get', '/api/auditor/datasets', {}),
        ('get', '/api/auditor/history?auditor_username=audituser', {}),
        ('get', '/api/auditor/files?dataset_name=x&verifier_username=y&mode=annotation', {}),
        ('get', '/api/auditor/tasks?mode=annotation&dataset_name=x&verifier_username=y&file_name=f.json', {}),
        ('get', '/api/auditor/image?path=somepath', {}),
        ('post', '/api/auditor/save-labels', {'json': {"mode": "annotation", "dataset_name": "x", "verifier_username": "y", "file_name": "f.json", "tasks": [], "comments": ""}}),
        ('post', '/api/auditor/audit-record', {'json': {"mode": "annotation", "record_id": 1, "auditor_username": "audituser", "audit_status": True}})
    ]
    for method, path, kwargs in endpoints:
        resp_unauth = getattr(client_no_auth, method)(path, **kwargs)
        assert resp_unauth.status_code == 401, f"Unauth {path} returned {resp_unauth.status_code}"
        resp_forbid = getattr(not_auditor_client, method)(path, **kwargs)
        assert resp_forbid.status_code == 403, f"Forbidden {path} returned {resp_forbid.status_code}"