# routes/annotator_routes.py
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Optional
from pydantic import BaseModel
import logging
import json

# Core modules
from services.batch_service import get_batch_manager
from dependencies.auth import get_current_user, get_annotator_mode, get_current_active_user, require_annotator
from sqlalchemy.orm import Session
from db.db_connector import get_db_connection as get_db
from services.annotator_service import annotator_service
from db.models import Datasets
from utils.image_processing import get_image_from_storage

# Cache modules
from cache.annotator_cache import (get_cached_batch, cache_batch, get_cached_user_batch_id, 
                                   cache_user_batch,prefetch_batch_images_async, delete_user_batch_cache)

logger = logging.getLogger('annotator_routes')

router = APIRouter(
    prefix="/annotator", 
    tags=["Annotator"],
    dependencies=[Depends(get_current_active_user), Depends(require_annotator())]
)

class SaveLabelsRequest(BaseModel):
    labels: Dict[str, str]
    verification_mode: bool = False
    batch_name: Optional[str] = None

@router.get("/dashboard")
async def annotator_dashboard(
    annotation_mode: str = Depends(get_annotator_mode)
):
    """Get annotator dashboard data"""
    return {"annotation_mode": annotation_mode}

@router.get("/annotate")
async def annotate_route(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user),
    mode: str = Depends(get_annotator_mode)
):
    """Get annotation data for the current user with caching support"""
    username = current_user["sub"]
    
    # Try to get cached batch first
    cache_mode = "annotation" if mode == "annotation" else "verification"
    cached_batch_id = await get_cached_user_batch_id(username, cache_mode)
    if cached_batch_id:
        cached_batch = await get_cached_batch(cached_batch_id)
        if cached_batch:
            # Convert cached image paths to ImageInfo objects
            from schemas.annotation_schemas import ImageInfo
            cached_images = []
            for img_path in cached_batch['images']:
                cached_images.append(ImageInfo(
                    name=img_path.split('/')[-1],
                    path=img_path,
                    size=0,
                    type='file'
                ))
            
            # Get labels from cache or load them if not cached
            cached_labels = cached_batch.get('labels', {})
            if not cached_labels and mode == 'verification':
                # Fallback: Load labels if not in cache (for backward compatibility)
                dataset_obj = db.query(Datasets).filter(Datasets.dataset_name == cached_batch['dataset_name']).first()
                if dataset_obj and dataset_obj.label_folder_path:
                    try:
                        from core.nas_connector import get_ftp_connector
                        connector = await get_ftp_connector()
                        if connector:
                            file_bytes = await connector.get_file_content(dataset_obj.label_folder_path)
                            if file_bytes:
                                cached_labels = json.loads(file_bytes.decode('utf-8'))
                                logger.info(f"Successfully loaded {len(cached_labels)} labels for cached batch (fallback)")
                    except Exception as e:
                        logger.error(f"Error loading labels for cached batch: {e}")
            
            return {
                "images": annotator_service.prepare_image_data(cached_images, cached_labels),
                "user": current_user,
                "mode": mode,
                "batch_name": cached_batch['batch_name'],
                "instructions": cached_batch.get('instructions', '')
            }
    # Fallback to regular batch fetching
    images, labels, batch_name, dataset_name = await annotator_service.get_batch_for_user(db, username, mode=mode)

    dataset_obj = db.query(Datasets).filter(Datasets.dataset_name == dataset_name).first()
    instructions = dataset_obj.instructions if dataset_obj and dataset_obj.instructions else ""
    
    if not images:
        raise HTTPException(status_code=204, detail="No tasks available for this user")
    # Cache the batch for future requests
    batch_data = {
        'id': f"{username}_{mode}_{batch_name}",
        'batch_name': batch_name,
        'dataset_name': dataset_name,
        'images': [img.path for img in images],
        'image_count': len(images),
        'username': username,
        'mode': mode,
        'instructions': instructions,
        'labels': labels  # Store labels in cache
    }
    
    # Cache batch and user batch mapping
    await cache_batch(batch_data['id'], batch_data)
    await cache_user_batch(username, batch_data['id'], cache_mode)
    
    # Start async prefetching of images
    logger.info(f"Starting prefetch for batch {batch_data['id']} with {len(batch_data['images'])} images")
    prefetch_batch_images_async(batch_data)

    return {
        "images": annotator_service.prepare_image_data(images, labels),
        "user": current_user,
        "mode": mode,
        "batch_name": batch_name,
        "instructions": instructions
    }

@router.get("/next-set")
async def get_next_image_set(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user),
    mode: str = Depends(get_annotator_mode)
):
    """Get the next set of images for annotation with caching support"""
    username = current_user["sub"]
    batch_manager = get_batch_manager()
    cache_mode = "annotation" if mode == "annotation" else "verification"

    success, message, batch = batch_manager.assign_batch_to_user(username, mode=mode)

    if not success:
        return {
            "images": [],
            "status": "no_batches",
            "message": f'No new batches available: {message}'
        }

    dataset_name = batch.get('dataset_name', '')
    dataset_obj = db.query(Datasets).filter(Datasets.dataset_name == dataset_name).first()
    instructions = dataset_obj.instructions if dataset_obj and dataset_obj.instructions else ""
    
    # Load labels for verification mode
    batch_labels = {}
    if mode == 'verification' and dataset_obj and dataset_obj.label_folder_path:
        try:
            from core.nas_connector import get_ftp_connector
            connector = await get_ftp_connector()
            if connector:
                file_bytes = await connector.get_file_content(dataset_obj.label_folder_path)
                if file_bytes:
                    batch_labels = json.loads(file_bytes.decode('utf-8'))
                    logger.info(f"Successfully loaded {len(batch_labels)} labels for new batch")
        except Exception as e:
            logger.error(f"Error loading labels for new batch: {e}")
    
    batch_data = {
        'id': f"{username}_{mode}_{batch['batch_name']}",
        'batch_name': batch['batch_name'],
        'images': batch['images'],
        'image_count': batch['image_count'],
        'username': username,
        'mode': mode,
        'instructions': instructions,
        'labels': batch_labels  # Store labels in cache
    }
    await cache_batch(batch_data['id'], batch_data)
    await cache_user_batch(username, batch_data['id'], cache_mode)
    
    logger.info(f"Starting prefetch for newly assigned batch {batch_data['id']}")
    prefetch_batch_images_async(batch_data)

    return {
        "status": "success",
        "message": f'New batch assigned: {message}',
        "batch": batch
}

@router.get("/image/{image_path:path}")
async def get_image(image_path: str):
    """Get an image from cache or storage"""
    try:
        result = await get_image_from_storage(image_path, include_response_time=True)
        return result
    except HTTPException:
        # Re-raise HTTP exceptions (like 503, 404) without modification
        raise
    except Exception as e:
        logger.error(f"Error serving image {image_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to serve image: {str(e)}")


@router.post("/save-labels")
async def save_labels(
    data: SaveLabelsRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Save labels for a session and clean up cache"""
    try:
        username = current_user["sub"]
        labels = data.labels
        is_verification_mode = data.verification_mode
        batch_name = data.batch_name

        # Save labels via batch service
        batch_manager = get_batch_manager()
        mode = "verification" if is_verification_mode else "annotation"
        success, msg = await batch_manager.save_batch_labels(db, labels, username, batch_name, mode)
        
        if success:
            # Clean up cache after successful completion
            cache_mode = "annotation" if mode == "annotation" else "verification"
            await delete_user_batch_cache(username, cache_mode)
            logger.info(f"Cleaned up cache for completed batch")
            
            return {"success": True}
        
        raise HTTPException(status_code=500, detail=f"Failed to save {mode} labels: {msg}")

    except Exception as e:
        logger.error(f"Error in save_labels: {str(e)}")
        raise HTTPException(status_code=500, detail=f'Failed to save labels: {str(e)}')