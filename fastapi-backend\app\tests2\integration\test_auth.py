import pytest
from main import app
from fastapi.testclient import TestClient
from db.models.user import User

# Use shared client and db_session fixtures from conftest

def test_register_login_me(client, db_session):
    payload = {"username": "testuser", "password": "Testpass123!", "confirm_password": "Testpass123!", "role": "annotator", "full_name": "Test User", "email": "<EMAIL>"}
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code == 200
    resp = client.post('/api/auth/login', data={"username": "testuser", "password": "Testpass123!"})
    assert resp.status_code == 200
    token = resp.json().get("access_token")
    refresh_token = resp.json().get("refresh_token")
    assert token and refresh_token
    cookies = resp.cookies
    resp = client.get('/api/auth/me', cookies=cookies)
    assert resp.status_code == 200
    assert resp.json()["username"] == "testuser"
    # Test /api/auth/verify happy-path
    resp = client.get('/api/auth/verify', cookies=cookies)
    assert resp.status_code == 200
    assert resp.json().get("authenticated") is True
    assert resp.json()["user"]["username"] == "testuser"
    # Test /api/auth/refresh-token (body)
    resp = client.post('/api/auth/refresh-token', json={"refresh_token": refresh_token})
    assert resp.status_code == 200
    assert "access_token" in resp.json()
    # Test /api/auth/refresh (cookie)
    client.cookies.set("refresh_token", refresh_token)
    resp = client.post('/api/auth/refresh')
    assert resp.status_code == 200 or resp.status_code == 401
    # Invalid token (body)
    resp = client.post('/api/auth/refresh-token', json={"refresh_token": "badtoken"})
    assert resp.status_code == 401
    # Invalid token (cookie)
    # Clear existing cookies so only the bad token is sent
    client.cookies.clear()
    client.cookies.set("refresh_token", "badtoken")
    resp = client.post('/api/auth/refresh')
    assert resp.status_code == 401
    # Missing token (body)
    resp = client.post('/api/auth/refresh-token', json={})
    assert resp.status_code == 401 or resp.status_code == 422
    # Missing token (cookie)
    client.cookies.clear()
    resp = client.post('/api/auth/refresh')
    assert resp.status_code == 401

def test_register_validation(client):
    payload = {"username": "", "password": "", "confirm_password": "", "role": "", "full_name": "", "email": "notanemail"}
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code == 422

def test_login_wrong_password(client):
    resp = client.post('/api/auth/login', data={"username": "testuser", "password": "wrongpass"})
    assert resp.status_code == 401

def test_change_password(client, db_session):
    # Register and login
    payload = {"username": "changepw", "password": "Oldpass123!", "confirm_password": "Oldpass123!", "role": "annotator", "full_name": "Change PW", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=payload)
    resp = client.post('/api/auth/login', data={"username": "changepw", "password": "Oldpass123!"})
    cookies = resp.cookies
    # Change password
    resp = client.post('/api/auth/change-password', json={"current_password": "Oldpass123!", "new_password": "Newpass123!", "confirm_password": "Newpass123!"}, cookies=cookies)
    assert resp.status_code == 200
    # Login with new password
    resp = client.post('/api/auth/login', data={"username": "changepw", "password": "Newpass123!"})
    assert resp.status_code == 200

def test_username_length_boundaries(client, db_session):
    # Min length (1)
    payload = {"username": "a", "password": "Testpass123!", "confirm_password": "Testpass123!", "role": "annotator", "full_name": "A", "email": "<EMAIL>"}
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code in (200, 400)  # Accepts or rejects short usernames
    # Max length (32)
    uname = "u" * 32
    payload["username"] = uname
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code in (200, 400)
    # Above max (33)
    uname = "u" * 33
    payload["username"] = uname
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code == 400

def test_password_length_boundaries(client, db_session):
    # Min length (8)
    payload = {"username": "minpw", "password": "12345678", "confirm_password": "12345678", "role": "annotator", "full_name": "A", "email": "<EMAIL>"}
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code in (200, 400)
    # Max length (32)
    payload["username"] = "maxpw"
    payload["password"] = payload["confirm_password"] = "x" * 32
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code in (200, 400)
    # Above max (33)
    payload["username"] = "overpw"
    payload["password"] = payload["confirm_password"] = "x" * 33
    resp = client.post('/api/auth/register', json=payload)
    assert resp.status_code == 422

def test_logout(client):
    # Register a test user
    payload = {"username": "logoutuser", "password": "Logout123!", "confirm_password": "Logout123!", "role": "annotator", "full_name": "Logout User", "email": "<EMAIL>"}
    register_resp = client.post('/api/auth/register', json=payload)
    assert register_resp.status_code == 200
    login_resp = client.post('/api/auth/login', data={"username": payload["username"], "password": payload["password"]})
    assert login_resp.status_code == 200
    # Ensure cookies are set
    assert "access_token" in client.cookies
    assert "refresh_token" in client.cookies
    # Perform logout
    logout_resp = client.post('/api/auth/logout')
    assert logout_resp.status_code == 200
    # Ensure cookies are removed
    assert "access_token" not in client.cookies
    assert "refresh_token" not in client.cookies

def test_logout_idempotent(client):
    """Logout should succeed even when no session exists, and clear cookies idempotently."""
    # Ensure no cookies present
    client.cookies.clear()
    resp1 = client.post('/api/auth/logout')
    assert resp1.status_code == 200
    assert 'access_token' not in client.cookies
    assert 'refresh_token' not in client.cookies
    # Call again for idempotency
    resp2 = client.post('/api/auth/logout')
    assert resp2.status_code == 200
    assert 'access_token' not in client.cookies
    assert 'refresh_token' not in client.cookies

def test_verify_token_missing_and_expired(client):
    """GET /api/auth/verify should return 401 if access_token cookie is missing or expired."""
    client.cookies.clear()
    missing = client.get('/api/auth/verify')
    assert missing.status_code == 401

    # Expired token
    from datetime import timedelta
    from core.security import create_token
    expired = create_token(data={'sub': 'dummy'}, expires_delta=timedelta(minutes=-5), token_type="access")
    client.cookies.set('access_token', expired)
    expired_resp = client.get('/api/auth/verify')
    assert expired_resp.status_code == 401

def test_change_password_wrong_and_unauthenticated(client, db_session):
    """POST /api/auth/change-password should 400 on wrong current, 401 when unauthenticated."""
    # Register and login
    payload = {"username": "cpuser", "password": "Oldpass123!", "confirm_password": "Oldpass123!", "role": "annotator", "full_name": "CP User", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=payload)
    login = client.post('/api/auth/login', data={"username": "cpuser", "password": "Oldpass123!"})
    cookies = login.cookies
    # Wrong current password
    wrong = client.post('/api/auth/change-password', json={"current_password": "wrong", "new_password": "Newpass123!", "confirm_password": "Newpass123!"}, cookies=cookies)
    assert wrong.status_code == 400
    # Unauthenticated
    client.cookies.clear()
    noauth = client.post('/api/auth/change-password', json={"current_password": "Oldpass123!", "new_password": "Newpass123!", "confirm_password": "Newpass123!"})
    assert noauth.status_code == 401

def test_me_deactivated_and_deleted(client, db_session):
    """GET /api/auth/me should return 200 for deactivated user and 401 for deleted user."""
    # Deactivated user
    payload = {"username": "du", "password": "Du123456!", "confirm_password": "Du123456!", "role": "annotator", "full_name": "DU", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=payload)
    login = client.post('/api/auth/login', data={"username": "du", "password": "Du123456!"})
    cookies = login.cookies
    # Deactivate user
    u = db_session.query(User).filter_by(username="du").one()
    u.is_active = False
    db_session.commit()
    deact = client.get('/api/auth/me', cookies=cookies)
    # Deactivated users currently still receive profile
    assert deact.status_code == 200

    # Deleted user
    # Register new user
    payload2 = {"username": "deluser", "password": "Del123456!", "confirm_password": "Del123456!", "role": "annotator", "full_name": "Del User", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=payload2)
    login2 = client.post('/api/auth/login', data={"username": "deluser", "password": "Del123456!"})
    cookies2 = login2.cookies
    # Delete from DB
    del_u = db_session.query(User).filter_by(username="deluser").one()
    db_session.delete(del_u)
    db_session.commit()
    deleted = client.get('/api/auth/me', cookies=cookies2)
    assert deleted.status_code == 401 