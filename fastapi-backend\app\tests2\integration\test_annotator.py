import pytest
from main import app
from fastapi.testclient import Test<PERSON>lient
from db import db_connector
from db.models.image_annotation import ImageAnnotation
from db.models.user import User, AnnotatorMode

@pytest.fixture(scope='function')
def annotator_client(db_session):
    with Test<PERSON><PERSON>(app) as client:
        payload = {"username": "annouser", "password": "Annopass123!", "confirm_password": "Annopass123!", "role": "annotator", "full_name": "Anno User", "email": "<EMAIL>"}
        reg_resp = client.post('/api/auth/register', json=payload)
        assert reg_resp.status_code == 200, f"Registration failed: {reg_resp.text}"
        login_resp = client.post('/api/auth/login', data={"username": "annouser", "password": "Annopass123!"})
        assert login_resp.status_code == 200, f"Login failed: {login_resp.text}"
        yield client

def test_dashboard(annotator_client):
    resp = annotator_client.get('/api/annotator/dashboard')
    assert resp.status_code == 200
    assert "annotation_mode" in resp.json()

def test_annotate(annotator_client):
    resp = annotator_client.get('/api/annotator/annotate')
    assert resp.status_code in (200, 204)

def test_next_set(annotator_client):
    resp = annotator_client.get('/api/annotator/next-set')
    assert resp.status_code == 200

def test_save_labels(annotator_client):
    payload = {"labels": {"img1": "label1"}, "verification_mode": False, "batch_name": "batch1"}
    resp = annotator_client.post('/api/annotator/save-labels', json=payload)
    assert resp.status_code in (200, 500)  # 500 if no batch assigned
    if resp.status_code == 200:
        db = db_connector.SessionLocal()
        record = db.query(ImageAnnotation).filter_by(dataset_batch_name="batch1").first()
        assert record is not None
        db.close()

def test_label_length_boundaries(annotator_client):
    payload = {"labels": {"img1": "a"}, "verification_mode": False, "batch_name": "batch1"}
    resp = annotator_client.post('/api/annotator/save-labels', json=payload)
    assert resp.status_code in (200, 400, 500)
    payload["labels"]["img1"] = "l" * 256
    resp = annotator_client.post('/api/annotator/save-labels', json=payload)
    assert resp.status_code in (200, 400, 500)
    payload["labels"]["img1"] = "l" * 257
    resp = annotator_client.post('/api/annotator/save-labels', json=payload)
    assert resp.status_code in (400, 500)

def test_batch_size_boundaries(annotator_client):
    payload = {"labels": {f"img{i}": "label" for i in range(1)}, "verification_mode": False, "batch_name": "batchmin"}
    resp = annotator_client.post('/api/annotator/save-labels', json=payload)
    assert resp.status_code in (200, 400, 500)
    payload["labels"] = {f"img{i}": "label" for i in range(50)}
    payload["batch_name"] = "batchmax"
    resp = annotator_client.post('/api/annotator/save-labels', json=payload)
    assert resp.status_code in (200, 400, 500)
    payload["labels"] = {f"img{i}": "label" for i in range(51)}
    payload["batch_name"] = "batchover"
    resp = annotator_client.post('/api/annotator/save-labels', json=payload)
    assert resp.status_code in (400, 500)

class FakeConnector:
    """Fake FTP connector returning preset image bytes."""
    async def get_file_content(self, path):
        return b'fake-image-bytes'


def test_image_cache_fallback(monkeypatch, annotator_client):
    """
    Ensure image retrieval falls back from Redis cache to FTP and then caches results,
    and that subsequent calls use the Redis cache.
    """
    image_path = 'folder/test.jpg'
    # Simulate cache miss in image_processing (Redis disabled)
    async def fake_is_image_cached(p):
        return False
    monkeypatch.setattr('utils.image_processing.is_image_cached', fake_is_image_cached, raising=True)

    async def fake_get_cached_image(p):
        return None
    monkeypatch.setattr('utils.image_processing.get_cached_image', fake_get_cached_image, raising=True)
    # Capture cached content via image_processing.cache_image
    cached = {}
    async def fake_cache_image(path, content):
        cached['data'] = content
        return True
    monkeypatch.setattr('utils.image_processing.cache_image', fake_cache_image, raising=True)
    # Patch FTP connector in image_processing
    async def fake_get_ftp_connector():
        return FakeConnector()
    monkeypatch.setattr('utils.image_processing.get_ftp_connector', fake_get_ftp_connector, raising=True)

    # First call: expect FTP fallback
    resp = annotator_client.get(f'/api/annotator/image/{image_path}')
    assert resp.status_code == 200
    assert resp.headers.get('X-Source') == 'FTP'
    assert resp.content == b'fake-image-bytes'
    # Verify caching occurred
    assert cached.get('data') == b'fake-image-bytes'

    # Simulate cache hit in image_processing
    async def hit_is_image_cached(p):
        return True
    monkeypatch.setattr('utils.image_processing.is_image_cached', hit_is_image_cached, raising=True)
    async def hit_get_cached_image(p):
        return b'cached-bytes'
    monkeypatch.setattr('utils.image_processing.get_cached_image', hit_get_cached_image, raising=True)

    # Second call: expect Redis-Cache source
    resp2 = annotator_client.get(f'/api/annotator/image/{image_path}')
    assert resp2.status_code == 200
    assert resp2.headers.get('X-Source') == 'Redis-Cache'
    assert resp2.content == b'cached-bytes'

def test_unauthorized_annotator_endpoints():
    """All annotator endpoints should return 401 when no access token is provided."""
    client = TestClient(app)
    # List of (method, path, kwargs)
    endpoints = [
        ('get', '/api/annotator/dashboard', {}),
        ('get', '/api/annotator/annotate', {}),
        ('get', '/api/annotator/next-set', {}),
        ('get', '/api/annotator/image/test.jpg', {}),
        ('post', '/api/annotator/save-labels', {'json': {'labels': {}, 'verification_mode': False, 'batch_name': 'batch'}}),
    ]
    for method, path, kwargs in endpoints:
        resp = getattr(client, method)(path, **kwargs)
        assert resp.status_code == 401, f"Unauthorized {path} returned {resp.status_code}"


def test_invalid_image_path_traversal(annotator_client):
    """Requests with path traversal should return 400 Bad Request."""
    resp = annotator_client.get('/api/annotator/image/../etc/passwd')
    # Path traversal may be normalized to a missing route, resulting in 404
    assert resp.status_code in (400, 404)

def test_mode_switching_annotation_mode(annotator_client, db_session):
    """Dashboard should reflect changes to user's annotator_mode (annotation vs verification)."""
    # Switch mode to verification
    user = db_session.query(User).filter_by(username="annouser").one()
    user.annotator_mode = AnnotatorMode.VERIFICATION
    db_session.commit()
    resp = annotator_client.get('/api/annotator/dashboard')
    assert resp.status_code == 200
    assert resp.json().get("annotation_mode") == "verification"

    # Switch back to annotation
    user.annotator_mode = AnnotatorMode.ANNOTATION
    db_session.commit()
    resp2 = annotator_client.get('/api/annotator/dashboard')
    assert resp2.json().get("annotation_mode") == "annotation"


def test_annotate_no_tasks(monkeypatch, annotator_client):
    """When no tasks are available, annotate endpoint should return 204."""
    async def fake_get_batch_for_user(db, username, mode):
        return [], {}, "", None
    # Patch the batch retrieval to simulate no tasks
    monkeypatch.setattr(
        'services.annotator_service.annotator_service.get_batch_for_user',
        fake_get_batch_for_user,
        raising=True
    )
    resp = annotator_client.get('/api/annotator/annotate')
    assert resp.status_code == 204 