/**
 * Frontend Logic Tests - Utility Functions
 * Tests for utility functions used throughout the application
 */

// Mock react-hot-toast
const mockToast = {
  success: jest.fn(),
  error: jest.fn(),
  loading: jest.fn(),
  promise: jest.fn(),
  dismiss: jest.fn(),
};

jest.mock('react-hot-toast', () => ({
  __esModule: true,
  default: mockToast,
}));

// Mock showToast utility (extracted from toast.ts)
const showToast = {
  success: (message: string) => {
    mockToast.success(message, {
      duration: 4000,
      position: 'top-right',
      style: {
        background: '#10B981',
        color: '#fff',
        fontWeight: '500',
      },
      iconTheme: {
        primary: '#fff',
        secondary: '#10B981',
      },
    });
  },

  error: (message: string) => {
    mockToast.error(message, {
      duration: 5000,
      position: 'top-right',
      style: {
        background: '#EF4444',
        color: '#fff',
        fontWeight: '500',
      },
      iconTheme: {
        primary: '#fff',
        secondary: '#EF4444',
      },
    });
  },

  info: (message: string) => {
    mockToast(message, {
      duration: 4000,
      position: 'top-right',
      icon: 'ℹ️',
      style: {
        background: '#3B82F6',
        color: '#fff',
        fontWeight: '500',
      },
    });
  },

  warning: (message: string) => {
    mockToast(message, {
      duration: 4000,
      position: 'top-right',
      icon: '⚠️',
      style: {
        background: '#F59E0B',
        color: '#fff',
        fontWeight: '500',
      },
    });
  },

  loading: (message: string) => {
    return mockToast.loading(message, {
      position: 'top-right',
      style: {
        background: '#6B7280',
        color: '#fff',
        fontWeight: '500',
      },
    });
  },

  dismiss: (toastId?: string) => {
    mockToast.dismiss(toastId);
  },

  promise: <T,>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    }
  ) => {
    return mockToast.promise(promise, messages, {
      position: 'top-right',
      style: {
        fontWeight: '500',
      },
      success: {
        style: {
          background: '#10B981',
          color: '#fff',
        },
      },
      error: {
        style: {
          background: '#EF4444',
          color: '#fff',
        },
      },
      loading: {
        style: {
          background: '#6B7280',
          color: '#fff',
        },
      },
    });
  },
};

// Mock authFetch utility
const authFetch = async (input: RequestInfo, init: RequestInit = {}) => {
  const headers = new Headers(init.headers || {});
  
  return fetch(input, {
    ...init,
    headers,
    credentials: 'include',
  });
};

// Mock CSS class utilities (extracted from AuditorclassNames.ts)
const primaryBgClass = 'bg-[#0D47A1]';
const primaryHoverBgClass = 'hover:bg-[#1159B8]';
const primaryActiveBgClass = 'bg-[#0B3A88]';

const getSidebarClasses = (collapsed: boolean) => {
  const bgColor = primaryBgClass;
  return `${bgColor} text-white transition-all duration-300 flex flex-col ${collapsed ? 'w-28' : 'w-64'}`;
};

const getHeaderClasses = (collapsed: boolean) => {
  return `flex items-center py-4 gap-4 ${collapsed ? 'justify-center px-0' : 'justify-start px-6'}`;
};

const getNavItemClasses = (
  collapsed: boolean,
  isActive: boolean,
  fullWidth: boolean = true
) => {
  const hoverBg = primaryHoverBgClass;
  const activeBg = primaryActiveBgClass;
  const justify = collapsed ? 'justify-center' : 'justify-start';
  const width = fullWidth ? 'w-full' : '';
  return [
    'flex',
    width,
    'items-center text-white no-underline hover:no-underline text-lg',
    justify,
    'px-6 py-4 gap-4',
    hoverBg,
    'rounded',
    isActive ? activeBg : '',
  ]
    .filter(Boolean)
    .join(' ');
};

// Mock global fetch
global.fetch = jest.fn();

describe('Frontend Logic Tests - Utility Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Toast Utility Functions', () => {
    it('should call success toast with correct parameters', () => {
      const message = 'Operation successful!';
      
      showToast.success(message);
      
      expect(mockToast.success).toHaveBeenCalledWith(message, {
        duration: 4000,
        position: 'top-right',
        style: {
          background: '#10B981',
          color: '#fff',
          fontWeight: '500',
        },
        iconTheme: {
          primary: '#fff',
          secondary: '#10B981',
        },
      });
    });

    it('should call error toast with correct parameters', () => {
      const message = 'Something went wrong!';
      
      showToast.error(message);
      
      expect(mockToast.error).toHaveBeenCalledWith(message, {
        duration: 5000,
        position: 'top-right',
        style: {
          background: '#EF4444',
          color: '#fff',
          fontWeight: '500',
        },
        iconTheme: {
          primary: '#fff',
          secondary: '#EF4444',
        },
      });
    });

    it('should call info toast with correct parameters', () => {
      const message = 'Information message';
      
      showToast.info(message);
      
      expect(mockToast).toHaveBeenCalledWith(message, {
        duration: 4000,
        position: 'top-right',
        icon: 'ℹ️',
        style: {
          background: '#3B82F6',
          color: '#fff',
          fontWeight: '500',
        },
      });
    });

    it('should call warning toast with correct parameters', () => {
      const message = 'Warning message';
      
      showToast.warning(message);
      
      expect(mockToast).toHaveBeenCalledWith(message, {
        duration: 4000,
        position: 'top-right',
        icon: '⚠️',
        style: {
          background: '#F59E0B',
          color: '#fff',
          fontWeight: '500',
        },
      });
    });

    it('should call loading toast and return toast ID', () => {
      const message = 'Loading...';
      const mockToastId = 'toast-123';
      mockToast.loading.mockReturnValue(mockToastId);
      
      const result = showToast.loading(message);
      
      expect(mockToast.loading).toHaveBeenCalledWith(message, {
        position: 'top-right',
        style: {
          background: '#6B7280',
          color: '#fff',
          fontWeight: '500',
        },
      });
      expect(result).toBe(mockToastId);
    });

    it('should dismiss toast with ID', () => {
      const toastId = 'toast-123';
      
      showToast.dismiss(toastId);
      
      expect(mockToast.dismiss).toHaveBeenCalledWith(toastId);
    });

    it('should dismiss all toasts when no ID provided', () => {
      showToast.dismiss();
      
      expect(mockToast.dismiss).toHaveBeenCalledWith(undefined);
    });

    it('should handle promise toast with string messages', () => {
      const promise = Promise.resolve('success');
      const messages = {
        loading: 'Loading...',
        success: 'Success!',
        error: 'Error occurred'
      };
      
      showToast.promise(promise, messages);
      
      expect(mockToast.promise).toHaveBeenCalledWith(promise, messages, expect.any(Object));
    });

    it('should handle promise toast with function messages', () => {
      const promise = Promise.resolve({ data: 'test' });
      const messages = {
        loading: 'Loading...',
        success: (data: any) => `Success: ${data.data}`,
        error: (error: any) => `Error: ${error.message}`
      };
      
      showToast.promise(promise, messages);
      
      expect(mockToast.promise).toHaveBeenCalledWith(promise, messages, expect.any(Object));
    });
  });

  describe('AuthFetch Utility', () => {
    it('should make fetch request with credentials included', async () => {
      const mockResponse = { ok: true, json: () => Promise.resolve({}) };
      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);
      
      const url = 'http://localhost:5000/api/test';
      const options = { method: 'GET' };
      
      await authFetch(url, options);
      
      expect(global.fetch).toHaveBeenCalledWith(url, {
        ...options,
        headers: expect.any(Headers),
        credentials: 'include',
      });
    });

    it('should preserve existing headers', async () => {
      const mockResponse = { ok: true, json: () => Promise.resolve({}) };
      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);
      
      const url = 'http://localhost:5000/api/test';
      const options = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer token'
        }
      };
      
      await authFetch(url, options);
      
      expect(global.fetch).toHaveBeenCalledWith(url, {
        ...options,
        headers: expect.any(Headers),
        credentials: 'include',
      });
    });

    it('should work with no options provided', async () => {
      const mockResponse = { ok: true, json: () => Promise.resolve({}) };
      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);
      
      const url = 'http://localhost:5000/api/test';
      
      await authFetch(url);
      
      expect(global.fetch).toHaveBeenCalledWith(url, {
        headers: expect.any(Headers),
        credentials: 'include',
      });
    });
  });

  describe('CSS Class Utilities', () => {
    describe('getSidebarClasses', () => {
      it('should return expanded sidebar classes when not collapsed', () => {
        const result = getSidebarClasses(false);
        
        expect(result).toContain('w-64');
        expect(result).toContain(primaryBgClass);
        expect(result).toContain('text-white');
        expect(result).toContain('transition-all');
        expect(result).toContain('duration-300');
        expect(result).toContain('flex');
        expect(result).toContain('flex-col');
      });

      it('should return collapsed sidebar classes when collapsed', () => {
        const result = getSidebarClasses(true);
        
        expect(result).toContain('w-28');
        expect(result).toContain(primaryBgClass);
        expect(result).not.toContain('w-64');
      });
    });

    describe('getHeaderClasses', () => {
      it('should return expanded header classes when not collapsed', () => {
        const result = getHeaderClasses(false);
        
        expect(result).toContain('justify-start');
        expect(result).toContain('px-6');
        expect(result).toContain('flex');
        expect(result).toContain('items-center');
        expect(result).toContain('py-4');
        expect(result).toContain('gap-4');
      });

      it('should return collapsed header classes when collapsed', () => {
        const result = getHeaderClasses(true);
        
        expect(result).toContain('justify-center');
        expect(result).toContain('px-0');
        expect(result).not.toContain('justify-start');
        expect(result).not.toContain('px-6');
      });
    });

    describe('getNavItemClasses', () => {
      it('should return nav item classes for expanded, inactive state', () => {
        const result = getNavItemClasses(false, false, true);
        
        expect(result).toContain('w-full');
        expect(result).toContain('justify-start');
        expect(result).toContain(primaryHoverBgClass);
        expect(result).not.toContain(primaryActiveBgClass);
      });

      it('should return nav item classes for collapsed, active state', () => {
        const result = getNavItemClasses(true, true, true);
        
        expect(result).toContain('w-full');
        expect(result).toContain('justify-center');
        expect(result).toContain(primaryHoverBgClass);
        expect(result).toContain(primaryActiveBgClass);
      });

      it('should return nav item classes without full width', () => {
        const result = getNavItemClasses(false, false, false);
        
        expect(result).not.toContain('w-full');
        expect(result).toContain('justify-start');
      });

      it('should include all base classes', () => {
        const result = getNavItemClasses(false, false, true);
        
        expect(result).toContain('flex');
        expect(result).toContain('items-center');
        expect(result).toContain('text-white');
        expect(result).toContain('no-underline');
        expect(result).toContain('hover:no-underline');
        expect(result).toContain('text-lg');
        expect(result).toContain('px-6');
        expect(result).toContain('py-4');
        expect(result).toContain('gap-4');
        expect(result).toContain('rounded');
      });
    });
  });
});
