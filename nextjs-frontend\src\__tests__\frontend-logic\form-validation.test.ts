/**
 * Frontend Logic Tests - Form Validation Functions
 * Tests for all form validation logic used throughout the application
 */

// Mock validation functions extracted from components
const validateRegisterForm = (formData: {
  username: string;
  password: string;
  confirmPassword: string;
  email: string;
}) => {
  const errors: {[key: string]: string} = {};
  
  // Username validation (letters, numbers, underscores only)
  if (!formData.username) {
    errors.username = 'Username is required';
  } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
    errors.username = 'Username can only contain letters, numbers, and underscores';
  }
  
  // Password validation
  if (!formData.password) {
    errors.password = 'Password is required';
  } else if (formData.password.length < 8) {
    errors.password = 'Password must be at least 8 characters long';
  }
  
  // Confirm password validation
  if (formData.password !== formData.confirmPassword) {
    errors.confirmPassword = 'Passwords do not match';
  }
  
  // Email validation (if provided)
  if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
    errors.email = 'Please enter a valid email address';
  }
  
  return errors;
};

const validatePasswordForm = (formData: {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}) => {
  const errors: {[key: string]: string} = {};
  
  // Current password validation
  if (!formData.currentPassword) {
    errors.currentPassword = 'Current password is required';
  }
  
  // New password validation
  if (!formData.newPassword) {
    errors.newPassword = 'New password is required';
  } else if (formData.newPassword.length < 6) {
    errors.newPassword = 'New password must be at least 6 characters long';
  }
  
  // Confirm password validation
  if (formData.newPassword !== formData.confirmPassword) {
    errors.confirmPassword = 'Passwords do not match';
  }
  
  return errors;
};

const validateClientForm = (clientForm: {
  username: string;
  fullName: string;
  email: string;
  password: string;
  confirmPassword: string;
}) => {
  const errors: any = {};
  
  if (!clientForm.username.trim()) errors.username = 'Username is required';
  if (!clientForm.fullName.trim()) errors.fullName = 'Full name is required';
  if (!clientForm.email.trim()) errors.email = 'Email is required';
  if (!clientForm.password) errors.password = 'Password is required';
  if (!clientForm.confirmPassword) errors.confirmPassword = 'Please confirm password';
  
  if (clientForm.username && !/^[a-zA-Z0-9_]+$/.test(clientForm.username)) {
    errors.username = 'Username can only contain letters, numbers, and underscores';
  }
  
  if (clientForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(clientForm.email)) {
    errors.email = 'Please enter a valid email address';
  }
  
  if (clientForm.password && clientForm.password.length < 8) {
    errors.password = 'Password must be at least 8 characters';
  }
  
  if (clientForm.password !== clientForm.confirmPassword) {
    errors.confirmPassword = 'Passwords do not match';
  }
  
  return errors;
};

const validateUserManagementForm = (newUser: {
  username: string;
  fullName: string;
  email: string;
  password: string;
  confirmPassword: string;
}) => {
  const validationErrors: any = {};
  
  // Required field checks
  if (!newUser.username.trim()) {
    validationErrors.username = 'Username is required.';
  }
  if (!newUser.fullName.trim()) {
    validationErrors.fullName = 'Full name is required.';
  }
  if (!newUser.password) {
    validationErrors.password = 'Password is required.';
  }
  if (!newUser.confirmPassword) {
    validationErrors.confirmPassword = 'Please confirm your password.';
  }
  if (!newUser.email.trim()) {
    validationErrors.email = 'Email is required.';
  }
  
  const usernamePattern = /^[a-zA-Z0-9_]+$/;
  
  // Format and match validations
  if (newUser.username && !usernamePattern.test(newUser.username)) {
    validationErrors.username = validationErrors.username || 'Invalid username format.';
  }
  if (newUser.password && newUser.password.length < 8) {
    validationErrors.password = validationErrors.password || 'Password must be at least 8 characters.';
  }
  if (newUser.password && newUser.confirmPassword && newUser.password !== newUser.confirmPassword) {
    validationErrors.confirmPassword = 'Passwords do not match.';
  }
  
  // Email validation helper
  const isValidEmail = (email: string) => /\S+@\S+\.\S+/.test(email);
  if (newUser.email && !isValidEmail(newUser.email)) {
    validationErrors.email = 'Please enter a valid email address.';
  }
  
  return validationErrors;
};

describe('Frontend Logic Tests - Form Validation', () => {
  describe('Register Form Validation', () => {
    it('should validate required fields', () => {
      const formData = {
        username: '',
        password: '',
        confirmPassword: '',
        email: ''
      };
      
      const errors = validateRegisterForm(formData);
      
      expect(errors.username).toBe('Username is required');
      expect(errors.password).toBe('Password is required');
    });

    it('should validate username format', () => {
      const formData = {
        username: 'invalid-username!',
        password: 'validpassword123',
        confirmPassword: 'validpassword123',
        email: '<EMAIL>'
      };
      
      const errors = validateRegisterForm(formData);
      
      expect(errors.username).toBe('Username can only contain letters, numbers, and underscores');
    });

    it('should validate password length', () => {
      const formData = {
        username: 'validuser',
        password: '123',
        confirmPassword: '123',
        email: '<EMAIL>'
      };
      
      const errors = validateRegisterForm(formData);
      
      expect(errors.password).toBe('Password must be at least 8 characters long');
    });

    it('should validate password confirmation', () => {
      const formData = {
        username: 'validuser',
        password: 'validpassword123',
        confirmPassword: 'differentpassword',
        email: '<EMAIL>'
      };
      
      const errors = validateRegisterForm(formData);
      
      expect(errors.confirmPassword).toBe('Passwords do not match');
    });

    it('should validate email format', () => {
      const formData = {
        username: 'validuser',
        password: 'validpassword123',
        confirmPassword: 'validpassword123',
        email: 'invalid-email'
      };
      
      const errors = validateRegisterForm(formData);
      
      expect(errors.email).toBe('Please enter a valid email address');
    });

    it('should pass validation with valid data', () => {
      const formData = {
        username: 'validuser123',
        password: 'validpassword123',
        confirmPassword: 'validpassword123',
        email: '<EMAIL>'
      };
      
      const errors = validateRegisterForm(formData);
      
      expect(Object.keys(errors)).toHaveLength(0);
    });
  });

  describe('Password Change Form Validation', () => {
    it('should validate required fields', () => {
      const formData = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
      
      const errors = validatePasswordForm(formData);
      
      expect(errors.currentPassword).toBe('Current password is required');
      expect(errors.newPassword).toBe('New password is required');
    });

    it('should validate new password length', () => {
      const formData = {
        currentPassword: 'oldpass',
        newPassword: '123',
        confirmPassword: '123'
      };
      
      const errors = validatePasswordForm(formData);
      
      expect(errors.newPassword).toBe('New password must be at least 6 characters long');
    });

    it('should validate password confirmation', () => {
      const formData = {
        currentPassword: 'oldpass',
        newPassword: 'newpassword',
        confirmPassword: 'differentpass'
      };
      
      const errors = validatePasswordForm(formData);
      
      expect(errors.confirmPassword).toBe('Passwords do not match');
    });

    it('should pass validation with valid data', () => {
      const formData = {
        currentPassword: 'oldpassword',
        newPassword: 'newpassword123',
        confirmPassword: 'newpassword123'
      };
      
      const errors = validatePasswordForm(formData);
      
      expect(Object.keys(errors)).toHaveLength(0);
    });
  });

  describe('Client Registration Form Validation', () => {
    it('should validate all required fields', () => {
      const clientForm = {
        username: '',
        fullName: '',
        email: '',
        password: '',
        confirmPassword: ''
      };
      
      const errors = validateClientForm(clientForm);
      
      expect(errors.username).toBe('Username is required');
      expect(errors.fullName).toBe('Full name is required');
      expect(errors.email).toBe('Email is required');
      expect(errors.password).toBe('Password is required');
      expect(errors.confirmPassword).toBe('Please confirm password');
    });

    it('should validate username format', () => {
      const clientForm = {
        username: 'invalid@username',
        fullName: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };
      
      const errors = validateClientForm(clientForm);
      
      expect(errors.username).toBe('Username can only contain letters, numbers, and underscores');
    });

    it('should validate email format', () => {
      const clientForm = {
        username: 'testuser',
        fullName: 'Test User',
        email: 'invalid-email',
        password: 'password123',
        confirmPassword: 'password123'
      };
      
      const errors = validateClientForm(clientForm);
      
      expect(errors.email).toBe('Please enter a valid email address');
    });

    it('should pass validation with valid data', () => {
      const clientForm = {
        username: 'testclient',
        fullName: 'Test Client User',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };
      
      const errors = validateClientForm(clientForm);
      
      expect(Object.keys(errors)).toHaveLength(0);
    });
  });

  describe('User Management Form Validation', () => {
    it('should validate all required fields', () => {
      const newUser = {
        username: '',
        fullName: '',
        email: '',
        password: '',
        confirmPassword: ''
      };
      
      const errors = validateUserManagementForm(newUser);
      
      expect(errors.username).toBe('Username is required.');
      expect(errors.fullName).toBe('Full name is required.');
      expect(errors.email).toBe('Email is required.');
      expect(errors.password).toBe('Password is required.');
      expect(errors.confirmPassword).toBe('Please confirm your password.');
    });

    it('should validate username format', () => {
      const newUser = {
        username: 'invalid-user!',
        fullName: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };
      
      const errors = validateUserManagementForm(newUser);
      
      expect(errors.username).toBe('Invalid username format.');
    });

    it('should pass validation with valid data', () => {
      const newUser = {
        username: 'validuser',
        fullName: 'Valid User',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };
      
      const errors = validateUserManagementForm(newUser);
      
      expect(Object.keys(errors)).toHaveLength(0);
    });
  });
});
