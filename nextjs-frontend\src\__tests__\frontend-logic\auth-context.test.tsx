/**
 * Frontend Logic Tests - AuthContext
 * Tests for authentication context state management and logic
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { useRouter, usePathname } from 'next/navigation';

// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}));

// Mock fetch globally
global.fetch = jest.fn();

// Mock AuthContext implementation for testing
interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: string;
  is_active: boolean;
  created_at: string;
  last_login?: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  checkAuth: () => Promise<void>;
  logout: () => Promise<void>;
  login: () => Promise<void>;
  isLoading: boolean;
}

const AuthContext = React.createContext<AuthContextType>({
  isAuthenticated: false,
  user: null,
  checkAuth: async () => {},
  logout: async () => {},
  login: async () => {},
  isLoading: false,
});

const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);
  const [user, setUser] = React.useState<User | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const router = useRouter();
  const pathname = usePathname();

  const checkAuth = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'}/auth/verify`, {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setIsAuthenticated(true);
        setUser(data.user);
      } else {
        setIsAuthenticated(false);
        setUser(null);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setIsAuthenticated(false);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async () => {
    await checkAuth();
  };

  const logout = async () => {
    try {
      await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'}/auth/logout`, {
        method: 'POST',
        credentials: 'include',
      });
      setIsAuthenticated(false);
      setUser(null);
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
      setIsAuthenticated(false);
      setUser(null);
      router.push('/');
    }
  };

  // Only check auth automatically for protected routes
  React.useEffect(() => {
    const protectedRoutes = ['/admin', '/annotator', '/auditor', '/client'];
    const isProtectedRoute = protectedRoutes.some(route => pathname?.startsWith(route));
    
    if (isProtectedRoute) {
      checkAuth();
    }
  }, [pathname]);

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, checkAuth, logout, login, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Test component to access auth context
const TestComponent = () => {
  const { user, isAuthenticated, login, logout, checkAuth, isLoading } = useAuth();
  
  return (
    <div>
      <div data-testid="auth-status">
        {isAuthenticated ? 'authenticated' : 'not-authenticated'}
      </div>
      <div data-testid="user-info">
        {user ? `${user.username} - ${user.role}` : 'no-user'}
      </div>
      <div data-testid="loading-status">
        {isLoading ? 'loading' : 'not-loading'}
      </div>
      <button onClick={login} data-testid="login-btn">
        Login
      </button>
      <button onClick={logout} data-testid="logout-btn">
        Logout
      </button>
      <button onClick={checkAuth} data-testid="check-auth-btn">
        Check Auth
      </button>
    </div>
  );
};

describe('Frontend Logic Tests - AuthContext', () => {
  const mockRouter = {
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (usePathname as jest.Mock).mockReturnValue('/');
    (global.fetch as jest.Mock).mockClear();
  });

  const renderWithAuth = (component: React.ReactElement) => {
    return render(
      <AuthProvider>
        {component}
      </AuthProvider>
    );
  };

  describe('Initial State', () => {
    it('should initialize with unauthenticated state', () => {
      renderWithAuth(<TestComponent />);
      
      expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
      expect(screen.getByTestId('user-info')).toHaveTextContent('no-user');
      expect(screen.getByTestId('loading-status')).toHaveTextContent('not-loading');
    });
  });

  describe('useAuth Hook', () => {
    it('should throw error when used outside AuthProvider', () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => {
        render(<TestComponent />);
      }).toThrow('useAuth must be used within an AuthProvider');
      
      consoleSpy.mockRestore();
    });
  });

  describe('checkAuth Function', () => {
    it('should handle successful authentication', async () => {
      const mockUser = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'admin',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z'
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ user: mockUser })
      });

      renderWithAuth(<TestComponent />);
      
      await act(async () => {
        screen.getByTestId('check-auth-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
        expect(screen.getByTestId('user-info')).toHaveTextContent('testuser - admin');
      });
    });

    it('should handle authentication failure', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401
      });

      renderWithAuth(<TestComponent />);
      
      await act(async () => {
        screen.getByTestId('check-auth-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
        expect(screen.getByTestId('user-info')).toHaveTextContent('no-user');
      });
    });

    it('should handle network errors', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      renderWithAuth(<TestComponent />);
      
      await act(async () => {
        screen.getByTestId('check-auth-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
        expect(screen.getByTestId('user-info')).toHaveTextContent('no-user');
      });
    });

    it('should show loading state during auth check', async () => {
      (global.fetch as jest.Mock).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ ok: true, json: () => ({}) }), 100))
      );

      renderWithAuth(<TestComponent />);
      
      act(() => {
        screen.getByTestId('check-auth-btn').click();
      });

      expect(screen.getByTestId('loading-status')).toHaveTextContent('loading');

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('not-loading');
      });
    });
  });

  describe('login Function', () => {
    it('should call checkAuth when login is triggered', async () => {
      const mockUser = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'annotator',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z'
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ user: mockUser })
      });

      renderWithAuth(<TestComponent />);
      
      await act(async () => {
        screen.getByTestId('login-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
        expect(screen.getByTestId('user-info')).toHaveTextContent('testuser - annotator');
      });
    });
  });

  describe('logout Function', () => {
    it('should handle successful logout', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      });

      renderWithAuth(<TestComponent />);
      
      await act(async () => {
        screen.getByTestId('logout-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
        expect(screen.getByTestId('user-info')).toHaveTextContent('no-user');
        expect(mockRouter.push).toHaveBeenCalledWith('/');
      });
    });

    it('should handle logout failure gracefully', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Logout failed'));

      renderWithAuth(<TestComponent />);
      
      await act(async () => {
        screen.getByTestId('logout-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
        expect(screen.getByTestId('user-info')).toHaveTextContent('no-user');
        expect(mockRouter.push).toHaveBeenCalledWith('/');
      });
    });
  });

  describe('Protected Route Handling', () => {
    it('should automatically check auth for protected routes', async () => {
      (usePathname as jest.Mock).mockReturnValue('/admin/dashboard');
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          user: { 
            id: 1, 
            username: 'admin', 
            role: 'admin',
            email: '<EMAIL>',
            full_name: 'Admin User',
            is_active: true,
            created_at: '2024-01-01T00:00:00Z'
          } 
        })
      });

      renderWithAuth(<TestComponent />);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/auth/verify',
          { credentials: 'include' }
        );
      });
    });

    it('should not check auth for public routes', () => {
      (usePathname as jest.Mock).mockReturnValue('/');
      
      renderWithAuth(<TestComponent />);

      expect(global.fetch).not.toHaveBeenCalled();
    });
  });
});
