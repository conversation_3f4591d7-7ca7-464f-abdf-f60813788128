# REAL API Integration Tests for DADP Frontend

## Overview

These tests make **ACTUAL HTTP calls** to your FastAPI backend to verify that the frontend can properly communicate with all backend endpoints. This is **production-level testing** that validates real API integration.

## ⚠️ IMPORTANT PREREQUISITES

### 1. Backend Must Be Running
```bash
# Start your FastAPI backend first
cd fastapi-backend/app
python main.py
# Backend should be accessible at http://localhost:5000
```

### 2. Test Users Must Exist
Create these test users in your database:

```sql
-- Admin user
INSERT INTO users (username, email, full_name, hashed_password, role, is_active) 
VALUES ('test_admin', '<EMAIL>', 'Test Admin User', 'hashed_password_here', 'admin', true);

-- Annotator user  
INSERT INTO users (username, email, full_name, hashed_password, role, is_active)
VALUES ('test_annotator', '<EMAIL>', 'Test Annotator User', 'hashed_password_here', 'annotator', true);

-- Auditor user
INSERT INTO users (username, email, full_name, hashed_password, role, is_active) 
VALUES ('test_auditor', '<EMAIL>', 'Test Auditor User', 'hashed_password_here', 'auditor', true);
```

### 3. Environment Configuration
```bash
# Set environment variable for API URL
export NEXT_PUBLIC_API_URL=http://localhost:5000/api
```

## 🧪 Test Coverage

### Authentication Endpoints ✅
- `POST /auth/login` - User login with credentials
- `POST /auth/register` - New user registration  
- `GET /auth/verify` - Token verification
- `POST /auth/logout` - User logout
- Error handling for invalid credentials

### Admin Endpoints ✅
- `GET /admin/dashboard` - Admin dashboard data
- `GET /admin/users` - User management
- `GET /admin/nas-status` - NAS connection status
- `GET /admin/drive-status` - Google Drive status
- Role-based access control validation

### Annotator Endpoints ✅
- `GET /annotator/dashboard` - Annotator dashboard
- `GET /annotator/annotate` - Fetch annotation batches
- `POST /annotator/save-labels` - Save annotation labels
- `GET /annotator/verification` - Verification tasks
- `POST /annotator/submit-verification` - Submit verifications
- `GET /annotator/supervision` - Supervision tasks

### Auditor Endpoints ✅
- `GET /auditor/modes` - Available audit modes
- `GET /auditor/datasets` - Available datasets
- `POST /auditor/select-dataset` - Dataset selection
- `GET /auditor/audit-data` - Audit data retrieval
- `POST /auditor/submit-audit` - Submit audit results
- `GET /auditor/history` - Audit history
- `GET /auditor/statistics` - Audit statistics
- `POST /auditor/export-results` - Export audit results

### Specialized APIs ✅
- **Note-OCR**: PDF/Image upload and processing
- **Synthetic Dataset**: AI-powered dataset generation
- **Telegram**: Channel management and data fetching
- **Knowledge Base**: Entry management and operations

## 🚀 Running the Tests

### Run All API Integration Tests
```bash
npm test -- --testPathPattern=api-integration
```

### Run Specific Test Suites
```bash
# Authentication tests only
npm test -- src/__tests__/api-integration/real-api.test.ts

# Annotator tests only  
npm test -- src/__tests__/api-integration/annotator-api.test.ts

# Auditor tests only
npm test -- src/__tests__/api-integration/auditor-api.test.ts

# Specialized APIs tests only
npm test -- src/__tests__/api-integration/specialized-apis.test.ts
```

### Run with Verbose Output
```bash
npm test -- --testPathPattern=api-integration --verbose
```

## 📊 Expected Test Results

### Successful Test Run Example:
```
PASS src/__tests__/api-integration/real-api.test.ts
PASS src/__tests__/api-integration/annotator-api.test.ts  
PASS src/__tests__/api-integration/auditor-api.test.ts
PASS src/__tests__/api-integration/specialized-apis.test.ts

Test Suites: 4 passed, 4 total
Tests: 45 passed, 45 total
```

## 🔧 Troubleshooting

### Backend Not Running
```
Error: Backend not accessible - tests cannot run
```
**Solution**: Start your FastAPI backend on http://localhost:5000

### Authentication Failures
```
Error: Login failed - 401 Unauthorized
```
**Solution**: Verify test users exist with correct credentials

### Database Connection Issues
```
Error: Database connection failed
```
**Solution**: Ensure your database is running and accessible

### CORS Issues
```
Error: CORS policy blocked the request
```
**Solution**: Configure CORS in your FastAPI backend to allow localhost:3000

## 📈 Test Metrics

These tests validate:
- **45+ API endpoints** across all user roles
- **Authentication flows** and security
- **Error handling** and edge cases  
- **Data validation** and response formats
- **Role-based access control**
- **File upload/download** functionality
- **Real-time data processing**

## 🎯 Production Readiness

These tests ensure your frontend can:
✅ Successfully authenticate users  
✅ Handle all user roles and permissions
✅ Process annotation workflows end-to-end
✅ Manage audit and quality control processes  
✅ Integrate with specialized services (OCR, AI, Telegram)
✅ Handle errors gracefully
✅ Maintain data integrity across operations

This is **real production-level API testing** that validates your entire frontend-backend integration.
