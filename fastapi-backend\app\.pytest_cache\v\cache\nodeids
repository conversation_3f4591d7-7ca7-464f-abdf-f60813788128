["tests2/contract/test_contract.py::test_auth_login_contract", "tests2/contract/test_contract.py::test_auth_register_contract", "tests2/contract/test_contract.py::test_contract[DELETE /api/NoteOCR/documents/{document_id}]", "tests2/contract/test_contract.py::test_contract[DELETE /api/knowledge-base/entries/{entry_id}]", "tests2/contract/test_contract.py::test_contract[GET /]", "tests2/contract/test_contract.py::test_contract[GET /admin/google-drive-callback]", "tests2/contract/test_contract.py::test_contract[GET /api/NoteOCR/documents/]", "tests2/contract/test_contract.py::test_contract[GET /api/NoteOCR/documents/{document_id}]", "tests2/contract/test_contract.py::test_contract[GET /api/NoteOCR/images/extractor-mode]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/browse-nas-directory]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/browser/{folder}]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/check-google-drive-connection]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/check-nas-connection]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/dashboard]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/data-delivery]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/edit-instructions]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/get-datasets]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/image/{image_path}]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/ocr-directory]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/users/{username}]", "tests2/contract/test_contract.py::test_contract[GET /api/admin/users]", "tests2/contract/test_contract.py::test_contract[GET /api/annotator/annotate]", "tests2/contract/test_contract.py::test_contract[GET /api/annotator/dashboard]", "tests2/contract/test_contract.py::test_contract[GET /api/annotator/image/{image_path}]", "tests2/contract/test_contract.py::test_contract[GET /api/annotator/next-set]", "tests2/contract/test_contract.py::test_contract[GET /api/auditor/datasets]", "tests2/contract/test_contract.py::test_contract[GET /api/auditor/files]", "tests2/contract/test_contract.py::test_contract[GET /api/auditor/history]", "tests2/contract/test_contract.py::test_contract[GET /api/auditor/image]", "tests2/contract/test_contract.py::test_contract[GET /api/auditor/modes]", "tests2/contract/test_contract.py::test_contract[GET /api/auditor/tasks]", "tests2/contract/test_contract.py::test_contract[GET /api/auditor/verifiers]", "tests2/contract/test_contract.py::test_contract[GET /api/auth/me]", "tests2/contract/test_contract.py::test_contract[GET /api/auth/verify]", "tests2/contract/test_contract.py::test_contract[GET /api/client/datasets/{dataset_id}]", "tests2/contract/test_contract.py::test_contract[GET /api/client/datasets]", "tests2/contract/test_contract.py::test_contract[GET /api/knowledge-base/entries/{entry_id}]", "tests2/contract/test_contract.py::test_contract[GET /api/knowledge-base/entries]", "tests2/contract/test_contract.py::test_contract[GET /api/knowledge-base/topics]", "tests2/contract/test_contract.py::test_contract[GET /api/supervision/check-status/{image_id}]", "tests2/contract/test_contract.py::test_contract[GET /api/supervision/document-image/{image_id}]", "tests2/contract/test_contract.py::test_contract[GET /api/supervision/download_csv/{image_id}]", "tests2/contract/test_contract.py::test_contract[GET /api/supervision/download_txt/{image_id}]", "tests2/contract/test_contract.py::test_contract[GET /api/supervision/review]", "tests2/contract/test_contract.py::test_contract[GET /api/synthetic-dataset/dataset-types]", "tests2/contract/test_contract.py::test_contract[GET /api/synthetic-dataset/knowledge-entries]", "tests2/contract/test_contract.py::test_contract[GET /api/synthetic-dataset/models]", "tests2/contract/test_contract.py::test_contract[GET /api/telegram/analytics]", "tests2/contract/test_contract.py::test_contract[GET /api/telegram/channels]", "tests2/contract/test_contract.py::test_contract[GET /api/telegram/check-auth]", "tests2/contract/test_contract.py::test_contract[GET /api/telegram/dates]", "tests2/contract/test_contract.py::test_contract[GET /api/telegram/export-analytics-csv]", "tests2/contract/test_contract.py::test_contract[GET /api/telegram/images]", "tests2/contract/test_contract.py::test_contract[POST /api/NoteOCR/documents/]", "tests2/contract/test_contract.py::test_contract[POST /api/NoteOCR/images/chat]", "tests2/contract/test_contract.py::test_contract[POST /api/NoteOCR/images/extract-ocr]", "tests2/contract/test_contract.py::test_contract[POST /api/NoteOCR/images/upload]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/add-user]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/configure-google-drive]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/connect-nas]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/disconnect-nas]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/edit-instructions]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/flush-db]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/merge-dataset-json]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/reset-google-drive]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/select-annotation-folder]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/select-dataset]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/select-verification-folders]", "tests2/contract/test_contract.py::test_contract[POST /api/admin/users/{username}/suspend]", "tests2/contract/test_contract.py::test_contract[POST /api/annotator/save-labels]", "tests2/contract/test_contract.py::test_contract[POST /api/auditor/audit-record]", "tests2/contract/test_contract.py::test_contract[POST /api/auditor/save-labels]", "tests2/contract/test_contract.py::test_contract[POST /api/auth/change-password]", "tests2/contract/test_contract.py::test_contract[POST /api/auth/login]", "tests2/contract/test_contract.py::test_contract[POST /api/auth/logout]", "tests2/contract/test_contract.py::test_contract[POST /api/auth/refresh-token]", "tests2/contract/test_contract.py::test_contract[POST /api/auth/refresh]", "tests2/contract/test_contract.py::test_contract[POST /api/auth/register]", "tests2/contract/test_contract.py::test_contract[POST /api/knowledge-base/entries]", "tests2/contract/test_contract.py::test_contract[POST /api/supervision/download_csv]", "tests2/contract/test_contract.py::test_contract[POST /api/supervision/list-drive-folders]", "tests2/contract/test_contract.py::test_contract[POST /api/supervision/save-document]", "tests2/contract/test_contract.py::test_contract[POST /api/supervision/upload]", "tests2/contract/test_contract.py::test_contract[POST /api/synthetic-dataset/generate-nonref]", "tests2/contract/test_contract.py::test_contract[POST /api/synthetic-dataset/generate]", "tests2/contract/test_contract.py::test_contract[POST /api/telegram/connect]", "tests2/contract/test_contract.py::test_contract[POST /api/telegram/disconnect]", "tests2/contract/test_contract.py::test_contract[POST /api/telegram/download-image]", "tests2/contract/test_contract.py::test_contract[POST /api/telegram/download-multiple]", "tests2/contract/test_contract.py::test_contract[POST /api/telegram/upload-to-drive]", "tests2/contract/test_contract.py::test_contract[POST /api/telegram/verify-code]", "tests2/contract/test_contract.py::test_contract[POST /api/telegram/verify-password]", "tests2/contract/test_contract.py::test_contract[PUT /api/admin/users/{username}]", "tests2/contract/test_contract.py::test_contract[PUT /api/knowledge-base/entries/{entry_id}]", "tests2/contract/test_openapi.py::test_api_contract[DELETE /api/NoteOCR/documents/{document_id}]", "tests2/contract/test_openapi.py::test_api_contract[DELETE /api/knowledge-base/entries/{entry_id}]", "tests2/contract/test_openapi.py::test_api_contract[GET /]", "tests2/contract/test_openapi.py::test_api_contract[GET /admin/google-drive-callback]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/NoteOCR/documents/]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/NoteOCR/documents/{document_id}]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/NoteOCR/images/extractor-mode]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/browse-nas-directory]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/browser/{folder}]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/check-google-drive-connection]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/check-nas-connection]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/dashboard]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/data-delivery]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/edit-instructions]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/get-datasets]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/image/{image_path}]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/ocr-directory]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/users/{username}]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/admin/users]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/annotator/annotate]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/annotator/dashboard]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/annotator/image/{image_path}]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/annotator/next-set]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/datasets]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/files]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/history]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/image]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/modes]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/tasks]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/auditor/verifiers]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/auth/me]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/auth/verify]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/client/datasets/{dataset_id}]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/client/datasets]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/knowledge-base/entries/{entry_id}]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/knowledge-base/entries]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/knowledge-base/topics]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/supervision/check-status/{image_id}]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/supervision/document-image/{image_id}]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/supervision/download_csv/{image_id}]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/supervision/download_txt/{image_id}]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/supervision/review]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/synthetic-dataset/dataset-types]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/synthetic-dataset/knowledge-entries]", "tests2/contract/test_openapi.py::test_api_contract[GET /api/synthetic-dataset/models]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/NoteOCR/documents/]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/NoteOCR/images/chat]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/NoteOCR/images/extract-ocr]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/NoteOCR/images/upload]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/add-user]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/configure-google-drive]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/connect-nas]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/disconnect-nas]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/edit-instructions]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/flush-db]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/merge-dataset-json]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/reset-google-drive]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/select-annotation-folder]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/select-dataset]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/select-verification-folders]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/admin/users/{username}/suspend]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/annotator/save-labels]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/auditor/audit-record]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/auditor/save-labels]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/auth/change-password]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/auth/login]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/auth/logout]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/auth/refresh-token]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/auth/refresh]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/auth/register]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/knowledge-base/entries]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/supervision/download_csv]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/supervision/list-drive-folders]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/supervision/save-document]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/supervision/upload]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/synthetic-dataset/generate-nonref]", "tests2/contract/test_openapi.py::test_api_contract[POST /api/synthetic-dataset/generate]", "tests2/contract/test_openapi.py::test_api_contract[PUT /api/admin/users/{username}]", "tests2/contract/test_openapi.py::test_api_contract[PUT /api/knowledge-base/entries/{entry_id}]", "tests2/integration/test_admin.py::test_add_user", "tests2/integration/test_admin.py::test_add_user_field_boundaries", "tests2/integration/test_admin.py::test_admin_endpoint_forbidden_for_annotator", "tests2/integration/test_admin.py::test_admin_endpoint_unauthenticated", "tests2/integration/test_admin.py::test_admin_image_proxy", "tests2/integration/test_admin.py::test_admin_rbac_for_all_protected_endpoints", "tests2/integration/test_admin.py::test_admin_users_list_forbidden_for_annotator", "tests2/integration/test_admin.py::test_browse_nas_directory", "tests2/integration/test_admin.py::test_browser_folder", "tests2/integration/test_admin.py::test_browser_pagination_params", "tests2/integration/test_admin.py::test_check_google_drive_connection", "tests2/integration/test_admin.py::test_check_nas_connection", "tests2/integration/test_admin.py::test_configure_google_drive", "tests2/integration/test_admin.py::test_connect_nas", "tests2/integration/test_admin.py::test_dashboard", "tests2/integration/test_admin.py::test_data_delivery", "tests2/integration/test_admin.py::test_disconnect_nas", "tests2/integration/test_admin.py::test_edit_instructions", "tests2/integration/test_admin.py::test_flush_db_success_and_error", "tests2/integration/test_admin.py::test_get_and_missing_user_by_username", "tests2/integration/test_admin.py::test_get_datasets", "tests2/integration/test_admin.py::test_google_drive_callback", "tests2/integration/test_admin.py::test_merge_dataset_json", "tests2/integration/test_admin.py::test_ocr_directory", "tests2/integration/test_admin.py::test_post_edit_instructions", "tests2/integration/test_admin.py::test_reset_google_drive", "tests2/integration/test_admin.py::test_select_annotation_folder", "tests2/integration/test_admin.py::test_select_dataset", "tests2/integration/test_admin.py::test_select_verification_folders", "tests2/integration/test_admin.py::test_suspend_and_reactivate_user", "tests2/integration/test_admin.py::test_update_user_valid_and_invalid", "tests2/integration/test_admin.py::test_users_list", "tests2/integration/test_aggregations.py::test_knowledge_base_topic_counts", "tests2/integration/test_aggregations.py::test_pdf_page_image_join_counts", "tests2/integration/test_annotator.py::test_annotate", "tests2/integration/test_annotator.py::test_annotate_no_tasks", "tests2/integration/test_annotator.py::test_batch_size_boundaries", "tests2/integration/test_annotator.py::test_dashboard", "tests2/integration/test_annotator.py::test_image_cache_fallback", "tests2/integration/test_annotator.py::test_invalid_image_path_traversal", "tests2/integration/test_annotator.py::test_label_length_boundaries", "tests2/integration/test_annotator.py::test_mode_switching_annotation_mode", "tests2/integration/test_annotator.py::test_next_set", "tests2/integration/test_annotator.py::test_save_labels", "tests2/integration/test_annotator.py::test_unauthorized_annotator_endpoints", "tests2/integration/test_auditor.py::test_audit_record_endpoint", "tests2/integration/test_auditor.py::test_auditor_rbac_for_all_endpoints", "tests2/integration/test_auditor.py::test_datasets", "tests2/integration/test_auditor.py::test_history", "tests2/integration/test_auditor.py::test_image_proxy_success_and_not_found", "tests2/integration/test_auditor.py::test_list_files_and_tasks_empty", "tests2/integration/test_auditor.py::test_modes", "tests2/integration/test_auditor.py::test_save_labels_db_and_boundaries", "tests2/integration/test_auditor.py::test_unauthorized_auditor_endpoints", "tests2/integration/test_auditor.py::test_verifiers", "tests2/integration/test_auth.py::test_change_password", "tests2/integration/test_auth.py::test_change_password_wrong_and_unauthenticated", "tests2/integration/test_auth.py::test_login_wrong_password", "tests2/integration/test_auth.py::test_logout", "tests2/integration/test_auth.py::test_logout_idempotent", "tests2/integration/test_auth.py::test_me_deactivated_and_deleted", "tests2/integration/test_auth.py::test_password_length_boundaries", "tests2/integration/test_auth.py::test_register_login_me", "tests2/integration/test_auth.py::test_register_validation", "tests2/integration/test_auth.py::test_username_length_boundaries", "tests2/integration/test_auth.py::test_verify_token_missing_and_expired", "tests2/integration/test_backup_and_migrations.py::test_alembic_migrations", "tests2/integration/test_backup_and_migrations.py::test_data_dump_and_restore", "tests2/integration/test_backup_and_migrations.py::test_differential_backup_restore", "tests2/integration/test_backup_and_migrations.py::test_incremental_backup_restore", "tests2/integration/test_backup_and_migrations.py::test_migrations_with_data", "tests2/integration/test_backup_and_migrations.py::test_schema_backup_and_restore", "tests2/integration/test_backup_restore.py::test_data_dump_and_restore", "tests2/integration/test_backup_restore.py::test_schema_backup_and_restore", "tests2/integration/test_bulk_filter_pagination.py::test_bulk_insert_and_filter_pagination", "tests2/integration/test_client.py::test_client_rbac_for_all_endpoints", "tests2/integration/test_client.py::test_dataset_details", "tests2/integration/test_client.py::test_list_datasets", "tests2/integration/test_concurrency.py::test_concurrent_annotation_saving", "tests2/integration/test_concurrency.py::test_concurrent_batch_fetch", "tests2/integration/test_concurrency.py::test_concurrent_database_inserts_with_unique_constraints", "tests2/integration/test_concurrency.py::test_concurrent_duplicate_user_registration", "tests2/integration/test_concurrency.py::test_concurrent_file_upload_simulation", "tests2/integration/test_concurrency.py::test_concurrent_inserts", "tests2/integration/test_concurrency.py::test_concurrent_read_modify_write_cycles", "tests2/integration/test_concurrency.py::test_concurrent_save_labels", "tests2/integration/test_concurrency.py::test_concurrent_updates_do_not_corrupt_data", "tests2/integration/test_concurrency.py::test_concurrent_user_registration", "tests2/integration/test_concurrency.py::test_connection_pool_stress", "tests2/integration/test_concurrency.py::test_transaction_isolation", "tests2/integration/test_concurrency.py::test_transaction_timeout_scenarios", "tests2/integration/test_crud_and_pagination.py::test_bulk_insert_and_filter_pagination", "tests2/integration/test_crud_and_pagination.py::test_partial_update_kb", "tests2/integration/test_db_behavior.py::test_commit_rollback", "tests2/integration/test_db_behavior.py::test_foreign_key_and_cascade_behavior", "tests2/integration/test_db_behavior.py::test_invalid_db_url", "tests2/integration/test_db_behavior.py::test_not_null_constraints", "tests2/integration/test_db_behavior.py::test_unique_constraint[/api/auth/register-payload0]", "tests2/integration/test_db_behavior.py::test_user_unique_constraints_direct", "tests2/integration/test_db_security.py::test_password_hashing_at_rest", "tests2/integration/test_edge_cases.py::test_corrupted_data_handling", "tests2/integration/test_edge_cases.py::test_extreme_numeric_values", "tests2/integration/test_edge_cases.py::test_malformed_json_text_blob", "tests2/integration/test_edge_cases.py::test_overlength_strings", "tests2/integration/test_endpoint_performance.py::test_endpoint_response_time[GET-/api/auth/me-0.5]", "tests2/integration/test_endpoint_performance.py::test_endpoint_response_time[GET-/openapi.json-0.5]", "tests2/integration/test_error_handling.py::test_concurrent_session_conflict", "tests2/integration/test_error_handling.py::test_corrupted_data_handling", "tests2/integration/test_error_handling.py::test_db_downtime", "tests2/integration/test_error_handling.py::test_duplicate_key_violations", "tests2/integration/test_error_handling.py::test_extreme_numeric_values", "tests2/integration/test_error_handling.py::test_file_upload_failure", "tests2/integration/test_error_handling.py::test_foreign_key_constraint_violations", "tests2/integration/test_error_handling.py::test_ftp_failure", "tests2/integration/test_error_handling.py::test_gemini_api_failure", "tests2/integration/test_error_handling.py::test_google_drive_failure", "tests2/integration/test_error_handling.py::test_image_processing_failure", "tests2/integration/test_error_handling.py::test_invalid_authorization_format", "tests2/integration/test_error_handling.py::test_invalid_content_type", "tests2/integration/test_error_handling.py::test_large_request_payload", "tests2/integration/test_error_handling.py::test_malformed_json", "tests2/integration/test_error_handling.py::test_malformed_json_text_blob", "tests2/integration/test_error_handling.py::test_missing_gemini_api_key", "tests2/integration/test_error_handling.py::test_missing_required_headers", "tests2/integration/test_error_handling.py::test_null_constraint_violations", "tests2/integration/test_error_handling.py::test_ocr_service_failure", "tests2/integration/test_error_handling.py::test_overlength_strings", "tests2/integration/test_error_handling.py::test_pdf_processing_failure", "tests2/integration/test_error_handling.py::test_redis_cache_failure", "tests2/integration/test_error_handling.py::test_redis_cache_set_failure", "tests2/integration/test_error_handling.py::test_request_timeout", "tests2/integration/test_error_handling.py::test_telegram_failure", "tests2/integration/test_error_handling.py::test_weak_password", "tests2/integration/test_expired_token_flows.py::test_cookie_refresh_with_expired_refresh_token", "tests2/integration/test_expired_token_flows.py::test_expired_access_token_rejected", "tests2/integration/test_expired_token_flows.py::test_expired_refresh_token_rejected", "tests2/integration/test_explain_plans.py::test_explain_plan_knowledge_entry_index", "tests2/integration/test_explain_plans.py::test_explain_plan_user_email_index", "tests2/integration/test_external_failures.py::test_ftp_failure", "tests2/integration/test_external_failures.py::test_google_drive_failure", "tests2/integration/test_external_failures.py::test_telegram_failure", "tests2/integration/test_file_uploads.py::test_download_document_not_found", "tests2/integration/test_file_uploads.py::test_download_image_proxy", "tests2/integration/test_file_uploads.py::test_large_batch_download", "tests2/integration/test_file_uploads.py::test_stream_csv_not_found", "tests2/integration/test_file_uploads.py::test_stream_txt_not_found", "tests2/integration/test_file_uploads.py::test_upload_large_file", "tests2/integration/test_file_uploads.py::test_upload_pdf_invalid", "tests2/integration/test_file_uploads.py::test_upload_pdf_valid", "tests2/integration/test_isolation_levels.py::test_read_uncommitted_pragma", "tests2/integration/test_knowledge_base.py::test_bulk_insert_and_filter_pagination", "tests2/integration/test_knowledge_base.py::test_create_entry", "tests2/integration/test_knowledge_base.py::test_delete_nonexistent_entry", "tests2/integration/test_knowledge_base.py::test_entries_combined_filters", "tests2/integration/test_knowledge_base.py::test_entries_query_params_invalid", "tests2/integration/test_knowledge_base.py::test_knowledge_base_rbac_for_all_endpoints", "tests2/integration/test_knowledge_base.py::test_list_entries", "tests2/integration/test_knowledge_base.py::test_partial_update_kb_entry", "tests2/integration/test_knowledge_base.py::test_title_content_length_boundaries", "tests2/integration/test_knowledge_base.py::test_topics", "tests2/integration/test_knowledge_base.py::test_update_nonexistent_entry", "tests2/integration/test_load_ci.py::test_locust_headless", "tests2/integration/test_logging.py::test_db_error_logging", "tests2/integration/test_logging.py::test_query_metrics_logging", "tests2/integration/test_migrations.py::test_alembic_migrations", "tests2/integration/test_migrations.py::test_migrations_with_data", "tests2/integration/test_monitoring.py::test_db_error_logging", "tests2/integration/test_monitoring.py::test_query_metrics_logging", "tests2/integration/test_naming_conventions.py::test_table_and_column_naming_conventions", "tests2/integration/test_noteocr.py::test_chat_requires_fields", "tests2/integration/test_noteocr.py::test_extract_ocr_requires_body", "tests2/integration/test_noteocr.py::test_extractor_mode", "tests2/integration/test_noteocr.py::test_image_upload_extract_ocr_chat", "tests2/integration/test_noteocr.py::test_list_documents", "tests2/integration/test_noteocr.py::test_not_found_document", "tests2/integration/test_noteocr.py::test_pdf_upload_get_delete", "tests2/integration/test_noteocr.py::test_upload_large_file", "tests2/integration/test_partial_download.py::test_partial_download_csv_integrity", "tests2/integration/test_patch_update_kb.py::test_partial_update_kb", "tests2/integration/test_performance.py::test_endpoint_response_time[GET-/api/auth/me-0.5]", "tests2/integration/test_performance.py::test_endpoint_response_time[GET-/openapi.json-0.5]", "tests2/integration/test_performance.py::test_explain_plan_knowledge_entry_index", "tests2/integration/test_performance.py::test_explain_plan_user_email_index", "tests2/integration/test_performance.py::test_index_performance", "tests2/integration/test_performance.py::test_locust_headless", "tests2/integration/test_performance.py::test_simple_query_performance", "tests2/integration/test_schema.py::test_check_constraints_naming", "tests2/integration/test_schema.py::test_knowledge_base_schema", "tests2/integration/test_schema.py::test_pdf_extractor_schema", "tests2/integration/test_schema.py::test_user_defaults_and_indexes", "tests2/integration/test_schema.py::test_user_table_schema", "tests2/integration/test_schema_and_behavior.py::test_check_constraints_naming", "tests2/integration/test_schema_and_behavior.py::test_commit_rollback", "tests2/integration/test_schema_and_behavior.py::test_foreign_key_and_cascade_behavior", "tests2/integration/test_schema_and_behavior.py::test_invalid_db_url", "tests2/integration/test_schema_and_behavior.py::test_knowledge_base_schema", "tests2/integration/test_schema_and_behavior.py::test_not_null_constraints", "tests2/integration/test_schema_and_behavior.py::test_pdf_extractor_schema", "tests2/integration/test_schema_and_behavior.py::test_unique_constraint[/api/auth/register-payload0]", "tests2/integration/test_schema_and_behavior.py::test_user_defaults_and_indexes", "tests2/integration/test_schema_and_behavior.py::test_user_table_schema", "tests2/integration/test_schema_and_behavior.py::test_user_unique_constraints_direct", "tests2/integration/test_security.py::test_admin_endpoint_access_control", "tests2/integration/test_security.py::test_annotator_endpoint_role_control", "tests2/integration/test_security.py::test_api_sql_injection[/api/annotator/save-labels-batch_name]", "tests2/integration/test_security.py::test_api_sql_injection[/api/auth/register-username]", "tests2/integration/test_security.py::test_api_sql_injection[/api/knowledge-base/entries-title]", "tests2/integration/test_security.py::test_auditor_endpoint_role_control", "tests2/integration/test_security.py::test_change_password_flow", "tests2/integration/test_security.py::test_change_password_schema_validation_mismatch", "tests2/integration/test_security.py::test_change_password_wrong_current", "tests2/integration/test_security.py::test_cookie_refresh_endpoint", "tests2/integration/test_security.py::test_cookie_refresh_flow", "tests2/integration/test_security.py::test_cookie_refresh_sets_new_cookies", "tests2/integration/test_security.py::test_cookie_security_attributes", "tests2/integration/test_security.py::test_cors_preflight_all_endpoints", "tests2/integration/test_security.py::test_cors_preflight_auth_endpoints[/api/auth/change-password]", "tests2/integration/test_security.py::test_cors_preflight_auth_endpoints[/api/auth/login]", "tests2/integration/test_security.py::test_cors_preflight_auth_endpoints[/api/auth/logout]", "tests2/integration/test_security.py::test_cors_preflight_auth_endpoints[/api/auth/refresh-token]", "tests2/integration/test_security.py::test_cors_preflight_auth_endpoints[/api/auth/refresh]", "tests2/integration/test_security.py::test_cors_preflight_auth_endpoints[/api/auth/register]", "tests2/integration/test_security.py::test_cors_preflight_auth_endpoints[/api/auth/verify]", "tests2/integration/test_security.py::test_cors_preflight_options", "tests2/integration/test_security.py::test_db_user_permissions", "tests2/integration/test_security.py::test_duplicate_registration", "tests2/integration/test_security.py::test_encryption_at_rest", "tests2/integration/test_security.py::test_login_failure_invalid_credentials", "tests2/integration/test_security.py::test_login_success", "tests2/integration/test_security.py::test_logout_clears_cookies", "tests2/integration/test_security.py::test_malformed_bearer_header", "tests2/integration/test_security.py::test_method_not_allowed_for_register_and_login", "tests2/integration/test_security.py::test_password_hashing_at_rest", "tests2/integration/test_security.py::test_protected_endpoint_with_valid_token", "tests2/integration/test_security.py::test_protected_endpoints_require_auth", "tests2/integration/test_security.py::test_refresh_token_endpoint", "tests2/integration/test_security.py::test_refresh_token_wrong_type", "tests2/integration/test_security.py::test_register_schema_validation_password_mismatch", "tests2/integration/test_security.py::test_sql_injection_orm", "tests2/integration/test_security.py::test_sql_injection_raw", "tests2/integration/test_security.py::test_tampered_jwt_rejected", "tests2/integration/test_security.py::test_tls_enforcement", "tests2/integration/test_security.py::test_verify_token_happy_path", "tests2/integration/test_security_injection.py::test_sql_injection[/api/annotator/save-labels-batch_name]", "tests2/integration/test_security_injection.py::test_sql_injection[/api/auth/register-username]", "tests2/integration/test_security_injection.py::test_sql_injection[/api/knowledge-base/entries-title]", "tests2/integration/test_streaming.py::test_large_batch_download", "tests2/integration/test_streaming.py::test_stream_csv_not_found", "tests2/integration/test_streaming.py::test_stream_txt_not_found", "tests2/integration/test_supervision.py::test_assign_batch_to_annotator", "tests2/integration/test_supervision.py::test_batch_quality_metrics", "tests2/integration/test_supervision.py::test_bulk_export_csv_headers", "tests2/integration/test_supervision.py::test_bulk_export_download", "tests2/integration/test_supervision.py::test_cancel_long_running_task", "tests2/integration/test_supervision.py::test_check_status_and_document_image", "tests2/integration/test_supervision.py::test_create_batch_from_folder", "tests2/integration/test_supervision.py::test_document_type_boundaries", "tests2/integration/test_supervision.py::test_download_batch_results", "tests2/integration/test_supervision.py::test_download_by_id_success[download_csv-data-text/csv-attachment]", "tests2/integration/test_supervision.py::test_download_by_id_success[download_csv-data0-text/csv-attachment]", "tests2/integration/test_supervision.py::test_download_by_id_success[download_txt-line1\\nline2-text/plain-attachment]", "tests2/integration/test_supervision.py::test_download_by_id_success[download_txt-txt_content-text/plain-attachment]", "tests2/integration/test_supervision.py::test_download_csv_midrange_streaming", "tests2/integration/test_supervision.py::test_get_annotator_performance", "tests2/integration/test_supervision.py::test_get_batch_progress", "tests2/integration/test_supervision.py::test_list_drive_folders", "tests2/integration/test_supervision.py::test_list_drive_folders_invalid_folder_type", "tests2/integration/test_supervision.py::test_list_drive_folders_success", "tests2/integration/test_supervision.py::test_list_supervision_batches", "tests2/integration/test_supervision.py::test_long_running_task_status", "tests2/integration/test_supervision.py::test_partial_download_csv_integrity", "tests2/integration/test_supervision.py::test_review_response_shape", "tests2/integration/test_supervision.py::test_save_document", "tests2/integration/test_supervision.py::test_supervision_analytics", "tests2/integration/test_supervision.py::test_supervision_export_reports", "tests2/integration/test_supervision.py::test_supervision_rbac_for_all_endpoints", "tests2/integration/test_supervision.py::test_supervision_real_time_updates", "tests2/integration/test_supervision.py::test_supervision_unauthorized_access", "tests2/integration/test_supervision.py::test_unauthorized_supervision_endpoints", "tests2/integration/test_supervision.py::test_upload_review", "tests2/integration/test_synthetic_dataset.py::test_dataset_types", "tests2/integration/test_synthetic_dataset.py::test_generate_dataset_boundaries", "tests2/integration/test_synthetic_dataset.py::test_generate_nonref_forbidden_for_non_admin", "tests2/integration/test_synthetic_dataset.py::test_generate_nonref_invalid_num_samples", "tests2/integration/test_synthetic_dataset.py::test_generate_nonref_missing_fields", "tests2/integration/test_synthetic_dataset.py::test_generate_nonref_no_api_key_returns_error", "tests2/integration/test_synthetic_dataset.py::test_generate_nonref_requires_auth", "tests2/integration/test_synthetic_dataset.py::test_knowledge_entries_filtering", "tests2/integration/test_synthetic_dataset.py::test_models", "tests2/integration/test_telegram.py::test_analytics_success", "tests2/integration/test_telegram.py::test_analytics_unauth", "tests2/integration/test_telegram.py::test_channels_missing_session", "tests2/integration/test_telegram.py::test_channels_success", "tests2/integration/test_telegram.py::test_channels_unauth", "tests2/integration/test_telegram.py::test_check_auth_missing_session", "tests2/integration/test_telegram.py::test_check_auth_success", "tests2/integration/test_telegram.py::test_check_auth_unauth", "tests2/integration/test_telegram.py::test_connect_invalid", "tests2/integration/test_telegram.py::test_connect_missing_params", "tests2/integration/test_telegram.py::test_connect_success", "tests2/integration/test_telegram.py::test_dates_missing_params", "tests2/integration/test_telegram.py::test_disconnect_success", "tests2/integration/test_telegram.py::test_disconnect_unauth", "tests2/integration/test_telegram.py::test_download_image_missing_fields", "tests2/integration/test_telegram.py::test_download_image_success", "tests2/integration/test_telegram.py::test_download_image_unauth", "tests2/integration/test_telegram.py::test_download_multiple_missing_fields", "tests2/integration/test_telegram.py::test_download_multiple_success", "tests2/integration/test_telegram.py::test_download_multiple_unauth", "tests2/integration/test_telegram.py::test_export_analytics_csv_success", "tests2/integration/test_telegram.py::test_export_analytics_csv_unauth", "tests2/integration/test_telegram.py::test_get_dates_success", "tests2/integration/test_telegram.py::test_get_dates_unauth", "tests2/integration/test_telegram.py::test_get_images_success", "tests2/integration/test_telegram.py::test_get_images_unauth", "tests2/integration/test_telegram.py::test_images_missing_params", "tests2/integration/test_telegram.py::test_unauthorized_telegram_endpoints", "tests2/integration/test_telegram.py::test_upload_to_drive_success", "tests2/integration/test_telegram.py::test_upload_to_drive_unauth", "tests2/integration/test_telegram.py::test_verify_code_missing_params", "tests2/integration/test_telegram.py::test_verify_code_success", "tests2/integration/test_telegram.py::test_verify_code_unauth", "tests2/integration/test_telegram.py::test_verify_password_missing_params", "tests2/integration/test_telegram.py::test_verify_password_success", "tests2/integration/test_telegram.py::test_verify_password_unauth", "tests2/integration/test_transactions.py::test_concurrent_batch_fetch", "tests2/integration/test_transactions.py::test_concurrent_inserts", "tests2/integration/test_transactions.py::test_concurrent_save_labels", "tests2/integration/test_transactions.py::test_dirty_reads", "tests2/integration/test_transactions.py::test_non_repeatable_reads", "tests2/integration/test_transactions.py::test_phantom_reads", "tests2/integration/test_transactions.py::test_read_uncommitted_pragma", "tests2/integration/test_transactions.py::test_transaction_isolation", "tests2/security/test_authz.py::test_admin_403_real_token", "tests2/security/test_authz.py::test_admin_403_with_non_admin_roles", "tests2/security/test_authz.py::test_annotator_403_with_non_annotator_roles", "tests2/security/test_authz.py::test_auditor_403_with_non_auditor_roles", "tests2/security/test_authz.py::test_client_403_with_non_client_roles", "tests2/security/test_authz.py::test_expired_malformed_jwt", "tests2/security/test_authz.py::test_inactive_user_access", "tests2/security/test_authz.py::test_logout_clears_cookies", "tests2/security/test_authz.py::test_malformed_bearer_header", "tests2/security/test_authz.py::test_protected_401", "tests2/security/test_authz.py::test_role_escalation_annotator_to_admin", "tests2/security/test_authz.py::test_role_escalation_client_to_annotator", "tests2/test_smoke.py::test_smoke_all_routes[/-methods78]", "tests2/test_smoke.py::test_smoke_all_routes[/admin/google-drive-callback-methods4]", "tests2/test_smoke.py::test_smoke_all_routes[/api/NoteOCR/documents/-methods51]", "tests2/test_smoke.py::test_smoke_all_routes[/api/NoteOCR/documents/-methods52]", "tests2/test_smoke.py::test_smoke_all_routes[/api/NoteOCR/documents/{document_id}-methods53]", "tests2/test_smoke.py::test_smoke_all_routes[/api/NoteOCR/images/chat-methods57]", "tests2/test_smoke.py::test_smoke_all_routes[/api/NoteOCR/images/extract-ocr-methods56]", "tests2/test_smoke.py::test_smoke_all_routes[/api/NoteOCR/images/extractor-mode-methods54]", "tests2/test_smoke.py::test_smoke_all_routes[/api/NoteOCR/images/upload-methods55]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/add-user-methods33]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/browse-nas-directory-methods37]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/browser/{folder:path}-methods42]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/check-google-drive-connection-methods27]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/check-nas-connection-methods25]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/configure-google-drive-methods26]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/connect-nas-methods23]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/dashboard-methods22]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/data-delivery-methods43]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/disconnect-nas-methods24]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/edit-instructions-methods34]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/edit-instructions-methods35]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/flush-db-methods29]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/get-datasets-methods41]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/image/{image_path:path}-methods45]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/merge-dataset-json-methods44]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/ocr-directory-methods36]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/reset-google-drive-methods28]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/select-annotation-folder-methods38]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/select-dataset-methods40]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/select-verification-folders-methods39]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/users-methods30]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/users/{username}-methods31]", "tests2/test_smoke.py::test_smoke_all_routes[/api/admin/users/{username}/suspend-methods32]", "tests2/test_smoke.py::test_smoke_all_routes[/api/annotator/annotate-methods47]", "tests2/test_smoke.py::test_smoke_all_routes[/api/annotator/dashboard-methods46]", "tests2/test_smoke.py::test_smoke_all_routes[/api/annotator/image/{image_path:path}-methods49]", "tests2/test_smoke.py::test_smoke_all_routes[/api/annotator/next-set-methods48]", "tests2/test_smoke.py::test_smoke_all_routes[/api/annotator/save-labels-methods50]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auditor/audit-record-methods20]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auditor/datasets-methods14]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auditor/files-methods16]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auditor/history-methods19]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auditor/image-methods18]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auditor/modes-methods13]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auditor/save-labels-methods21]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auditor/tasks-methods17]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auditor/verifiers-methods15]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auth/change-password-methods11]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auth/login-methods7]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auth/logout-methods8]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auth/me-methods12]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auth/refresh-methods9]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auth/refresh-token-methods5]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auth/register-methods6]", "tests2/test_smoke.py::test_smoke_all_routes[/api/auth/verify-methods10]", "tests2/test_smoke.py::test_smoke_all_routes[/api/client/datasets-methods67]", "tests2/test_smoke.py::test_smoke_all_routes[/api/client/datasets/{dataset_id}-methods68]", "tests2/test_smoke.py::test_smoke_all_routes[/api/knowledge-base/entries-methods74]", "tests2/test_smoke.py::test_smoke_all_routes[/api/knowledge-base/entries-methods77]", "tests2/test_smoke.py::test_smoke_all_routes[/api/knowledge-base/entries/{entry_id}-methods76]", "tests2/test_smoke.py::test_smoke_all_routes[/api/knowledge-base/topics-methods75]", "tests2/test_smoke.py::test_smoke_all_routes[/api/supervision/check-status/{image_id}-methods60]", "tests2/test_smoke.py::test_smoke_all_routes[/api/supervision/document-image/{image_id}-methods61]", "tests2/test_smoke.py::test_smoke_all_routes[/api/supervision/download_csv-methods66]", "tests2/test_smoke.py::test_smoke_all_routes[/api/supervision/download_csv/{image_id}-methods63]", "tests2/test_smoke.py::test_smoke_all_routes[/api/supervision/download_txt/{image_id}-methods64]", "tests2/test_smoke.py::test_smoke_all_routes[/api/supervision/list-drive-folders-methods65]", "tests2/test_smoke.py::test_smoke_all_routes[/api/supervision/review-methods59]", "tests2/test_smoke.py::test_smoke_all_routes[/api/supervision/save-document-methods62]", "tests2/test_smoke.py::test_smoke_all_routes[/api/supervision/upload-methods58]", "tests2/test_smoke.py::test_smoke_all_routes[/api/synthetic-dataset/dataset-types-methods70]", "tests2/test_smoke.py::test_smoke_all_routes[/api/synthetic-dataset/generate-methods71]", "tests2/test_smoke.py::test_smoke_all_routes[/api/synthetic-dataset/generate-nonref-methods72]", "tests2/test_smoke.py::test_smoke_all_routes[/api/synthetic-dataset/knowledge-entries-methods73]", "tests2/test_smoke.py::test_smoke_all_routes[/api/synthetic-dataset/models-methods69]", "tests2/test_smoke.py::test_smoke_all_routes[/docs-methods1]", "tests2/test_smoke.py::test_smoke_all_routes[/docs/oauth2-redirect-methods2]", "tests2/test_smoke.py::test_smoke_all_routes[/openapi.json-methods0]", "tests2/test_smoke.py::test_smoke_all_routes[/redoc-methods3]", "tests2/test_smoke.py::test_smoke_app_startup", "tests2/test_smoke.py::test_smoke_basic_endpoints", "tests2/test_smoke.py::test_smoke_protected_routes[/-methods95]", "tests2/test_smoke.py::test_smoke_protected_routes[/admin/google-drive-callback-methods4]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/NoteOCR/documents/-methods52]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/NoteOCR/documents/-methods53]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/NoteOCR/documents/{document_id}-methods54]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/NoteOCR/documents/{document_id}-methods55]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/NoteOCR/images/chat-methods59]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/NoteOCR/images/extract-ocr-methods58]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/NoteOCR/images/extractor-mode-methods56]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/NoteOCR/images/upload-methods57]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/add-user-methods34]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/browse-nas-directory-methods38]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/browser/{folder:path}-methods43]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/check-google-drive-connection-methods27]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/check-nas-connection-methods25]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/configure-google-drive-methods26]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/connect-nas-methods23]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/dashboard-methods22]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/data-delivery-methods44]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/disconnect-nas-methods24]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/edit-instructions-methods35]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/edit-instructions-methods36]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/flush-db-methods29]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/get-datasets-methods42]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/image/{image_path:path}-methods46]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/merge-dataset-json-methods45]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/ocr-directory-methods37]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/reset-google-drive-methods28]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/select-annotation-folder-methods39]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/select-dataset-methods41]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/select-verification-folders-methods40]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/users-methods30]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/users/{username}-methods31]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/users/{username}-methods32]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/admin/users/{username}/suspend-methods33]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/annotator/annotate-methods48]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/annotator/dashboard-methods47]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/annotator/image/{image_path:path}-methods50]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/annotator/next-set-methods49]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/annotator/save-labels-methods51]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auditor/audit-record-methods20]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auditor/datasets-methods14]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auditor/files-methods16]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auditor/history-methods19]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auditor/image-methods18]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auditor/modes-methods13]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auditor/save-labels-methods21]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auditor/tasks-methods17]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auditor/verifiers-methods15]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auth/change-password-methods11]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auth/login-methods7]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auth/logout-methods8]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auth/me-methods12]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auth/refresh-methods9]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auth/refresh-token-methods5]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auth/register-methods6]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/auth/verify-methods10]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/client/datasets-methods69]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/client/datasets/{dataset_id}-methods70]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/knowledge-base/entries-methods76]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/knowledge-base/entries-methods79]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/knowledge-base/entries/{entry_id}-methods78]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/knowledge-base/entries/{entry_id}-methods80]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/knowledge-base/entries/{entry_id}-methods81]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/knowledge-base/topics-methods77]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/supervision/check-status/{image_id}-methods62]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/supervision/document-image/{image_id}-methods63]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/supervision/download_csv-methods68]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/supervision/download_csv/{image_id}-methods65]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/supervision/download_txt/{image_id}-methods66]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/supervision/list-drive-folders-methods67]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/supervision/review-methods61]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/supervision/save-document-methods64]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/supervision/upload-methods60]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/synthetic-dataset/dataset-types-methods72]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/synthetic-dataset/generate-methods73]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/synthetic-dataset/generate-nonref-methods74]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/synthetic-dataset/knowledge-entries-methods75]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/synthetic-dataset/models-methods71]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/analytics-methods92]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/channels-methods86]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/check-auth-methods85]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/connect-methods82]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/dates-methods89]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/disconnect-methods87]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/download-image-methods90]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/download-multiple-methods91]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/export-analytics-csv-methods94]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/images-methods88]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/upload-to-drive-methods93]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/verify-code-methods83]", "tests2/test_smoke.py::test_smoke_protected_routes[/api/telegram/verify-password-methods84]", "tests2/test_smoke.py::test_smoke_protected_routes[/docs-methods1]", "tests2/test_smoke.py::test_smoke_protected_routes[/docs/oauth2-redirect-methods2]", "tests2/test_smoke.py::test_smoke_protected_routes[/openapi.json-methods0]", "tests2/test_smoke.py::test_smoke_protected_routes[/redoc-methods3]", "tests2/test_smoke.py::test_smoke_public_routes[/-methods95]", "tests2/test_smoke.py::test_smoke_public_routes[/admin/google-drive-callback-methods4]", "tests2/test_smoke.py::test_smoke_public_routes[/api/NoteOCR/documents/-methods52]", "tests2/test_smoke.py::test_smoke_public_routes[/api/NoteOCR/documents/-methods53]", "tests2/test_smoke.py::test_smoke_public_routes[/api/NoteOCR/documents/{document_id}-methods54]", "tests2/test_smoke.py::test_smoke_public_routes[/api/NoteOCR/documents/{document_id}-methods55]", "tests2/test_smoke.py::test_smoke_public_routes[/api/NoteOCR/images/chat-methods59]", "tests2/test_smoke.py::test_smoke_public_routes[/api/NoteOCR/images/extract-ocr-methods58]", "tests2/test_smoke.py::test_smoke_public_routes[/api/NoteOCR/images/extractor-mode-methods56]", "tests2/test_smoke.py::test_smoke_public_routes[/api/NoteOCR/images/upload-methods57]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/add-user-methods34]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/browse-nas-directory-methods38]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/browser/{folder:path}-methods43]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/check-google-drive-connection-methods27]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/check-nas-connection-methods25]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/configure-google-drive-methods26]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/connect-nas-methods23]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/dashboard-methods22]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/data-delivery-methods44]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/disconnect-nas-methods24]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/edit-instructions-methods35]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/edit-instructions-methods36]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/flush-db-methods29]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/get-datasets-methods42]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/image/{image_path:path}-methods46]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/merge-dataset-json-methods45]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/ocr-directory-methods37]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/reset-google-drive-methods28]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/select-annotation-folder-methods39]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/select-dataset-methods41]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/select-verification-folders-methods40]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/users-methods30]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/users/{username}-methods31]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/users/{username}-methods32]", "tests2/test_smoke.py::test_smoke_public_routes[/api/admin/users/{username}/suspend-methods33]", "tests2/test_smoke.py::test_smoke_public_routes[/api/annotator/annotate-methods48]", "tests2/test_smoke.py::test_smoke_public_routes[/api/annotator/dashboard-methods47]", "tests2/test_smoke.py::test_smoke_public_routes[/api/annotator/image/{image_path:path}-methods50]", "tests2/test_smoke.py::test_smoke_public_routes[/api/annotator/next-set-methods49]", "tests2/test_smoke.py::test_smoke_public_routes[/api/annotator/save-labels-methods51]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auditor/audit-record-methods20]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auditor/datasets-methods14]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auditor/files-methods16]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auditor/history-methods19]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auditor/image-methods18]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auditor/modes-methods13]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auditor/save-labels-methods21]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auditor/tasks-methods17]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auditor/verifiers-methods15]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auth/change-password-methods11]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auth/login-methods7]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auth/logout-methods8]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auth/me-methods12]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auth/refresh-methods9]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auth/refresh-token-methods5]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auth/register-methods6]", "tests2/test_smoke.py::test_smoke_public_routes[/api/auth/verify-methods10]", "tests2/test_smoke.py::test_smoke_public_routes[/api/client/datasets-methods69]", "tests2/test_smoke.py::test_smoke_public_routes[/api/client/datasets/{dataset_id}-methods70]", "tests2/test_smoke.py::test_smoke_public_routes[/api/knowledge-base/entries-methods76]", "tests2/test_smoke.py::test_smoke_public_routes[/api/knowledge-base/entries-methods79]", "tests2/test_smoke.py::test_smoke_public_routes[/api/knowledge-base/entries/{entry_id}-methods78]", "tests2/test_smoke.py::test_smoke_public_routes[/api/knowledge-base/entries/{entry_id}-methods80]", "tests2/test_smoke.py::test_smoke_public_routes[/api/knowledge-base/entries/{entry_id}-methods81]", "tests2/test_smoke.py::test_smoke_public_routes[/api/knowledge-base/topics-methods77]", "tests2/test_smoke.py::test_smoke_public_routes[/api/supervision/check-status/{image_id}-methods62]", "tests2/test_smoke.py::test_smoke_public_routes[/api/supervision/document-image/{image_id}-methods63]", "tests2/test_smoke.py::test_smoke_public_routes[/api/supervision/download_csv-methods68]", "tests2/test_smoke.py::test_smoke_public_routes[/api/supervision/download_csv/{image_id}-methods65]", "tests2/test_smoke.py::test_smoke_public_routes[/api/supervision/download_txt/{image_id}-methods66]", "tests2/test_smoke.py::test_smoke_public_routes[/api/supervision/list-drive-folders-methods67]", "tests2/test_smoke.py::test_smoke_public_routes[/api/supervision/review-methods61]", "tests2/test_smoke.py::test_smoke_public_routes[/api/supervision/save-document-methods64]", "tests2/test_smoke.py::test_smoke_public_routes[/api/supervision/upload-methods60]", "tests2/test_smoke.py::test_smoke_public_routes[/api/synthetic-dataset/dataset-types-methods72]", "tests2/test_smoke.py::test_smoke_public_routes[/api/synthetic-dataset/generate-methods73]", "tests2/test_smoke.py::test_smoke_public_routes[/api/synthetic-dataset/generate-nonref-methods74]", "tests2/test_smoke.py::test_smoke_public_routes[/api/synthetic-dataset/knowledge-entries-methods75]", "tests2/test_smoke.py::test_smoke_public_routes[/api/synthetic-dataset/models-methods71]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/analytics-methods92]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/channels-methods86]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/check-auth-methods85]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/connect-methods82]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/dates-methods89]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/disconnect-methods87]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/download-image-methods90]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/download-multiple-methods91]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/export-analytics-csv-methods94]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/images-methods88]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/upload-to-drive-methods93]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/verify-code-methods83]", "tests2/test_smoke.py::test_smoke_public_routes[/api/telegram/verify-password-methods84]", "tests2/test_smoke.py::test_smoke_public_routes[/docs-methods1]", "tests2/test_smoke.py::test_smoke_public_routes[/docs/oauth2-redirect-methods2]", "tests2/test_smoke.py::test_smoke_public_routes[/openapi.json-methods0]", "tests2/test_smoke.py::test_smoke_public_routes[/redoc-methods3]", "tests2/test_smoke.py::test_smoke_route_discovery", "tests2/unit/test_schemas.py::test_schema_valid[AccessTokenResponse-valid9-invalid9]", "tests2/unit/test_schemas.py::test_schema_valid[AddUserRequest-valid4-invalid4]", "tests2/unit/test_schemas.py::test_schema_valid[AdminInstruction-valid35-invalid35]", "tests2/unit/test_schemas.py::test_schema_valid[AdminSetting-valid34-invalid34]", "tests2/unit/test_schemas.py::test_schema_valid[BatchAssignmentRequest-valid21-invalid21]", "tests2/unit/test_schemas.py::test_schema_valid[BatchInfo-valid11-invalid11]", "tests2/unit/test_schemas.py::test_schema_valid[ChangePasswordRequest-valid2-invalid2]", "tests2/unit/test_schemas.py::test_schema_valid[ChangePasswordRequest-valid43-invalid43]", "tests2/unit/test_schemas.py::test_schema_valid[CropImageRequest-valid15-invalid15]", "tests2/unit/test_schemas.py::test_schema_valid[CropImageResponse-valid16-invalid16]", "tests2/unit/test_schemas.py::test_schema_valid[Document-valid27-invalid27]", "tests2/unit/test_schemas.py::test_schema_valid[DocumentBase-valid26-invalid26]", "tests2/unit/test_schemas.py::test_schema_valid[DocumentSummary-valid28-invalid28]", "tests2/unit/test_schemas.py::test_schema_valid[ErrorResponse-valid37-invalid37]", "tests2/unit/test_schemas.py::test_schema_valid[ErrorResponse-valid39-invalid39]", "tests2/unit/test_schemas.py::test_schema_valid[ErrorResponse-valid41-invalid41]", "tests2/unit/test_schemas.py::test_schema_valid[FTPConfig-valid17-invalid17]", "tests2/unit/test_schemas.py::test_schema_valid[GenerateConceptsRequest-valid30-invalid30]", "tests2/unit/test_schemas.py::test_schema_valid[GenerateQARequest-valid29-invalid29]", "tests2/unit/test_schemas.py::test_schema_valid[Image-valid23-invalid23]", "tests2/unit/test_schemas.py::test_schema_valid[ImageBase-valid22-invalid22]", "tests2/unit/test_schemas.py::test_schema_valid[ImageBatchBase-valid18-invalid18]", "tests2/unit/test_schemas.py::test_schema_valid[ImageInfo-valid10-invalid10]", "tests2/unit/test_schemas.py::test_schema_valid[ImageResponse-valid14-invalid14]", "tests2/unit/test_schemas.py::test_schema_valid[JsonContentRequest-valid31-invalid31]", "tests2/unit/test_schemas.py::test_schema_valid[LoginRequest-valid1-invalid1]", "tests2/unit/test_schemas.py::test_schema_valid[Page-valid25-invalid25]", "tests2/unit/test_schemas.py::test_schema_valid[PageBase-valid24-invalid24]", "tests2/unit/test_schemas.py::test_schema_valid[ProcessedManualBatch-valid19-invalid19]", "tests2/unit/test_schemas.py::test_schema_valid[ProcessedVerificationBatch-valid20-invalid20]", "tests2/unit/test_schemas.py::test_schema_valid[RefreshTokenRequest-valid6-invalid6]", "tests2/unit/test_schemas.py::test_schema_valid[SaveLabelsRequest-valid12-invalid12]", "tests2/unit/test_schemas.py::test_schema_valid[SaveLabelsResponse-valid13-invalid13]", "tests2/unit/test_schemas.py::test_schema_valid[SuccessResponse-valid36-invalid36]", "tests2/unit/test_schemas.py::test_schema_valid[SuccessResponse-valid38-invalid38]", "tests2/unit/test_schemas.py::test_schema_valid[SuccessResponse-valid40-invalid40]", "tests2/unit/test_schemas.py::test_schema_valid[TaskResponse-valid32-invalid32]", "tests2/unit/test_schemas.py::test_schema_valid[TasksResponse-valid33-invalid33]", "tests2/unit/test_schemas.py::test_schema_valid[TokenResponse-valid8-invalid8]", "tests2/unit/test_schemas.py::test_schema_valid[UserCreate-valid3-invalid3]", "tests2/unit/test_schemas.py::test_schema_valid[UserRegisterRequest-valid0-invalid0]", "tests2/unit/test_schemas.py::test_schema_valid[UserRegisterRequest-valid42-invalid42]", "tests2/unit/test_schemas.py::test_schema_valid[UserResponse-valid7-invalid7]", "tests2/unit/test_schemas.py::test_schema_valid[UserUpdate-valid5-invalid5]"]