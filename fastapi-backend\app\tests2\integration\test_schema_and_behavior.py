import pytest
import os
from fastapi.testclient import TestClient
from sqlalchemy import inspect, MetaData, func, text
from sqlalchemy.exc import IntegrityError
from db.db_connector import engine, get_db_connection
from db import db_connector
from db.models.user import User, UserRole, AnnotatorMode
from db.models.knowledge_base import KnowledgeEntry
from db.models.pdf_extractor import Document, Page, Image

from main import app

client = TestClient(app)

# 1. Schema & Structure Tests

def test_user_table_schema(db_session):
    engine_bind = db_session.get_bind()
    inspector = inspect(engine_bind)
    columns = inspector.get_columns('users')
    column_names = {col['name'] for col in columns}
    expected = {'id', 'username', 'full_name', 'email', 'role', 'password_hash', 'created_at', 'last_login', 'is_active', 'annotator_mode'}
    assert expected.issubset(column_names)
    for col in columns:
        if col['name'] in ('username', 'full_name', 'email', 'password_hash', 'role', 'is_active', 'annotator_mode'):
            assert not col['nullable']
    uniques = inspector.get_unique_constraints('users')
    unique_cols = {tuple(uc['column_names']) for uc in uniques}
    assert ('username', 'email') in unique_cols
    for col in columns:
        name, col_type = col['name'], col['type']
        if name == 'id':
            from sqlalchemy import Integer
            assert isinstance(col_type, Integer)
        elif name in ('username', 'email', 'password_hash', 'full_name', 'role', 'annotator_mode'):
            from sqlalchemy import String
            assert isinstance(col_type, String)
        elif name == 'is_active':
            from sqlalchemy import Boolean
            assert isinstance(col_type, Boolean)
        elif name in ('created_at', 'last_login'):
            from sqlalchemy import DateTime
            assert isinstance(col_type, DateTime)


def test_pdf_extractor_schema(db_session):
    inspector = inspect(db_session.get_bind())
    tables = inspector.get_table_names()
    assert 'documents' in tables and 'pages' in tables and 'images' in tables
    fks_pages = inspector.get_foreign_keys('pages')
    assert any(fk['referred_table']=='documents' for fk in fks_pages)
    fks_images = inspector.get_foreign_keys('images')
    assert any(fk['referred_table']=='pages' for fk in fks_images)


def test_user_defaults_and_indexes(db_session):
    inspector = inspect(db_session.get_bind())
    indexes = inspector.get_indexes('users')
    idx_cols = {tuple(idx['column_names']) for idx in indexes}
    assert ('username',) in idx_cols and ('email',) in idx_cols
    u = User(username='defuser', full_name='Default User', email='<EMAIL>', password_hash='h')
    db_session.add(u)
    db_session.commit()
    fetched = db_session.query(User).filter_by(username='defuser').one()
    assert fetched.role == UserRole.ANNOTATOR
    assert fetched.is_active
    assert fetched.annotator_mode == AnnotatorMode.ANNOTATION
    assert fetched.created_at is not None
    db_session.delete(fetched)
    db_session.commit()


def test_knowledge_base_schema(db_session):
    inspector = inspect(db_session.get_bind())
    cols = inspector.get_columns('knowledge_base')
    for col in cols:
        name, col_type = col['name'], col['type']
        if name=='title':
            from sqlalchemy import String
            assert isinstance(col_type, String) and col_type.length==255
        elif name=='topic':
            from sqlalchemy import String
            assert isinstance(col_type, String) and col_type.length==100
        elif name=='content':
            from sqlalchemy import Text
            assert isinstance(col_type, Text)


def test_check_constraints_naming(db_session):
    inspector = inspect(db_session.get_bind())
    for table in inspector.get_table_names():
        for ck in inspector.get_check_constraints(table):
            name = ck.get('name')
            assert name and name.startswith('ck_')

# 2. Data Integrity & Behavior

def test_invalid_db_url(monkeypatch):
    monkeypatch.setenv('DATABASE_URL','invalid://')
    gen = db_connector.get_db_connection()
    db = next(gen)
    assert db is not None
    gen.close()


def test_commit_rollback(monkeypatch, db_session):
    def fail():
        raise IntegrityError('fail',None,None)
    db_session.commit = fail
    with pytest.raises(IntegrityError):
        db_session.commit()


@pytest.mark.parametrize('endpoint,payload',[
    ('/api/auth/register',{"username":"auser","password":"Password1!","confirm_password":"Password1!","role":"admin","full_name":"A","email":"<EMAIL>"}),
])
def test_unique_constraint(endpoint,payload,db_session):
    resp1 = client.post(endpoint,json=payload)
    resp2 = client.post(endpoint,json=payload)
    assert resp2.status_code==400


def test_user_unique_constraints_direct(db_session):
    from sqlalchemy.exc import IntegrityError
    u1=User(username='du',full_name='DF',email='<EMAIL>',password_hash='h')
    db_session.add(u1)
    db_session.commit()
    u2=User(username='du',full_name='DF',email='<EMAIL>',password_hash='h')
    db_session.add(u2)
    with pytest.raises(IntegrityError):
        db_session.commit()


def test_not_null_constraints(db_session):
    from sqlalchemy.exc import IntegrityError
    user=User(username=None,full_name=None,email=None,password_hash=None)
    db_session.add(user)
    with pytest.raises(IntegrityError): db_session.commit() 