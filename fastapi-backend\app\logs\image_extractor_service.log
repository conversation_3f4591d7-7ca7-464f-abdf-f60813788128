2025-07-04 12:31:58,560 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-04 12:31:58,566 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:31:58,569 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:31:58,573 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:44:19,623 - image_extractor_service - INFO - Stored image to database with ID: 2, filename: test.png
2025-07-04 12:44:19,627 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:44:19,631 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:44:19,637 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:46:58,750 - image_extractor_service - INFO - Stored image to database with ID: 3, filename: test.png
2025-07-04 12:46:58,752 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:46:58,752 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:46:58,761 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:51:07,775 - image_extractor_service - INFO - Stored image to database with ID: 4, filename: test.png
2025-07-04 12:51:07,780 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:51:07,785 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:51:07,789 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:55:55,513 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-04 12:55:55,517 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:55:55,521 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 12:55:55,525 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 13:03:16,654 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-04 13:03:16,658 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 13:03:16,661 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 13:03:16,666 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 13:13:05,398 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-04 13:13:05,404 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 13:13:05,408 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 13:13:05,417 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 14:10:36,745 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-04 14:10:36,752 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 14:10:36,758 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 14:10:36,766 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 15:01:39,905 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-04 15:01:39,911 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 15:01:39,916 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 15:01:39,921 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 15:20:06,985 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-04 15:20:06,992 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 15:20:06,997 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 15:20:07,004 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 17:23:46,616 - image_extractor_service - ERROR - Invalid image ID: 
2025-07-04 17:37:33,339 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-04 17:37:33,345 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 17:37:33,350 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 17:37:33,354 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 17:52:21,016 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-04 17:52:21,017 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 17:52:21,017 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-04 17:52:21,017 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 12:03:36,838 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-05 12:03:36,843 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 12:03:36,846 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 12:03:36,852 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 12:16:52,549 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-05 12:16:52,557 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 12:16:52,559 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 12:16:52,561 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 14:17:10,879 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-05 14:17:10,886 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 14:17:10,892 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 14:17:10,892 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 14:41:23,377 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-05 14:41:23,385 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 14:41:23,392 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 14:41:23,398 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 16:36:18,335 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-05 16:36:18,338 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 16:36:18,338 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 16:36:18,351 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 16:46:50,792 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-05 16:46:50,797 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 16:46:50,799 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 16:46:50,806 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 18:55:22,924 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-05 18:55:22,925 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 18:55:22,925 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 18:55:22,925 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-05 18:55:22,946 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 11:48:15,406 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-07 11:48:15,410 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 11:48:15,413 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 11:48:15,417 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 11:48:15,420 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 11:55:24,001 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-07 11:55:24,005 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 11:55:24,009 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 11:55:24,012 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 11:55:24,015 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 12:18:26,673 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-07 12:18:26,678 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 12:18:26,680 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 12:18:26,683 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 12:18:26,686 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 12:35:12,521 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-07 12:35:12,530 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 12:35:12,535 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 12:35:12,542 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 12:35:12,548 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 14:48:51,506 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-07 14:48:51,510 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 14:48:51,514 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 14:48:51,518 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 14:48:51,522 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 14:59:46,710 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-07 14:59:46,719 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 14:59:46,724 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 14:59:46,727 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-07 14:59:46,727 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 10:39:47,104 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-11 10:39:47,112 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 10:39:47,117 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 10:39:47,120 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 10:39:47,125 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 11:40:25,984 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-11 11:41:35,510 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-11 11:52:58,707 - image_extractor_service - ERROR - Invalid image ID: 
2025-07-11 14:10:25,295 - image_extractor_service - ERROR - Error processing image: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.jpg', 'ZmFrZV9pbWFnZV9kYXRh', '2025-07-11 14:05:17.232652')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: database is locked

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\NoteOCR\image_extractor_service.py", line 62, in process_image
    session.commit()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 93, in save_obj
    _emit_insert_statements(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.jpg', 'ZmFrZV9pbWFnZV9kYXRh', '2025-07-11 14:05:17.232652')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:10:27,316 - image_extractor_service - ERROR - Error cleaning up old image metadata: (sqlite3.OperationalError) database is locked
[SQL: DELETE FROM image_extractor WHERE image_extractor.uploaded_at < ?]
[parameters: ('2025-07-10 14:10:27.314606',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: database is locked

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\NoteOCR\image_extractor_service.py", line 250, in cleanup_old_images
    deleted = session.query(ImageExtractor).filter(ImageExtractor.uploaded_at < cutoff).delete()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\query.py", line 3208, in delete
    result: CursorResult[Any] = self.session.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\bulk_persistence.py", line 2021, in orm_execute_statement
    return super().orm_execute_statement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked
[SQL: DELETE FROM image_extractor WHERE image_extractor.uploaded_at < ?]
[parameters: ('2025-07-10 14:10:27.314606',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:10:27,371 - image_extractor_service - ERROR - Error processing image: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.png', 'iVBORw0KGgowMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAw', '2025-07-11 14:05:17.232652')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: database is locked

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\NoteOCR\image_extractor_service.py", line 62, in process_image
    session.commit()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 93, in save_obj
    _emit_insert_statements(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.png', 'iVBORw0KGgowMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAw', '2025-07-11 14:05:17.232652')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-11 14:10:27,377 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 14:10:27,379 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 14:10:27,382 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 14:10:27,385 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 14:45:48,097 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-11 14:46:40,694 - image_extractor_service - INFO - Stored image to database with ID: 2, filename: test.png
2025-07-11 14:46:40,694 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 14:46:40,694 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 14:46:40,703 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 14:46:40,707 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 14:57:11,793 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-11 14:57:27,308 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-11 14:57:27,310 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 14:57:27,310 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 14:57:27,310 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-11 14:57:27,321 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 10:25:20,676 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-12 10:25:25,589 - image_extractor_service - INFO - Stored image to database with ID: 2, filename: test.png
2025-07-12 10:25:25,594 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 10:25:25,605 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 10:25:25,605 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 10:25:25,629 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 11:04:27,331 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-12 11:04:30,365 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-12 11:04:30,365 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 11:04:30,365 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 11:04:30,365 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 11:04:30,365 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 11:20:01,579 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-12 11:20:04,421 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-12 11:20:04,424 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 11:20:04,429 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 11:20:04,432 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 11:20:04,435 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 12:56:45,107 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-12 12:56:47,991 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-12 12:56:47,993 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 12:56:47,997 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 12:56:48,000 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 12:56:48,004 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:10:10,452 - image_extractor_service - ERROR - Error processing image: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.jpg', 'ZmFrZV9pbWFnZV9kYXRh', '2025-07-12 13:05:03.360880')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: database is locked

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\NoteOCR\image_extractor_service.py", line 62, in process_image
    session.commit()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 93, in save_obj
    _emit_insert_statements(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.jpg', 'ZmFrZV9pbWFnZV9kYXRh', '2025-07-12 13:05:03.360880')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:10:12,477 - image_extractor_service - ERROR - Error cleaning up old image metadata: (sqlite3.OperationalError) database is locked
[SQL: DELETE FROM image_extractor WHERE image_extractor.uploaded_at < ?]
[parameters: ('2025-07-11 13:10:12.476519',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: database is locked

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\NoteOCR\image_extractor_service.py", line 250, in cleanup_old_images
    deleted = session.query(ImageExtractor).filter(ImageExtractor.uploaded_at < cutoff).delete()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\query.py", line 3208, in delete
    result: CursorResult[Any] = self.session.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\bulk_persistence.py", line 2021, in orm_execute_statement
    return super().orm_execute_statement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked
[SQL: DELETE FROM image_extractor WHERE image_extractor.uploaded_at < ?]
[parameters: ('2025-07-11 13:10:12.476519',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:10:12,515 - image_extractor_service - ERROR - Error processing image: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.png', 'iVBORw0KGgowMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAw', '2025-07-12 13:05:03.360880')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: database is locked

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\NoteOCR\image_extractor_service.py", line 62, in process_image
    session.commit()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 93, in save_obj
    _emit_insert_statements(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.png', 'iVBORw0KGgowMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAw', '2025-07-12 13:05:03.360880')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:10:12,517 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:10:12,517 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:10:12,517 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:10:12,517 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:11:38,707 - image_extractor_service - ERROR - Error cleaning up old image metadata: (sqlite3.OperationalError) database is locked
[SQL: DELETE FROM image_extractor WHERE image_extractor.uploaded_at < ?]
[parameters: ('2025-07-11 13:11:38.707306',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: database is locked

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\NoteOCR\image_extractor_service.py", line 250, in cleanup_old_images
    deleted = session.query(ImageExtractor).filter(ImageExtractor.uploaded_at < cutoff).delete()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\query.py", line 3208, in delete
    result: CursorResult[Any] = self.session.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\bulk_persistence.py", line 2021, in orm_execute_statement
    return super().orm_execute_statement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked
[SQL: DELETE FROM image_extractor WHERE image_extractor.uploaded_at < ?]
[parameters: ('2025-07-11 13:11:38.707306',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:20,977 - image_extractor_service - ERROR - Error processing image: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.jpg', 'ZmFrZV9pbWFnZV9kYXRh', '2025-07-12 13:12:13.667903')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: database is locked

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\NoteOCR\image_extractor_service.py", line 62, in process_image
    session.commit()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 93, in save_obj
    _emit_insert_statements(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.jpg', 'ZmFrZV9pbWFnZV9kYXRh', '2025-07-12 13:12:13.667903')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:22,964 - image_extractor_service - ERROR - Error cleaning up old image metadata: (sqlite3.OperationalError) database is locked
[SQL: DELETE FROM image_extractor WHERE image_extractor.uploaded_at < ?]
[parameters: ('2025-07-11 13:17:22.963285',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: database is locked

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\NoteOCR\image_extractor_service.py", line 250, in cleanup_old_images
    deleted = session.query(ImageExtractor).filter(ImageExtractor.uploaded_at < cutoff).delete()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\query.py", line 3208, in delete
    result: CursorResult[Any] = self.session.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\bulk_persistence.py", line 2021, in orm_execute_statement
    return super().orm_execute_statement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked
[SQL: DELETE FROM image_extractor WHERE image_extractor.uploaded_at < ?]
[parameters: ('2025-07-11 13:17:22.963285',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:22,999 - image_extractor_service - ERROR - Error processing image: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.png', 'iVBORw0KGgowMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAw', '2025-07-12 13:12:13.667903')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: database is locked

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\NoteOCR\image_extractor_service.py", line 62, in process_image
    session.commit()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 93, in save_obj
    _emit_insert_statements(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked
[SQL: INSERT INTO image_extractor (image_name, image_data, uploaded_at) VALUES (?, ?, ?)]
[parameters: ('test.png', 'iVBORw0KGgowMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAw', '2025-07-12 13:12:13.667903')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:17:23,006 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:17:23,009 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:17:23,012 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:17:23,015 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:18:49,776 - image_extractor_service - ERROR - Error cleaning up old image metadata: (sqlite3.OperationalError) database is locked
[SQL: DELETE FROM image_extractor WHERE image_extractor.uploaded_at < ?]
[parameters: ('2025-07-11 13:18:49.775244',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: database is locked

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\fastapi-backend\app\NoteOCR\image_extractor_service.py", line 250, in cleanup_old_images
    deleted = session.query(ImageExtractor).filter(ImageExtractor.uploaded_at < cutoff).delete()
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\query.py", line 3208, in delete
    result: CursorResult[Any] = self.session.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\bulk_persistence.py", line 2021, in orm_execute_statement
    return super().orm_execute_statement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Desktop\DADP-Prod-FN-1\venv\lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked
[SQL: DELETE FROM image_extractor WHERE image_extractor.uploaded_at < ?]
[parameters: ('2025-07-11 13:18:49.775244',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-12 13:20:37,187 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-12 13:41:33,520 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-12 13:41:36,460 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-12 13:41:36,464 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:41:36,467 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:41:36,470 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:41:36,473 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:56:48,909 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-12 13:56:51,985 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-12 13:56:51,988 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:56:51,991 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:56:51,995 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 13:56:51,999 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:06:37,171 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-12 14:06:40,109 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-12 14:06:40,112 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:06:40,116 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:06:40,119 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:06:40,119 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:14:41,290 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-12 14:14:44,320 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-12 14:14:44,323 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:14:44,324 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:14:44,333 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:14:44,335 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:23:30,042 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-12 14:23:32,951 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-12 14:23:32,954 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:23:32,954 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:23:32,958 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:23:32,958 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:44:56,493 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.jpg
2025-07-12 14:44:59,608 - image_extractor_service - INFO - Stored image to database with ID: 1, filename: test.png
2025-07-12 14:44:59,611 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:44:59,614 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:44:59,618 - image_extractor_service - ERROR - Invalid image ID: fakeid
2025-07-12 14:44:59,621 - image_extractor_service - ERROR - Invalid image ID: fakeid
