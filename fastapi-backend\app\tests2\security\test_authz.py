import pytest
from main import app
from fastapi.testclient import TestClient

client = TestClient(app)

# Collect all protected endpoints (GET/POST/PUT/DELETE)
protected_endpoints = [
    (route.path, list(route.methods))
    for route in app.routes
    if hasattr(route, 'dependencies') and route.dependencies
]

# Collect all admin-only endpoints
admin_only_endpoints = [
    (route.path, list(route.methods))
    for route in app.routes
    if hasattr(route, 'dependencies') and any('require_admin' in str(dep.dependency) for dep in route.dependencies)
]

# Collect all auditor-only endpoints
auditor_only_endpoints = [
    (route.path, list(route.methods))
    for route in app.routes
    if hasattr(route, 'dependencies') and any('require_auditor' in str(dep.dependency) for dep in route.dependencies)
]

# Collect all client-only endpoints
client_only_endpoints = [
    (route.path, list(route.methods))
    for route in app.routes
    if hasattr(route, 'dependencies') and any('require_client' in str(dep.dependency) for dep in route.dependencies)
]

# Collect all annotator-only endpoints
annotator_only_endpoints = [
    (route.path, list(route.methods))
    for route in app.routes
    if hasattr(route, 'dependencies') and any('require_annotator' in str(dep.dependency) for dep in route.dependencies)
]

@pytest.fixture(scope='module')
def admin_token():
    payload = {"username": "authzadmin", "password": "Adminpass123!", "confirm_password": "Adminpass123!", "role": "admin", "full_name": "Admin User", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=payload)
    resp = client.post('/api/auth/login', data={"username": "authzadmin", "password": "Adminpass123!"})
    return resp.json().get("access_token"), resp.cookies

@pytest.fixture(scope='module')
def annotator_token():
    payload = {"username": "authzanno", "password": "Annopass123!", "confirm_password": "Annopass123!", "role": "annotator", "full_name": "Anno User", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=payload)
    resp = client.post('/api/auth/login', data={"username": "authzanno", "password": "Annopass123!"})
    return resp.json().get("access_token"), resp.cookies

@pytest.fixture(scope='module')
def auditor_token():
    payload = {"username": "authzauditor", "password": "Auditorpass123!", "confirm_password": "Auditorpass123!", "role": "auditor", "full_name": "Auditor User", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=payload)
    resp = client.post('/api/auth/login', data={"username": "authzauditor", "password": "Auditorpass123!"})
    return resp.json().get("access_token"), resp.cookies

@pytest.fixture(scope='module')
def client_token():
    payload = {"username": "authzclient", "password": "Clientpass123!", "confirm_password": "Clientpass123!", "role": "client", "full_name": "Client User", "email": "<EMAIL>"}
    client.post('/api/auth/register', json=payload)
    resp = client.post('/api/auth/login', data={"username": "authzclient", "password": "Clientpass123!"})
    return resp.json().get("access_token"), resp.cookies

def test_protected_401():
    """Test that all protected endpoints return 401 without authentication."""
    for path, methods in protected_endpoints:
        for method in methods:
            if method == 'GET':
                resp = client.get(path)
            elif method == 'POST':
                resp = client.post(path)
            elif method == 'PUT':
                resp = client.put(path)
            elif method == 'DELETE':
                resp = client.delete(path)
            else:
                continue
            assert resp.status_code == 401, f"Endpoint {method} {path} should return 401 without auth"

def test_admin_403_with_non_admin_roles(annotator_token, auditor_token, client_token):
    """Test that admin-only endpoints return 403 for non-admin roles."""
    for role_name, (token, cookies) in [("annotator", annotator_token), ("auditor", auditor_token), ("client", client_token)]:
        headers = {'Authorization': f'Bearer {token}'}
        for path, methods in admin_only_endpoints:
            for method in methods:
                if method == 'GET':
                    resp = client.get(path, headers=headers, cookies=cookies)
                elif method == 'POST':
                    resp = client.post(path, headers=headers, cookies=cookies)
                elif method == 'PUT':
                    resp = client.put(path, headers=headers, cookies=cookies)
                elif method == 'DELETE':
                    resp = client.delete(path, headers=headers, cookies=cookies)
                else:
                    continue
                assert resp.status_code == 403, f"Admin endpoint {method} {path} should return 403 for {role_name}"

def test_auditor_403_with_non_auditor_roles(annotator_token, client_token, admin_token):
    """Test that auditor-only endpoints return 403 for non-auditor roles."""
    for role_name, (token, cookies) in [("annotator", annotator_token), ("client", client_token), ("admin", admin_token)]:
        headers = {'Authorization': f'Bearer {token}'}
        for path, methods in auditor_only_endpoints:
            for method in methods:
                if method == 'GET':
                    resp = client.get(path, headers=headers, cookies=cookies)
                elif method == 'POST':
                    resp = client.post(path, headers=headers, cookies=cookies)
                elif method == 'PUT':
                    resp = client.put(path, headers=headers, cookies=cookies)
                elif method == 'DELETE':
                    resp = client.delete(path, headers=headers, cookies=cookies)
                else:
                    continue
                assert resp.status_code == 403, f"Auditor endpoint {method} {path} should return 403 for {role_name}"

def test_client_403_with_non_client_roles(annotator_token, auditor_token, admin_token):
    """Test that client-only endpoints return 403 for non-client roles."""
    for role_name, (token, cookies) in [("annotator", annotator_token), ("auditor", auditor_token), ("admin", admin_token)]:
        headers = {'Authorization': f'Bearer {token}'}
        for path, methods in client_only_endpoints:
            for method in methods:
                if method == 'GET':
                    resp = client.get(path, headers=headers, cookies=cookies)
                elif method == 'POST':
                    resp = client.post(path, headers=headers, cookies=cookies)
                elif method == 'PUT':
                    resp = client.put(path, headers=headers, cookies=cookies)
                elif method == 'DELETE':
                    resp = client.delete(path, headers=headers, cookies=cookies)
                else:
                    continue
                assert resp.status_code == 403, f"Client endpoint {method} {path} should return 403 for {role_name}"

def test_annotator_403_with_non_annotator_roles(auditor_token, client_token):
    """Test that annotator-only endpoints return 403 for non-annotator roles."""
    for role_name, (token, cookies) in [("auditor", auditor_token), ("client", client_token)]:
        headers = {'Authorization': f'Bearer {token}'}
        for path, methods in annotator_only_endpoints:
            for method in methods:
                if method == 'GET':
                    resp = client.get(path, headers=headers, cookies=cookies)
                elif method == 'POST':
                    resp = client.post(path, headers=headers, cookies=cookies)
                elif method == 'PUT':
                    resp = client.put(path, headers=headers, cookies=cookies)
                elif method == 'DELETE':
                    resp = client.delete(path, headers=headers, cookies=cookies)
                else:
                    continue
                assert resp.status_code == 403, f"Annotator endpoint {method} {path} should return 403 for {role_name}"

def test_expired_malformed_jwt():
    """Test that expired/malformed JWT tokens return 401."""
    # Use a fresh TestClient instance to avoid stale cookies
    client_no_cookie = TestClient(app)
    for path, methods in protected_endpoints:
        for method in methods:
            headers = {'Authorization': 'Bearer expiredtoken'}
            if method == 'GET':
                resp = client_no_cookie.get(path, headers=headers)
            elif method == 'POST':
                resp = client_no_cookie.post(path, headers=headers)
            elif method == 'PUT':
                resp = client_no_cookie.put(path, headers=headers)
            elif method == 'DELETE':
                resp = client_no_cookie.delete(path, headers=headers)
            else:
                continue
            assert resp.status_code == 401, f"Endpoint {method} {path} should return 401 for expired token"

def test_malformed_bearer_header():
    """Test that malformed Authorization headers return 401."""
    client_no_cookie = TestClient(app)
    malformed_headers = [
        {'Authorization': 'Bearer'},  # Missing token
        {'Authorization': 'Basic dGVzdA=='},  # Wrong auth type
        {'Authorization': 'Bearer token with spaces'},  # Invalid token format
        {'Authorization': 'bearer lowercase'},  # Wrong case
    ]
    
    for headers in malformed_headers:
        for path, methods in protected_endpoints[:3]:  # Test first 3 endpoints only
            for method in methods:
                if method == 'GET':
                    resp = client_no_cookie.get(path, headers=headers)
                elif method == 'POST':
                    resp = client_no_cookie.post(path, headers=headers)
                elif method == 'PUT':
                    resp = client_no_cookie.put(path, headers=headers)
                elif method == 'DELETE':
                    resp = client_no_cookie.delete(path, headers=headers)
                else:
                    continue
                assert resp.status_code == 401, f"Endpoint {method} {path} should return 401 for malformed header {headers}"

def test_logout_clears_cookies():
    """Test that logout clears authentication cookies."""
    resp = client.post('/api/auth/logout')
    assert resp.status_code == 200
    resp2 = client.get('/api/auth/me')
    assert resp2.status_code == 401

def test_inactive_user_access():
    """Test that inactive users cannot access protected endpoints."""
    # This test would require a way to deactivate users
    # Implementation depends on admin user management endpoints
    pass

# Legacy test functions for backward compatibility
def test_admin_403_real_token(annotator_token):
    """Legacy test - use test_admin_403_with_non_admin_roles instead."""
    token, cookies = annotator_token
    headers = {'Authorization': f'Bearer {token}'}
    for path, methods in admin_only_endpoints:
        for method in methods:
            if method == 'GET':
                resp = client.get(path, headers=headers, cookies=cookies)
            elif method == 'POST':
                resp = client.post(path, headers=headers, cookies=cookies)
            elif method == 'PUT':
                resp = client.put(path, headers=headers, cookies=cookies)
            elif method == 'DELETE':
                resp = client.delete(path, headers=headers, cookies=cookies)
            else:
                continue
            assert resp.status_code == 403

def test_role_escalation_annotator_to_admin(annotator_token):
    """Legacy test - use test_admin_403_with_non_admin_roles instead."""
    token, cookies = annotator_token
    headers = {'Authorization': f'Bearer {token}'}
    for path, methods in admin_only_endpoints:
        for method in methods:
            if method == 'GET':
                resp = client.get(path, headers=headers, cookies=cookies)
            elif method == 'POST':
                resp = client.post(path, headers=headers, cookies=cookies)
            elif method == 'PUT':
                resp = client.put(path, headers=headers, cookies=cookies)
            elif method == 'DELETE':
                resp = client.delete(path, headers=headers, cookies=cookies)
            else:
                continue
            assert resp.status_code == 403

def test_role_escalation_client_to_annotator(client_token):
    """Legacy test - use test_annotator_403_with_non_annotator_roles instead."""
    token, cookies = client_token
    headers = {'Authorization': f'Bearer {token}'}
    for path, methods in annotator_only_endpoints:
        for method in methods:
            if method == 'GET':
                resp = client.get(path, headers=headers, cookies=cookies)
            elif method == 'POST':
                resp = client.post(path, headers=headers, cookies=cookies)
            elif method == 'PUT':
                resp = client.put(path, headers=headers, cookies=cookies)
            elif method == 'DELETE':
                resp = client.delete(path, headers=headers, cookies=cookies)
            else:
                continue
            assert resp.status_code == 403 