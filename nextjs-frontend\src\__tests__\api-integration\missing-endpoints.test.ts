/**
 * REAL API Integration Tests - Missing Endpoints
 * Tests for all the API endpoints that were missing from the initial implementation
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'

// Helper functions
async function makeAuthenticatedRequest(
  endpoint: string, 
  options: RequestInit = {}, 
  authToken?: string
): Promise<Response> {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...options.headers,
  }

  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
    credentials: 'include',
  })

  return response
}

async function loginUser(username: string, password: string): Promise<string | null> {
  try {
    const response = await makeAuthenticatedRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password })
    })

    if (response.ok) {
      const data = await response.json()
      return data.access_token || null
    }
    return null
  } catch (error) {
    console.error('Login failed:', error)
    return null
  }
}

const TEST_USERS = {
  admin: { username: 'test_admin', password: 'test_password_123' },
  client: { username: 'test_client', password: 'test_password_123' },
  annotator: { username: 'test_annotator', password: 'test_password_123' }
}

describe('REAL API Integration Tests - Missing Admin Endpoints', () => {
  jest.setTimeout(30000)
  let adminToken: string

  beforeAll(async () => {
    adminToken = await loginUser(TEST_USERS.admin.username, TEST_USERS.admin.password) || ''
    expect(adminToken).toBeTruthy()
  })

  describe('GET /admin/get-datasets', () => {
    it('should fetch datasets for annotation mode', async () => {
      const response = await makeAuthenticatedRequest('/admin/get-datasets?mode=annotation', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('data')
      if (data.data.datasets) {
        expect(Array.isArray(data.data.datasets)).toBe(true)
      }
    })

    it('should fetch datasets for verification mode', async () => {
      const response = await makeAuthenticatedRequest('/admin/get-datasets?mode=verification', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })
  })

  describe('GET /admin/data-delivery', () => {
    it('should fetch data delivery statistics', async () => {
      const response = await makeAuthenticatedRequest('/admin/data-delivery', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('data')
    })

    it('should fetch data delivery for specific dataset', async () => {
      const response = await makeAuthenticatedRequest('/admin/data-delivery?dataset=1', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)
    })
  })

  describe('GET /admin/browse-nas-directory', () => {
    it('should browse NAS root directory', async () => {
      const response = await makeAuthenticatedRequest('/admin/browse-nas-directory', {
        method: 'GET'
      }, adminToken)

      expect([200, 400, 500]).toContain(response.status) // May fail if NAS not connected
    })

    it('should browse specific NAS path', async () => {
      const response = await makeAuthenticatedRequest('/admin/browse-nas-directory?path=/data', {
        method: 'GET'
      }, adminToken)

      expect([200, 400, 404, 500]).toContain(response.status)
    })
  })

  describe('POST /admin/select-dataset', () => {
    it('should select dataset for annotation', async () => {
      const selectRequest = {
        dataset_id: 1,
        mode: 'annotation'
      }

      const response = await makeAuthenticatedRequest('/admin/select-dataset', {
        method: 'POST',
        body: JSON.stringify(selectRequest)
      }, adminToken)

      expect([200, 400, 404]).toContain(response.status)
    })
  })

  describe('POST /admin/connect-nas', () => {
    it('should handle NAS connection request', async () => {
      const nasConfig = {
        host: 'test-nas.local',
        username: 'test_user',
        password: 'test_password',
        port: 21
      }

      const response = await makeAuthenticatedRequest('/admin/connect-nas', {
        method: 'POST',
        body: JSON.stringify(nasConfig)
      }, adminToken)

      expect([200, 400, 500]).toContain(response.status) // May fail if NAS not available
    })
  })

  describe('GET /admin/check-nas-connection', () => {
    it('should check NAS connection status', async () => {
      const response = await makeAuthenticatedRequest('/admin/check-nas-connection', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('connected')
      expect(typeof data.connected).toBe('boolean')
    })
  })

  describe('POST /admin/configure-google-drive', () => {
    it('should handle Google Drive configuration', async () => {
      const driveConfig = {
        credentials: 'test_credentials_json',
        folder_id: 'test_folder_id'
      }

      const response = await makeAuthenticatedRequest('/admin/configure-google-drive', {
        method: 'POST',
        body: JSON.stringify(driveConfig)
      }, adminToken)

      expect([200, 400, 500]).toContain(response.status)
    })
  })

  describe('GET /admin/check-google-drive-connection', () => {
    it('should check Google Drive connection status', async () => {
      const response = await makeAuthenticatedRequest('/admin/check-google-drive-connection', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('connected')
      expect(typeof data.connected).toBe('boolean')
    })
  })

  describe('POST /admin/merge-dataset-json', () => {
    it('should merge dataset JSON files', async () => {
      const mergeRequest = {
        dataset_name: 'test_dataset',
        storage_destination: '/merged_data'
      }

      const response = await makeAuthenticatedRequest('/admin/merge-dataset-json', {
        method: 'POST',
        body: JSON.stringify(mergeRequest)
      }, adminToken)

      expect([200, 400, 404, 500]).toContain(response.status)
    })
  })
})

describe('REAL API Integration Tests - Client Endpoints', () => {
  jest.setTimeout(30000)
  let clientToken: string

  beforeAll(async () => {
    clientToken = await loginUser(TEST_USERS.client.username, TEST_USERS.client.password) || ''
    expect(clientToken).toBeTruthy()
  })

  describe('GET /client/datasets', () => {
    it('should fetch client datasets list', async () => {
      const response = await makeAuthenticatedRequest('/client/datasets', {
        method: 'GET'
      }, clientToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('data')
      expect(data.data).toHaveProperty('datasets')
      expect(Array.isArray(data.data.datasets)).toBe(true)
    })
  })

  describe('GET /client/datasets/{id}', () => {
    it('should fetch specific dataset details', async () => {
      const response = await makeAuthenticatedRequest('/client/datasets/1', {
        method: 'GET'
      }, clientToken)

      expect([200, 404]).toContain(response.status)

      if (response.status === 200) {
        const data = await response.json()
        expect(data).toHaveProperty('success', true)
        expect(data).toHaveProperty('data')
      }
    })

    it('should handle non-existent dataset', async () => {
      const response = await makeAuthenticatedRequest('/client/datasets/99999', {
        method: 'GET'
      }, clientToken)

      expect([404, 200]).toContain(response.status)
    })
  })
})

describe('REAL API Integration Tests - Supervision Endpoints', () => {
  jest.setTimeout(30000)
  let annotatorToken: string

  beforeAll(async () => {
    annotatorToken = await loginUser(TEST_USERS.annotator.username, TEST_USERS.annotator.password) || ''
    expect(annotatorToken).toBeTruthy()
  })

  describe('POST /supervision/upload', () => {
    it('should handle file upload for supervision', async () => {
      const mockFile = new Blob(['mock file content'], { type: 'application/pdf' })
      const formData = new FormData()
      formData.append('file', mockFile, 'test.pdf')
      formData.append('document_type', 'passport')
      formData.append('model_type', 'standard')

      const response = await fetch(`${API_BASE_URL}/supervision/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${annotatorToken}`
        },
        credentials: 'include',
        body: formData
      })

      expect([200, 400, 422]).toContain(response.status)
    })
  })

  describe('GET /supervision/review', () => {
    it('should fetch supervision review data', async () => {
      const response = await makeAuthenticatedRequest('/supervision/review', {
        method: 'GET'
      }, annotatorToken)

      expect([200, 404]).toContain(response.status)

      if (response.status === 200) {
        const data = await response.json()
        expect(data).toHaveProperty('success')
      }
    })
  })

  describe('POST /supervision/list-drive-folders', () => {
    it('should list Google Drive folders', async () => {
      const driveRequest = {
        document_type: 'passport',
        model_type: 'standard',
        folder_id: 'root'
      }

      const response = await makeAuthenticatedRequest('/supervision/list-drive-folders', {
        method: 'POST',
        body: JSON.stringify(driveRequest)
      }, annotatorToken)

      expect([200, 400, 500]).toContain(response.status)
    })
  })

  describe('POST /supervision/download_csv', () => {
    it('should download CSV data', async () => {
      const csvRequest = {
        edited_data: [],
        filename: 'test_export.csv'
      }

      const response = await makeAuthenticatedRequest('/supervision/download_csv', {
        method: 'POST',
        body: JSON.stringify(csvRequest)
      }, annotatorToken)

      expect([200, 400]).toContain(response.status)
    })
  })
})

describe('REAL API Integration Tests - Knowledge Base Endpoints', () => {
  jest.setTimeout(30000)
  let adminToken: string

  beforeAll(async () => {
    adminToken = await loginUser(TEST_USERS.admin.username, TEST_USERS.admin.password) || ''
    expect(adminToken).toBeTruthy()
  })

  describe('GET /knowledge-base/topics', () => {
    it('should fetch knowledge base topics', async () => {
      const response = await makeAuthenticatedRequest('/knowledge-base/topics', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
    })
  })

  describe('GET /knowledge-base/entries', () => {
    it('should fetch knowledge base entries', async () => {
      const response = await makeAuthenticatedRequest('/knowledge-base/entries', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
    })

    it('should fetch entries by topic', async () => {
      const response = await makeAuthenticatedRequest('/knowledge-base/entries?topic=test', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)
    })
  })

  describe('POST /knowledge-base/entries', () => {
    it('should create new knowledge base entry', async () => {
      const newEntry = {
        title: 'Test Knowledge Entry',
        topic: 'testing',
        content: 'This is a test knowledge base entry for API testing.'
      }

      const response = await makeAuthenticatedRequest('/knowledge-base/entries', {
        method: 'POST',
        body: JSON.stringify(newEntry)
      }, adminToken)

      expect([200, 201]).toContain(response.status)

      if (response.status === 201) {
        const data = await response.json()
        expect(data).toHaveProperty('id')
      }
    })
  })

  describe('GET /knowledge-base/entries/{id}', () => {
    it('should fetch specific knowledge base entry', async () => {
      const response = await makeAuthenticatedRequest('/knowledge-base/entries/1', {
        method: 'GET'
      }, adminToken)

      expect([200, 404]).toContain(response.status)
    })
  })
})

describe('REAL API Integration Tests - Additional Missing Endpoints', () => {
  jest.setTimeout(30000)
  let adminToken: string

  beforeAll(async () => {
    adminToken = await loginUser(TEST_USERS.admin.username, TEST_USERS.admin.password) || ''
    expect(adminToken).toBeTruthy()
  })

  describe('GET /synthetic-dataset/knowledge-entries', () => {
    it('should fetch knowledge entries for synthetic data', async () => {
      const response = await makeAuthenticatedRequest('/synthetic-dataset/knowledge-entries', {
        method: 'GET'
      }, adminToken)

      expect([200, 404]).toContain(response.status)
    })

    it('should fetch knowledge entries by topic', async () => {
      const response = await makeAuthenticatedRequest('/synthetic-dataset/knowledge-entries?topic=test', {
        method: 'GET'
      }, adminToken)

      expect([200, 404]).toContain(response.status)
    })
  })

  describe('GET /auditor/image', () => {
    it('should fetch auditor image with path parameter', async () => {
      const imagePath = 'test/image.jpg'
      const response = await makeAuthenticatedRequest(`/auditor/image?path=${encodeURIComponent(imagePath)}`, {
        method: 'GET'
      }, adminToken)

      expect([200, 404, 500]).toContain(response.status)
    })
  })
})
