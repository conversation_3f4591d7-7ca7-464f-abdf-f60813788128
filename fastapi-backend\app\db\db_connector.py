"""
Database connection module for the application.
Provides centralized functions for connecting using SQLAlchemy.
"""
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from db.config import DatabaseSettings, UserSettings
from db.models import Base
from db.models.user import User
import logging
from typing import Generator
from core.security import hash_password
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import scoped_session
from contextlib import contextmanager

# Import all models to ensure they are registered with the declarative base
from db.models.user import Base as UserBase
from db.models.knowledge_base import Base as KnowledgeBase


logger = logging.getLogger('db_connector')

# Database URL
db_settings = DatabaseSettings()
SQLALCHEMY_DATABASE_URL = db_settings.url

# Create engine with SQLite configuration
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},  # Needed for SQLite
    poolclass=StaticPool  # Use static pool for SQLite
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create a scoped session for thread safety
ScopedSession = scoped_session(SessionLocal)

def get_db_connection() -> Generator[Session, None, None]:
    """
    Get a database session. Use this as a dependency in FastAPI endpoints.
    
    Yields:
        Session: SQLAlchemy database session
    
    Example:
        @app.get("/items/")
        def read_items(db: Session = Depends(get_db)):
            return db.query(Item).all()
    """
    db = ScopedSession()
    try:
        yield db
    finally:
        db.close()

@contextmanager
def get_db_session():
    """Context manager for database sessions."""
    db = ScopedSession()
    try:
        yield db
    finally:
        db.close()

def initialize_database():
    """
    Initialize the database by creating all tables defined in the models.
    This should be called during application startup.
    """
    try:
        logger.info("Ensuring database directory exists")
        db_path = db_settings.url.replace("sqlite:///", "")
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        logger.info("Creating database tables from SQLAlchemy models")
        UserBase.metadata.create_all(bind=engine)
        KnowledgeBase.metadata.create_all(bind=engine)
        logger.info("Successfully initialized database with all models")

        # Seed initial user data
        logger.info("Seeding initial user data")
        session = SessionLocal()
        try:
            user_settings = UserSettings()
            for user_conf in user_settings.users.values():
                if not session.query(User).filter(User.username == user_conf.username).first():
                    pwd_hash = hash_password(user_conf.password)
                    email = user_conf.email or f"{user_conf.username}@example.com"
                    new_user = User(
                        username=user_conf.username,
                        full_name=user_conf.full_name,
                        email=email,
                        password_hash=pwd_hash,
                        role=user_conf.role,
                        is_active=user_conf.is_active,
                        annotator_mode=user_conf.annotation_mode
                    )
                    session.add(new_user)
            session.commit()
            logger.info("Initial user data seeded successfully")
        except Exception as e:
            session.rollback()
            logger.error(f"Error seeding initial user data: {e}")
        finally:
            session.close()
        return True
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        return False

# Seed initial data for the knowledge base
def seed_knowledge_base():
    """Seed the knowledge base with initial data."""
    from db.models.knowledge_base import KnowledgeEntry
    
    try:
        with get_db_session() as db:
            # Check if data already exists
            if db.query(KnowledgeEntry).count() > 0:
                logger.info("Knowledge base already contains data, skipping seed")
                return True
            
            # Sample knowledge entries
            entries = [
                KnowledgeEntry(
                    title="Introduction to Machine Learning",
                    topic="Machine Learning",
                    content="""Machine learning is a branch of artificial intelligence (AI) and computer science which focuses on the use of data and algorithms to imitate the way that humans learn, gradually improving its accuracy. Machine learning algorithms build a model based on sample data, known as "training data," in order to make predictions or decisions without being explicitly programmed to do so.

There are three main types of machine learning: supervised learning, unsupervised learning, and reinforcement learning.

Supervised learning involves training a model on a labeled dataset, which means that each training example is paired with an output label. The goal is to learn a mapping from inputs to outputs.

Unsupervised learning involves training a model on an unlabeled dataset. The goal is to learn the inherent structure of the data without using explicitly provided labels.

Reinforcement learning involves training a model to make a sequence of decisions by rewarding desired behaviors and punishing undesired ones. The agent learns to achieve a goal in an uncertain, potentially complex environment.""",
                    source="AI Textbook"
                ),
                KnowledgeEntry(
                    title="The Solar System",
                    topic="Astronomy",
                    content="""The Solar System is the gravitationally bound system of the Sun and the objects that orbit it, either directly or indirectly. Of the objects that orbit the Sun directly, the largest are the eight planets, with the remainder being smaller objects, such as the five dwarf planets and small Solar System bodies.

The four smaller inner planets, Mercury, Venus, Earth and Mars, are terrestrial planets, being primarily composed of rock and metal. The four outer planets are giant planets, being substantially more massive than the terrestrials. The two largest, Jupiter and Saturn, are gas giants, being composed mainly of hydrogen and helium; the two outermost planets, Uranus and Neptune, are ice giants, being composed mostly of substances with relatively high melting points compared with hydrogen and helium, called volatiles, such as water, ammonia and methane. All eight planets have almost circular orbits that lie within a nearly flat disc called the ecliptic.

The Solar System also contains smaller objects. The asteroid belt, which lies between the orbits of Mars and Jupiter, mostly contains objects composed, like the terrestrial planets, of rock and metal. Beyond Neptune's orbit lie the Kuiper belt and scattered disc, which are populations of trans-Neptunian objects composed mostly of ices, and beyond them a newly discovered population of sednoids.""",
                    source="Astronomy Encyclopedia"
                ),
                KnowledgeEntry(
                    title="World War II",
                    topic="History",
                    content="""World War II or the Second World War, often abbreviated as WWII or WW2, was a global war that lasted from 1939 to 1945. It involved the vast majority of the world's countries—including all the great powers—forming two opposing military alliances: the Allies and the Axis. In a state of total war, directly involving more than 100 million personnel from more than 30 countries, the major participants threw their entire economic, industrial, and scientific capabilities behind the war effort, blurring the distinction between civilian and military resources.

World War II was the deadliest conflict in human history, marked by 70 to 85 million fatalities, most of whom were civilians in the Soviet Union and China. It included massacres, genocides (including the Holocaust), strategic bombing, premeditated death from starvation and disease, and the only use of nuclear weapons in war.

Japan, which aimed to dominate Asia and the Pacific, was at war with China by 1937, though neither side had declared war on the other. World War II is generally said to have begun on 1 September 1939, with the invasion of Poland by Germany and subsequent declarations of war on Germany by France and the United Kingdom. From late 1939 to early 1941, in a series of campaigns and treaties, Germany conquered or controlled much of continental Europe, and formed the Axis alliance with Italy and Japan.""",
                    source="History Archives"
                ),
                KnowledgeEntry(
                    title="Introduction to Python Programming",
                    topic="Programming",
                    content="""Python is an interpreted, high-level, general-purpose programming language. Created by Guido van Rossum and first released in 1991, Python's design philosophy emphasizes code readability with its notable use of significant whitespace. Its language constructs and object-oriented approach aim to help programmers write clear, logical code for small and large-scale projects.

Python is dynamically typed and garbage-collected. It supports multiple programming paradigms, including structured (particularly, procedural), object-oriented, and functional programming. Python is often described as a "batteries included" language due to its comprehensive standard library.

Python was conceived in the late 1980s as a successor to the ABC language. Python 2.0, released in 2000, introduced features like list comprehensions and a garbage collection system with reference counting. Python 3.0, released in 2008, was a major revision of the language that is not completely backward-compatible, and much Python 2 code does not run unmodified on Python 3.

Here's a simple example of Python code:

```python
# This is a simple Python program that prints "Hello, World!"
print("Hello, World!")
```

Python features a dynamic type system and automatic memory management. It supports multiple programming paradigms, including object-oriented, imperative, functional and procedural, and has a large and comprehensive standard library.""",
                    source="Programming Handbook"
                ),
                KnowledgeEntry(
                    title="Climate Change and Global Warming",
                    topic="Environment",
                    content="""Climate change refers to significant changes in global temperature, precipitation, wind patterns, and other measures of climate that occur over several decades or longer. Global warming refers to the long-term warming of the planet since the early 20th century, and most notably since the late 1970s, due to the increase in fossil fuel emissions since the Industrial Revolution.

The primary cause of climate change is human activities, particularly the burning of fossil fuels, which adds heat-trapping greenhouse gases to Earth's atmosphere. The evidence for rapid climate change is compelling: global temperature rise, warming oceans, shrinking ice sheets, glacial retreat, decreased snow cover, sea level rise, declining Arctic sea ice, extreme weather events, and ocean acidification.

The Intergovernmental Panel on Climate Change (IPCC), which includes more than 1,300 scientists from the United States and other countries, forecasts a temperature rise of 2.5 to 10 degrees Fahrenheit over the next century. According to the IPCC, the extent of climate change effects on individual regions will vary over time and with the ability of different societal and environmental systems to mitigate or adapt to change.

The effects of climate change include more frequent wildfires, longer periods of drought in some regions and an increase in the number, duration and intensity of tropical storms. Climate change also causes rising sea levels, which can lead to flooding and erosion of coastal and low-lying areas.""",
                    source="Environmental Science Journal"
                )
            ]
            
            # Add entries to the database
            for entry in entries:
                db.add(entry)
            
            db.commit()
            logger.info(f"Successfully seeded knowledge base with {len(entries)} entries")
            return True
    except Exception as e:
        logger.error(f"Error seeding knowledge base: {e}")
        return False