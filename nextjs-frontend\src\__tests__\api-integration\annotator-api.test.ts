/**
 * REAL API Integration Tests - Annotator Endpoints
 * Tests actual API calls to FastAPI backend for annotator functionality
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'

// Helper functions (reused from main test file)
async function makeAuthenticatedRequest(
  endpoint: string, 
  options: RequestInit = {}, 
  authToken?: string
): Promise<Response> {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...options.headers,
  }

  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
    credentials: 'include',
  })

  return response
}

async function loginUser(username: string, password: string): Promise<string | null> {
  try {
    const response = await makeAuthenticatedRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password })
    })

    if (response.ok) {
      const data = await response.json()
      return data.access_token || null
    }
    return null
  } catch (error) {
    console.error('Login failed:', error)
    return null
  }
}

const TEST_ANNOTATOR = {
  username: 'test_annotator',
  password: 'test_password_123'
}

describe('REAL API Integration Tests - Annotator Endpoints', () => {
  jest.setTimeout(30000)
  let annotatorToken: string

  beforeAll(async () => {
    // Login as annotator for all tests
    annotatorToken = await loginUser(TEST_ANNOTATOR.username, TEST_ANNOTATOR.password) || ''
    expect(annotatorToken).toBeTruthy()
  })

  describe('GET /annotator/dashboard', () => {
    it('should fetch annotator dashboard data', async () => {
      const response = await makeAuthenticatedRequest('/annotator/dashboard', {
        method: 'GET'
      }, annotatorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('annotation_mode')
      expect(data).toHaveProperty('instructions')
      expect(['annotation', 'verification', 'supervision']).toContain(data.annotation_mode)
    })
  })

  describe('GET /annotator/annotate', () => {
    it('should fetch annotation batch data', async () => {
      const response = await makeAuthenticatedRequest('/annotator/annotate', {
        method: 'GET'
      }, annotatorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('batch_id')
      expect(data).toHaveProperty('images')
      expect(data).toHaveProperty('labels')
      expect(Array.isArray(data.images)).toBe(true)
    })
  })

  describe('POST /annotator/save-labels', () => {
    it('should save annotation labels successfully', async () => {
      // First get a batch to annotate
      const batchResponse = await makeAuthenticatedRequest('/annotator/annotate', {
        method: 'GET'
      }, annotatorToken)
      
      expect(batchResponse.status).toBe(200)
      const batchData = await batchResponse.json()
      
      // Prepare sample labels
      const sampleLabels = {
        batch_id: batchData.batch_id,
        labels: {
          'image1.jpg': {
            'label': 'test_label',
            'confidence': 0.95,
            'coordinates': { x: 100, y: 100, width: 50, height: 50 }
          }
        }
      }

      const response = await makeAuthenticatedRequest('/annotator/save-labels', {
        method: 'POST',
        body: JSON.stringify(sampleLabels)
      }, annotatorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })
  })

  describe('GET /annotator/verification', () => {
    it('should fetch verification tasks', async () => {
      const response = await makeAuthenticatedRequest('/annotator/verification', {
        method: 'GET'
      }, annotatorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('batch_id')
      expect(data).toHaveProperty('images')
      expect(data).toHaveProperty('existing_labels')
    })
  })

  describe('POST /annotator/submit-verification', () => {
    it('should submit verification results', async () => {
      // First get verification batch
      const verificationResponse = await makeAuthenticatedRequest('/annotator/verification', {
        method: 'GET'
      }, annotatorToken)
      
      expect(verificationResponse.status).toBe(200)
      const verificationData = await verificationResponse.json()
      
      // Submit verification
      const verificationResult = {
        batch_id: verificationData.batch_id,
        verifications: {
          'image1.jpg': {
            'approved': true,
            'comments': 'Label looks correct'
          }
        }
      }

      const response = await makeAuthenticatedRequest('/annotator/submit-verification', {
        method: 'POST',
        body: JSON.stringify(verificationResult)
      }, annotatorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })
  })

  describe('GET /annotator/supervision', () => {
    it('should fetch supervision tasks', async () => {
      const response = await makeAuthenticatedRequest('/annotator/supervision', {
        method: 'GET'
      }, annotatorToken)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('documents')
      expect(Array.isArray(data.documents)).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should handle unauthorized access properly', async () => {
      const response = await makeAuthenticatedRequest('/annotator/dashboard', {
        method: 'GET'
      }, 'invalid_token')

      expect(response.status).toBe(401)
    })

    it('should handle malformed requests', async () => {
      const response = await makeAuthenticatedRequest('/annotator/save-labels', {
        method: 'POST',
        body: 'invalid_json'
      }, annotatorToken)

      expect(response.status).toBe(422) // Validation error
    })
  })
})
