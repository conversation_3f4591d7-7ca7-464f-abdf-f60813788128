/**
 * Frontend Logic Tests - RoleGuard Component
 * Tests for role-based access control and route protection logic
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { useRouter } from 'next/navigation';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock AuthContext
const mockAuthContext = {
  isAuthenticated: false,
  user: null,
  isLoading: false,
  checkAuth: jest.fn(),
  login: jest.fn(),
  logout: jest.fn(),
};

const AuthContext = React.createContext(mockAuthContext);

const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Mock RoleGuard component implementation
interface RoleGuardProps {
  allowedRoles: string[];
  children: React.ReactNode;
  fallbackRoute?: string;
}

const RoleGuard: React.FC<RoleGuardProps> = ({ 
  allowedRoles, 
  children, 
  fallbackRoute = '/' 
}) => {
  const { isAuthenticated, user, isLoading, checkAuth } = useAuth();
  const router = useRouter();
  const [redirecting, setRedirecting] = React.useState(false);
  const [authChecked, setAuthChecked] = React.useState(false);

  // Trigger auth check when RoleGuard mounts
  React.useEffect(() => {
    if (!authChecked && !isLoading) {
      checkAuth().finally(() => setAuthChecked(true));
    }
  }, [checkAuth, authChecked, isLoading]);

  React.useEffect(() => {
    if (authChecked && !isLoading) {
      if (!isAuthenticated) {
        // Not authenticated, redirect to home
        setRedirecting(true);
        router.push('/');
        return;
      }

      if (user && !allowedRoles.includes(user.role)) {
        // Authenticated but wrong role, redirect to appropriate dashboard
        setRedirecting(true);
        const roleRoutes: Record<string, string> = {
          'admin': '/admin',
          'annotator': '/annotator',
          'auditor': '/auditor',
          'client': '/client'
        };
        
        const userRoute = roleRoutes[user.role] || fallbackRoute;
        
        // Show a brief message before redirecting
        setTimeout(() => {
          router.push(userRoute);
        }, 1500);
        return;
      }
    }
  }, [authChecked, isLoading, isAuthenticated, user, allowedRoles, router, fallbackRoute]);

  // Show loading state
  if (isLoading || !authChecked) {
    return (
      <div data-testid="loading-state">
        <div>Loading...</div>
      </div>
    );
  }

  // Show redirecting state
  if (redirecting) {
    if (!isAuthenticated) {
      return (
        <div data-testid="redirect-unauthenticated">
          <div>Please log in to access this page.</div>
        </div>
      );
    }

    if (user && !allowedRoles.includes(user.role)) {
      return (
        <div data-testid="redirect-unauthorized">
          <div>You don't have permission to access this page. Redirecting...</div>
        </div>
      );
    }
  }

  // Show protected content if authenticated and authorized
  if (isAuthenticated && user && allowedRoles.includes(user.role)) {
    return <>{children}</>;
  }

  // Default fallback
  return (
    <div data-testid="access-denied">
      <div>Access denied</div>
    </div>
  );
};

// Test components
const ProtectedContent = () => <div data-testid="protected-content">Protected Content</div>;

const TestWrapper: React.FC<{ 
  authContextValue: any; 
  allowedRoles: string[];
  fallbackRoute?: string;
}> = ({ authContextValue, allowedRoles, fallbackRoute }) => (
  <AuthContext.Provider value={authContextValue}>
    <RoleGuard allowedRoles={allowedRoles} fallbackRoute={fallbackRoute}>
      <ProtectedContent />
    </RoleGuard>
  </AuthContext.Provider>
);

describe('Frontend Logic Tests - RoleGuard Component', () => {
  const mockRouter = {
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Loading States', () => {
    it('should show loading state when auth is loading', () => {
      const authContext = {
        ...mockAuthContext,
        isLoading: true,
        checkAuth: jest.fn().mockResolvedValue(undefined),
      };

      render(
        <TestWrapper 
          authContextValue={authContext} 
          allowedRoles={['admin']} 
        />
      );

      expect(screen.getByTestId('loading-state')).toBeInTheDocument();
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('should show loading state when auth check has not completed', () => {
      const authContext = {
        ...mockAuthContext,
        isLoading: false,
        checkAuth: jest.fn().mockResolvedValue(undefined),
      };

      render(
        <TestWrapper 
          authContextValue={authContext} 
          allowedRoles={['admin']} 
        />
      );

      expect(screen.getByTestId('loading-state')).toBeInTheDocument();
    });
  });

  describe('Authentication Check', () => {
    it('should trigger auth check on mount', async () => {
      const checkAuth = jest.fn().mockResolvedValue(undefined);
      const authContext = {
        ...mockAuthContext,
        isLoading: false,
        checkAuth,
      };

      render(
        <TestWrapper 
          authContextValue={authContext} 
          allowedRoles={['admin']} 
        />
      );

      await waitFor(() => {
        expect(checkAuth).toHaveBeenCalled();
      });
    });

    it('should not trigger auth check if already loading', () => {
      const checkAuth = jest.fn();
      const authContext = {
        ...mockAuthContext,
        isLoading: true,
        checkAuth,
      };

      render(
        <TestWrapper 
          authContextValue={authContext} 
          allowedRoles={['admin']} 
        />
      );

      expect(checkAuth).not.toHaveBeenCalled();
    });
  });

  describe('Unauthenticated Access', () => {
    it('should redirect to home when user is not authenticated', async () => {
      const checkAuth = jest.fn().mockResolvedValue(undefined);
      const authContext = {
        ...mockAuthContext,
        isAuthenticated: false,
        user: null,
        isLoading: false,
        checkAuth,
      };

      render(
        <TestWrapper 
          authContextValue={authContext} 
          allowedRoles={['admin']} 
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('redirect-unauthenticated')).toBeInTheDocument();
        expect(screen.getByText('Please log in to access this page.')).toBeInTheDocument();
        expect(mockRouter.push).toHaveBeenCalledWith('/');
      });
    });
  });

  describe('Authorized Access', () => {
    it('should render protected content when user has correct role', async () => {
      const checkAuth = jest.fn().mockResolvedValue(undefined);
      const authContext = {
        ...mockAuthContext,
        isAuthenticated: true,
        user: { id: 1, username: 'admin', role: 'admin' },
        isLoading: false,
        checkAuth,
      };

      render(
        <TestWrapper 
          authContextValue={authContext} 
          allowedRoles={['admin']} 
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('protected-content')).toBeInTheDocument();
        expect(screen.getByText('Protected Content')).toBeInTheDocument();
      });
    });

    it('should render protected content when user has one of multiple allowed roles', async () => {
      const checkAuth = jest.fn().mockResolvedValue(undefined);
      const authContext = {
        ...mockAuthContext,
        isAuthenticated: true,
        user: { id: 1, username: 'annotator', role: 'annotator' },
        isLoading: false,
        checkAuth,
      };

      render(
        <TestWrapper 
          authContextValue={authContext} 
          allowedRoles={['admin', 'annotator', 'auditor']} 
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      });
    });
  });

  describe('Unauthorized Access', () => {
    it('should redirect admin user to admin dashboard when accessing annotator page', async () => {
      const checkAuth = jest.fn().mockResolvedValue(undefined);
      const authContext = {
        ...mockAuthContext,
        isAuthenticated: true,
        user: { id: 1, username: 'admin', role: 'admin' },
        isLoading: false,
        checkAuth,
      };

      render(
        <TestWrapper 
          authContextValue={authContext} 
          allowedRoles={['annotator']} 
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('redirect-unauthorized')).toBeInTheDocument();
        expect(screen.getByText("You don't have permission to access this page. Redirecting...")).toBeInTheDocument();
      });

      // Fast-forward the timeout
      act(() => {
        jest.advanceTimersByTime(1500);
      });

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/admin');
      });
    });

    it('should redirect annotator user to annotator dashboard when accessing admin page', async () => {
      const checkAuth = jest.fn().mockResolvedValue(undefined);
      const authContext = {
        ...mockAuthContext,
        isAuthenticated: true,
        user: { id: 1, username: 'annotator', role: 'annotator' },
        isLoading: false,
        checkAuth,
      };

      render(
        <TestWrapper 
          authContextValue={authContext} 
          allowedRoles={['admin']} 
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('redirect-unauthorized')).toBeInTheDocument();
      });

      act(() => {
        jest.advanceTimersByTime(1500);
      });

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/annotator');
      });
    });

    it('should redirect to fallback route for unknown role', async () => {
      const checkAuth = jest.fn().mockResolvedValue(undefined);
      const authContext = {
        ...mockAuthContext,
        isAuthenticated: true,
        user: { id: 1, username: 'unknown', role: 'unknown_role' },
        isLoading: false,
        checkAuth,
      };

      render(
        <TestWrapper 
          authContextValue={authContext} 
          allowedRoles={['admin']} 
          fallbackRoute="/custom-fallback"
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('redirect-unauthorized')).toBeInTheDocument();
      });

      act(() => {
        jest.advanceTimersByTime(1500);
      });

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/custom-fallback');
      });
    });

    it('should use default fallback route when no custom fallback provided', async () => {
      const checkAuth = jest.fn().mockResolvedValue(undefined);
      const authContext = {
        ...mockAuthContext,
        isAuthenticated: true,
        user: { id: 1, username: 'unknown', role: 'unknown_role' },
        isLoading: false,
        checkAuth,
      };

      render(
        <TestWrapper 
          authContextValue={authContext} 
          allowedRoles={['admin']} 
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('redirect-unauthorized')).toBeInTheDocument();
      });

      act(() => {
        jest.advanceTimersByTime(1500);
      });

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/');
      });
    });
  });

  describe('Role Route Mapping', () => {
    const roleTestCases = [
      { role: 'admin', expectedRoute: '/admin' },
      { role: 'annotator', expectedRoute: '/annotator' },
      { role: 'auditor', expectedRoute: '/auditor' },
      { role: 'client', expectedRoute: '/client' },
    ];

    roleTestCases.forEach(({ role, expectedRoute }) => {
      it(`should redirect ${role} to ${expectedRoute} when accessing unauthorized page`, async () => {
        const checkAuth = jest.fn().mockResolvedValue(undefined);
        const authContext = {
          ...mockAuthContext,
          isAuthenticated: true,
          user: { id: 1, username: role, role },
          isLoading: false,
          checkAuth,
        };

        render(
          <TestWrapper 
            authContextValue={authContext} 
            allowedRoles={['different_role']} 
          />
        );

        await waitFor(() => {
          expect(screen.getByTestId('redirect-unauthorized')).toBeInTheDocument();
        });

        act(() => {
          jest.advanceTimersByTime(1500);
        });

        await waitFor(() => {
          expect(mockRouter.push).toHaveBeenCalledWith(expectedRoute);
        });
      });
    });
  });
});
