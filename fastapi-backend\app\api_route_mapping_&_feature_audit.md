---

### All Roles

| SOP Feature Description                       | Expected API or Route   | Method | Comments / Notes                                                                    |
| --------------------------------------------- | ----------------------- | ------ | ----------------------------------------------------------------------------------- |
| Navigate to DADP URL                          | N/A                     | N/A    | Browser navigation only                                                             |
| Login (username/password)                     | `/auth/login`           | POST   | OAuth2 form-data: username, password; sets `access_token` & `refresh_token` cookies |
| Self-register (Create New Account → Register) | `/auth/register`        | POST   | JSON body: `{ username, password, confirm_password, role, full_name, email }`       |
| Change Password                               | `/auth/change-password` | POST   | JSON body: `{ current_password, new_password, confirm_password }`                   |
| Logout                                        | `/auth/logout`          | POST   | No body; deletes `access_token` & `refresh_token` cookies                           |
| Refresh Access Token (body)                   | `/auth/refresh-token`   | POST   | JSON body: `{ refresh_token: str }`; returns JSON `{ access_token, token_type }`; 422 if body missing |
| Refresh Access Token (cookie)                 | `/auth/refresh`         | POST   | Reads `refresh_token` cookie; sets new `access_token` & `refresh_token` cookies     |
| Verify Access Token                           | `/auth/verify`          | GET    | Reads `access_token` cookie; returns 200 if valid                                   |
| Get Current User Profile                      | `/auth/me`              | GET    | Reads `access_token` cookie; returns full user profile; 403 if deactivated; 401 if deleted |

---

### Annotator

| SOP Feature Description                   | Expected API or Route                | Method | Comments / Notes                                                                                                                                             |
| ----------------------------------------- | ------------------------------------ | ------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| View Annotator Dashboard                  | `/annotator/dashboard`               | GET    | Returns `{ annotation_mode }`; client displays guidelines & shortcuts                                                                                        |
| Manual Labelling mode (“Let's Label!”)    | `/annotator/annotate`                | GET    | No params; reads mode via dependency; tries Redis `get_cached_user_batch_id` → `get_cached_batch`; on miss, loads & caches batch; prefetches images          |
| Label Verification mode (“Let's Verify!”) | `/annotator/annotate`                | GET    | Same endpoint; mode "verification"; same caching pattern                                                                                                     |
| Next Batch/Image (→ "Next")               | `/annotator/next-set`                | GET    | No params; assigns new batch via `batch_manager`; caches batch & mapping; prefetches images                                                                  |
| Get Image for Annotation                  | `/annotator/image/{image_path:path}` | GET    | Path-param `image_path`; uses `get_image_from_storage` which checks Redis (`annotator_cache.get_cached_image`), falls back to FTP & caches via `cache_image` |
| Save Labels                               | `/annotator/save-labels`             | POST   | JSON: `{ labels: Dict[str,str], verification_mode: bool, batch_name?: str }`; calls `batch_manager.save_batch_labels`, then `delete_user_batch_cache`        |

---

### Auditor

| SOP Feature Description               | Expected API or Route   | Method | Comments / Notes                                                                                                                                        |
| ------------------------------------- | ----------------------- | ------ | ------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Available Audit Modes ("Get Started") | `/auditor/modes`        | GET    | Returns `["annotation","verification"]`                                                                                                                 |
| Select Dataset                        | `/auditor/datasets`     | GET    | Query `mode?: str`; returns list of `{ dataset_name, dataset_image_path }`; Redis helpers present but unused                                            |
| Select Verifier                       | `/auditor/verifiers`    | GET    | Query `dataset_name: str, mode: str`; returns list of usernames                                                                                         |
| Select Batch Files                    | `/auditor/files`        | GET    | Query `dataset_name, verifier_username, mode`; returns `[ "<batch_name>.json" ]`                                                                        |
| Load Tasks                            | `/auditor/tasks`        | GET    | Query `mode, dataset_name, verifier_username, file_name`; returns image paths + labels from NAS & DB                                                    |
| Get Audit Image                       | `/auditor/image`        | GET    | Query `path: str`; proxies NAS image via FTP                                                                                                            |
| Save All Labels (batch)               | `/auditor/save-labels`  | POST   | JSON: `{ mode, dataset_name, verifier_username, file_name, tasks, comments? }`; saves JSON to NAS, updates DB via `auditor_service.update_audit_record` |
| Update Single Audit Record            | `/auditor/audit-record` | POST   | JSON: `{ mode, record_id, auditor_username, audit_status, comments? }`; returns 204 on success                                                          |
| Review History ("View History")       | `/auditor/history`      | GET    | Query `auditor_username`; returns past audit entries                                                                                                    |

---

### Supervisor

| SOP Feature Description              | Expected API or Route                    | Method | Comments / Notes                                                                                                                                                                       |
| ------------------------------------ | ---------------------------------------- | ------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Upload Local Files (≤10)             | `/supervision/upload`                    | POST   | Form-data: `document_type, model_type?`, `files: UploadFile[]`; enqueues tasks; caches metadata at `supervision:{image_id}:meta` and image data at `supervision:{image_id}:image_data` |
| Process Selected Drive Files         | `/supervision/upload`                    | POST   | Same endpoint; detects `drive_file_ids` & `drive_file_info`; enqueues tasks; caches metadata & image data in Redis                                                                     |
| Load Drive Folders                   | `/supervision/list-drive-folders`        | POST   | JSON: `{ folder_id?: string }`; returns Drive folders/files from Google Drive API                                                                                                      |
| Review Documents                     | `/supervision/review`                    | GET    | No params; returns `results_dict` statuses & parsed fields                                                                                                                             |
| Check Processing Status              | `/supervision/check-status/{image_id}`   | GET    | Path-param `image_id`; checks status in `results_dict`                                                                                                                                 |
| View Document Image for Review       | `/supervision/document-image/{image_id}` | GET    | Path-param `image_id`; uses Redis `cache_get("supervision:{id}:image_data")`, falls back to in-memory or file                                                                          |
| Download CSV Result                  | `/supervision/download_csv/{image_id}`   | GET    | Path-param `image_id`; streams CSV via `export_download_csv`                                                                                                                           |
| Download TXT Result                  | `/supervision/download_txt/{image_id}`   | GET    | Path-param `image_id`; streams TXT                                                                                                                                                     |
| Save Extracted Data to Google Sheets | `/supervision/save-document`             | POST   | JSON: `{ image_id, data, include_all_extracted_text?, verified?, corrections? }`; calls `save_document_data` in Google Sheets                                                          |
| Bulk-export All Results as CSV       | `/supervision/download_csv`              | POST   | JSON: `{ edited_data, filename? }`; streams combined CSV                                                                                                                               |

---

### Extractor (Note-OCR)

| SOP Feature Description | Expected API or Route              | Method | Comments / Notes                                                  |
| ----------------------- | ---------------------------------- | ------ | ----------------------------------------------------------------- |
| Service Readiness Check | `/NoteOCR/images/extractor-mode`   | GET    | Public (no auth); returns `{ success: true, message: … }`         |
| Upload PDF Document     | `/NoteOCR/documents/`              | POST   | multipart form-data: `file: UploadFile` (.pdf)                    |
| List Documents          | `/NoteOCR/documents/`              | GET    | No params; returns `List[DocumentSummary]`                        |
| Get Document by ID      | `/NoteOCR/documents/{document_id}` | GET    | Path-param `document_id: int`                                     |
| Delete Document         | `/NoteOCR/documents/{document_id}` | DELETE | Path-param `document_id: int`                                     |
| Upload Image            | `/NoteOCR/images/upload`           | POST   | multipart form-data: `image: UploadFile`; public                  |
| Standard OCR Extraction | `/NoteOCR/images/extract-ocr`      | POST   | JSON: `{ image_id: str }`; returns extraction                     |
| Custom-Prompt OCR       | `/NoteOCR/images/extract-ocr`      | POST   | JSON: `{ image_id, prompt? }`; same endpoint                      |
| Chat with Image         | `/NoteOCR/images/chat`             | POST   | JSON: `{ image_id, query, chat_history? }`; returns chat response |

---

### Admin

| SOP Feature Description                    | Expected API or Route                  | Method | Comments / Notes                                                                                                 |
| ------------------------------------------ | -------------------------------------- | ------ | ---------------------------------------------------------------------------------------------------------------- |
| View Admin Dashboard                       | `/admin/dashboard`                     | GET    | No params; returns global config & NAS/Drive status                                                              |
| Connect NAS (FTP)                          | `/admin/connect-nas`                   | POST   | JSON: `{ nas_type, nas_url, nas_username, nas_password, client_id?, redirect_after? }`; stores creds             |
| Disconnect NAS                             | `/admin/disconnect-nas`                | POST   | No body; clears .env and Redis `nas_credentials`                                                                 |
| Check NAS Connection                       | `/admin/check-nas-connection`          | GET    | Reads from Redis `nas_credentials` first, then .env; returns connectivity status                                 |
| OCR Directory Settings                     | `/admin/ocr-directory`                 | GET    | No params; returns OCR directory config & stats from DB                                                          |
| Browse NAS Directory (flat)                | `/admin/browse-nas-directory`          | GET    | Query `path?` (default `/`); returns directory listing; caches via `admin_cache.cache_directory_listing(path)`   |
| Browse NAS Directory (paginated)           | `/admin/browser/{folder:path}`         | GET    | Path-param `folder`, Query `page?`; returns paginated listing; same Redis cache key as above                     |
| Select Annotation Folder                   | `/admin/select-annotation-folder`      | POST   | JSON: `{ folder_path }`; creates annotation batches                                                              |
| Select Verification Folders                | `/admin/select-verification-folders`   | POST   | JSON: `{ image_folder, label_file }`; creates verification batches                                               |
| Select Dataset for Annotation/Verification | `/admin/select-dataset`                | POST   | JSON: `{ dataset_id, mode }`; updates AdminSettings; calls `invalidate_stats_cache("ocr_directory")`             |
| Get Available Datasets for Admin UI        | `/admin/get-datasets`                  | GET    | Query `mode`; returns list of datasets with progress                                                             |
| Data Delivery Statistics                   | `/admin/data-delivery`                 | GET    | No params; returns auditor completion stats                                                                      |
| Merge Verified Dataset JSON into Final     | `/admin/merge-dataset-json`            | POST   | JSON: `{ dataset_name, storage_destination? }`; reads & writes JSON via FTP                                      |
| View/Edit Per-Dataset Instructions (GET)   | `/admin/edit-instructions`             | GET    | Query `mode?`, `dataset?`; returns DB instructions                                                               |
| View/Edit Per-Dataset Instructions (POST)  | `/admin/edit-instructions`             | POST   | JSON: `{ mode, instructions, dataset? }`; updates DB                                                             |
| Stream Any Admin Image from NAS            | `/admin/image/{image_path:path}`       | GET    | Path-param `image_path`; proxies image via FTP                                                                   |
| List Users                                 | `/admin/users`                         | GET    | No params; caches with `admin_cache.cache_user_list()`                                                           |
| Get User Details                           | `/admin/users/{username}`              | GET    | Path-param `username`; caches with `admin_cache.cache_user_details(username)`                                    |
| Add New User                               | `/admin/add-user`                      | POST   | JSON: `{ username, password, full_name, email, role, is_active?, annotator_mode? }`; invalidates user-list cache |
| Update User                                | `/admin/users/{username}`              | PUT    | Path-param `username`; JSON: user fields; invalidates caches for that user                                       |
| Suspend / Reactivate User                  | `/admin/users/{username}/suspend`      | POST   | JSON: `{ action }`; invalidates caches for that user                                                             |
| Flush DB & Cache                           | `/admin/flush-db`                      | POST   | No body; clears DB tables and calls `redis_client.flushdb()`                                                     |
| Configure Google Drive OAuth               | `/admin/configure-google-drive`        | POST   | JSON: `{ client_id, client_secret, folder_id? }`                                                                 |
| Check Google Drive Connection              | `/admin/check-google-drive-connection` | GET    | No params; returns Drive OAuth status                                                                            |
| Reset Google Drive Configuration           | `/admin/reset-google-drive`            | POST   | No body; clears Drive tokens                                                                                     |

---

### Client

| SOP Feature Description                    | Expected API or Route           | Method | Comments / Notes                                   |
| ------------------------------------------ | ------------------------------- | ------ | -------------------------------------------------- |
| List Datasets Assigned to Client           | `/client/datasets`              | GET    | No params; returns assigned datasets               |
| Get Details & Metrics for a Client Dataset | `/client/datasets/{dataset_id}` | GET    | Path-param `dataset_id`; returns progress & counts |

---

### Synthetic Dataset

| SOP Feature Description                         | Expected API or Route                  | Method | Comments / Notes                         |
| ----------------------------------------------- | -------------------------------------- | ------ | ---------------------------------------- |
| List Available Generation Models                | `/synthetic-dataset/models`            | GET    | Returns a static list of `ModelInfo`     |
| List Synthetic Dataset Types                    | `/synthetic-dataset/dataset-types`     | GET    | Returns map of dataset types             |
| Generate Synthetic Dataset (with KB reference)  | `/synthetic-dataset/generate`          | POST   | JSON: `ReferenceDataRequest`; admin-only |
| Generate Synthetic Dataset (no reference)       | `/synthetic-dataset/generate-nonref`   | POST   | JSON: `SyntheticDataRequest`; admin-only |
| List Knowledge Entries for Synthetic Generation | `/synthetic-dataset/knowledge-entries` | GET    | Query `topic?`; returns list of entries  |

---

### Knowledge Base

| SOP Feature Description     | Expected API or Route                | Method | Comments / Notes                              |
| --------------------------- | ------------------------------------ | ------ | --------------------------------------------- |
| List All KB Entries         | `/knowledge-base/entries`            | GET    | Query `topic?`, `skip?`, `limit?`; admin-only |
| List All KB Topics          | `/knowledge-base/topics`             | GET    | admin-only                                    |
| Get One KB Entry by ID      | `/knowledge-base/entries/{entry_id}` | GET    | Path-param `entry_id`; admin-only             |
| Create a New KB Entry       | `/knowledge-base/entries`            | POST   | JSON: `KnowledgeEntryCreate`; admin-only      |
| Update an Existing KB Entry | `/knowledge-base/entries/{entry_id}` | PUT    | JSON: `KnowledgeEntryUpdate`; admin-only      |
| Delete a KB Entry           | `/knowledge-base/entries/{entry_id}` | DELETE | admin-only                                    |

---

### Telegram Admin

| SOP Feature Description                | Expected API or Route            | Method | Comments / Notes                                                                           |
| -------------------------------------- | -------------------------------- | ------ | ------------------------------------------------------------------------------------------ |
| Connect to Telegram API                | `/telegram/connect`              | POST   | JSON: `{ api_id, api_hash, phone? }`; sets `tg_session` cookie                             |
| Submit Telegram Verification Code      | `/telegram/verify-code`          | POST   | JSON: `{ code }`; may update `tg_session`                                                  |
| Submit Telegram 2FA Password           | `/telegram/verify-password`      | POST   | JSON: `{ password }`; may update `tg_session`                                              |
| Check Telegram Session Auth            | `/telegram/check-auth`           | GET    | Query `api_id?`, `api_hash?`, `phone?`, `refresh?`; Cookie `tg_session`; uses Redis caches |
| List Telegram Channels                 | `/telegram/channels`             | GET    | Query `refresh?`; Cookie `tg_session`; uses Redis cache                                    |
| Disconnect Telegram Session            | `/telegram/disconnect`           | POST   | No body; Cookie `tg_session`; invalidates auth cache                                       |
| List Images in Channel                 | `/telegram/images`               | GET    | Query `channel_id, date?, refresh?, limit?`; Cookie `tg_session`; Redis cache              |
| List Available Dates for Channel       | `/telegram/dates`                | GET    | Query `channel_id`; Cookie `tg_session`; Redis cache                                       |
| Download Single Telegram Image         | `/telegram/download-image`       | POST   | JSON: `{ message_id, channel_id, filename? }`; Cookie `tg_session`                         |
| Download Multiple Telegram Images      | `/telegram/download-multiple`    | POST   | JSON: `{ images: [ { id, channel_id } ] }`; Cookie `tg_session`                            |
| Get Channel Analytics                  | `/telegram/analytics`            | GET    | Query `channel_id, days?, refresh?`; Cookie `tg_session`; Redis cache                      |
| Upload Telegram Images to Google Drive | `/telegram/upload-to-drive`      | POST   | JSON: `{ images?, channel_name? }`; Cookie `tg_session`; uploads via cached data           |
| Export Telegram Analytics as CSV       | `/telegram/export-analytics-csv` | GET    | No params; streams CSV; Cookie `tg_session`                                                |

---


