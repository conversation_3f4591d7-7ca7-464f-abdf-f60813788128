import pytest
from main import app
from fastapi.testclient import Test<PERSON>lient
from db import db_connector
from db.models.datasets import Datasets

@pytest.fixture(scope='function')
def client_client(db_session):
    with TestClient(app) as client:
        payload = {"username": "clientuser", "password": "Clientpass123!", "confirm_password": "Clientpass123!", "role": "client", "full_name": "Client User", "email": "<EMAIL>"}
        reg_resp = client.post('/api/auth/register', json=payload)
        assert reg_resp.status_code == 200, f"Registration failed: {reg_resp.text}"
        login_resp = client.post('/api/auth/login', data={"username": "clientuser", "password": "Clientpass123!"})
        assert login_resp.status_code == 200, f"Login failed: {login_resp.text}"
        yield client

def test_list_datasets(client_client):
    client = client_client
    resp = client.get('/api/client/datasets')
    assert resp.status_code == 200
    assert resp.json()["success"]
    # DB assertion
    db = db_connector.SessionLocal()
    datasets = db.query(Datasets).all()
    assert isinstance(datasets, list)
    db.close()

def test_dataset_details(client_client):
    client = client_client
    # Try a not-found case
    resp = client.get('/api/client/datasets/9999')
    assert resp.status_code in (200, 404)
    # Boundary: min dataset_id
    resp = client.get('/api/client/datasets/1')
    assert resp.status_code in (200, 404)
    # Boundary: max dataset_id (simulate large int)
    resp = client.get(f'/api/client/datasets/{2**31-1}')
    assert resp.status_code in (200, 404)

def test_client_rbac_for_all_endpoints():
    """Client endpoints should require client role: unauthenticated->401, wrong-role->403"""
    client_no_auth = TestClient(app)
    endpoints = [
        ('get', '/api/client/datasets', {}),
        ('get', '/api/client/datasets/1', {})
    ]
    # Unauthenticated
    for method, path, kwargs in endpoints:
        resp = getattr(client_no_auth, method)(path, **kwargs)
        assert resp.status_code == 401, f"Unauth {path} returned {resp.status_code}"
    # Wrong role: annotator client
    with TestClient(app) as wrong_client:
        payload = {"username": "notclient", "password": "Notclient123!", "confirm_password": "Notclient123!", "role": "annotator", "full_name": "Not Client", "email": "<EMAIL>"}
        wrong_client.post('/api/auth/register', json=payload)
        login = wrong_client.post('/api/auth/login', data={"username": payload["username"], "password": payload["password"]})
        assert login.status_code == 200
        cookies = login.cookies
        for method, path, kwargs in endpoints:
            resp = getattr(wrong_client, method)(path, cookies=cookies, **kwargs)
            assert resp.status_code == 403, f"Forbidden {path} returned {resp.status_code}" 