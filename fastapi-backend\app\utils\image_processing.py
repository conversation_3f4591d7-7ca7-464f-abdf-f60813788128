import os
import re
from core.config import ITEMS_PER_PAGE, ALLOWED_IMAGE_EXTENSIONS
from core.nas_connector import get_ftp_connector
from fastapi import HTTPException
from fastapi.responses import StreamingResponse
import io
import mimetypes
from datetime import datetime
from cache.annotator_cache import get_cached_image, cache_image, is_image_cached, normalize_image_path

def natural_sort_key(item):
    """Natural sort key for sorting image names (works with strings or dicts with 'name' key)"""
    name = item.get('name', '') if isinstance(item, dict) else str(item)
    return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', name)]

async def get_images_from_folder(folder_path, page=1, items_per_page=ITEMS_PER_PAGE, recursive=True):
    """Get images from a folder with pagination, optionally exploring subfolders recursively

    Args:
        folder_path: Path to the folder to explore
        page: Page number for pagination
        items_per_page: Number of items per page
        recursive: Whether to explore subdirectories recursively

    Returns:
        tuple: (paginated_images, total_images)
    """
    try:
        if not folder_path:
            return [], 0

        connector = await get_ftp_connector()
        if not connector:
            return [], 0

        folder_path = folder_path.strip()
        folder_path = '/' + folder_path.lstrip('/')
        

        items = await connector.list_directory(folder_path)

        if isinstance(items, dict) and 'data' in items:
            items = items.get('data', {}).get('files', [])

        directories, files = [], []
        for item in items:
            if not isinstance(item, dict) or 'name' not in item:
                continue

            if 'path' not in item:
                item['path'] = os.path.join(folder_path, item['name']).replace('\\', '/')

            item_type = item.get('type', '').lower()
            if item_type in ('directory', 'dir', 'folder'):
                directories.append(item)
            elif item_type == 'file':
                files.append(item)

        image_files = [f for f in files if os.path.splitext(f['name'])[1].lower() in ALLOWED_IMAGE_EXTENSIONS]

        if recursive and directories:
            for directory in directories:
                subdir_images, _ = await get_images_from_folder(
                    directory['path'], page=1, items_per_page=50000, recursive=True
                )
                image_files.extend(subdir_images)

        image_files.sort(key=natural_sort_key)
        total_images = len(image_files)
        start_idx = (page - 1) * items_per_page
        paginated_images = image_files[start_idx:start_idx + items_per_page]

        return paginated_images, total_images

    except Exception as e:
        return [], 0
    
async def get_image_from_storage(image_path: str, include_response_time: bool = False):
    """Get an image from storage (cache or FTP)"""
    start_time = datetime.now()

    # Use the same normalization as cache module
    normalized_path = normalize_image_path(image_path)  
    # Check cache first
    if await is_image_cached(normalized_path):
        content = await get_cached_image(normalized_path)
        if content:
            content_type = mimetypes.guess_type(normalized_path)[0] or 'application/octet-stream'
            headers = {
                'Cache-Control': 'max-age=3600',
                'X-Source': 'Redis-Cache'
            }
            if include_response_time:
                headers['X-Response-Time'] = f"{(datetime.now() - start_time).total_seconds():.3f}s"
            
            return StreamingResponse(
                io.BytesIO(content),
                media_type=content_type,
                headers=headers
            )

    ftp_connector = await get_ftp_connector()
    if not ftp_connector:
        raise HTTPException(status_code=503, detail="Storage service temporarily unavailable")

    content = await ftp_connector.get_file_content(image_path)
    if content:
        await cache_image(normalized_path, content)
        content_type = mimetypes.guess_type(normalized_path)[0] or 'application/octet-stream'
        headers = {
            'Cache-Control': 'max-age=3600',
            'X-Source': 'FTP',
        }
        if include_response_time:
            headers['X-Response-Time'] = f"{(datetime.now() - start_time).total_seconds():.3f}s"
        
        return StreamingResponse(
            io.BytesIO(content),
            media_type=content_type,
            headers=headers
        )

    raise HTTPException(status_code=404, detail="Image not found")
