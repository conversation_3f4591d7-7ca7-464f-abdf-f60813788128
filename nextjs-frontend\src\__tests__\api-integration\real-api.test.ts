/**
 * REAL API Integration Tests for DADP Frontend
 * These tests make actual HTTP calls to the FastAPI backend
 * 
 * Prerequisites:
 * 1. FastAPI backend must be running on http://localhost:5000
 * 2. Database must be accessible and properly configured
 * 3. Test user accounts should exist or be created
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'

// Test configuration
const TEST_CONFIG = {
  timeout: 30000, // 30 seconds timeout for API calls
  retries: 3,
  backend_url: API_BASE_URL,
}

// Test user credentials (should be configured for your test environment)
const TEST_USERS = {
  admin: {
    username: 'test_admin',
    password: 'test_password_123',
    email: '<EMAIL>',
    full_name: 'Test Admin User',
    role: 'admin'
  },
  annotator: {
    username: 'test_annotator', 
    password: 'test_password_123',
    email: '<EMAIL>',
    full_name: 'Test Annotator User',
    role: 'annotator'
  },
  auditor: {
    username: 'test_auditor',
    password: 'test_password_123', 
    email: '<EMAIL>',
    full_name: 'Test Auditor User',
    role: 'auditor'
  }
}

// Helper function to make authenticated requests
async function makeAuthenticatedRequest(
  endpoint: string, 
  options: RequestInit = {}, 
  authToken?: string
): Promise<Response> {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...options.headers,
  }

  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
    credentials: 'include', // Important for cookie-based auth
  })

  return response
}

// Helper function to login and get auth token
async function loginUser(username: string, password: string): Promise<string | null> {
  try {
    const response = await makeAuthenticatedRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password })
    })

    if (response.ok) {
      const data = await response.json()
      return data.access_token || null
    }
    return null
  } catch (error) {
    console.error('Login failed:', error)
    return null
  }
}

describe('REAL API Integration Tests - Authentication Endpoints', () => {
  jest.setTimeout(TEST_CONFIG.timeout)

  beforeAll(async () => {
    // Check if backend is running
    try {
      const response = await fetch(`${API_BASE_URL}/health`, { method: 'GET' })
      if (!response.ok) {
        throw new Error(`Backend not accessible: ${response.status}`)
      }
    } catch (error) {
      console.error('⚠️  Backend is not running. Please start FastAPI backend on http://localhost:5000')
      throw new Error('Backend not accessible - tests cannot run')
    }
  })

  describe('POST /auth/login', () => {
    it('should successfully login with valid credentials', async () => {
      const response = await makeAuthenticatedRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          username: TEST_USERS.admin.username,
          password: TEST_USERS.admin.password
        })
      })

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('access_token')
      expect(data).toHaveProperty('user')
      expect(data.user.username).toBe(TEST_USERS.admin.username)
    })

    it('should reject invalid credentials', async () => {
      const response = await makeAuthenticatedRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          username: 'invalid_user',
          password: 'wrong_password'
        })
      })

      expect(response.status).toBe(401)
    })

    it('should reject malformed login requests', async () => {
      const response = await makeAuthenticatedRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          username: '', // Empty username
          password: ''  // Empty password
        })
      })

      expect(response.status).toBe(422) // Validation error
    })
  })

  describe('POST /auth/register', () => {
    it('should register a new user successfully', async () => {
      const newUser = {
        username: `test_user_${Date.now()}`, // Unique username
        email: `test_${Date.now()}@example.com`,
        full_name: 'Test User Registration',
        password: 'secure_password_123'
      }

      const response = await makeAuthenticatedRequest('/auth/register', {
        method: 'POST',
        body: JSON.stringify(newUser)
      })

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('user')
      expect(data.user.username).toBe(newUser.username)
    })

    it('should reject duplicate username registration', async () => {
      const duplicateUser = {
        username: TEST_USERS.admin.username, // Existing username
        email: '<EMAIL>',
        full_name: 'Duplicate User',
        password: 'password123'
      }

      const response = await makeAuthenticatedRequest('/auth/register', {
        method: 'POST',
        body: JSON.stringify(duplicateUser)
      })

      expect(response.status).toBe(400) // Bad request for duplicate
    })
  })

  describe('GET /auth/verify', () => {
    it('should verify valid authentication token', async () => {
      // First login to get token
      const token = await loginUser(TEST_USERS.admin.username, TEST_USERS.admin.password)
      expect(token).toBeTruthy()

      // Then verify the token
      const response = await makeAuthenticatedRequest('/auth/verify', {
        method: 'GET'
      }, token!)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('authenticated', true)
      expect(data).toHaveProperty('user')
    })

    it('should reject invalid authentication token', async () => {
      const response = await makeAuthenticatedRequest('/auth/verify', {
        method: 'GET'
      }, 'invalid_token_123')

      expect(response.status).toBe(401)
    })
  })

  describe('POST /auth/logout', () => {
    it('should successfully logout authenticated user', async () => {
      // First login
      const token = await loginUser(TEST_USERS.admin.username, TEST_USERS.admin.password)
      expect(token).toBeTruthy()

      // Then logout
      const response = await makeAuthenticatedRequest('/auth/logout', {
        method: 'POST'
      }, token!)

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })
  })
})

describe('REAL API Integration Tests - Admin Endpoints', () => {
  jest.setTimeout(TEST_CONFIG.timeout)
  let adminToken: string

  beforeAll(async () => {
    // Login as admin for all admin tests
    adminToken = await loginUser(TEST_USERS.admin.username, TEST_USERS.admin.password) || ''
    expect(adminToken).toBeTruthy()
  })

  describe('GET /admin/dashboard', () => {
    it('should fetch admin dashboard data', async () => {
      const response = await makeAuthenticatedRequest('/admin/dashboard', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('data')
      expect(data.data).toHaveProperty('config')
    })
  })

  describe('GET /admin/users', () => {
    it('should fetch all users list', async () => {
      const response = await makeAuthenticatedRequest('/admin/users', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
      if (data.length > 0) {
        expect(data[0]).toHaveProperty('id')
        expect(data[0]).toHaveProperty('username')
        expect(data[0]).toHaveProperty('role')
      }
    })
  })

  describe('GET /admin/nas-status', () => {
    it('should check NAS connection status', async () => {
      const response = await makeAuthenticatedRequest('/admin/nas-status', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('connected')
      expect(typeof data.connected).toBe('boolean')
    })
  })

  describe('GET /admin/drive-status', () => {
    it('should check Google Drive connection status', async () => {
      const response = await makeAuthenticatedRequest('/admin/drive-status', {
        method: 'GET'
      }, adminToken)

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('connected')
      expect(typeof data.connected).toBe('boolean')
    })
  })
})
